package com.github.b4ndithelps.l2tensura.handler;

import com.github.b4ndithelps.l2tensura.config.EPConfig;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import dev.xkmc.l2hostility.content.capability.mob.MobTraitCap;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.EntityJoinLevelEvent;
import net.minecraftforge.event.entity.living.LivingEvent;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.util.HashMap;
import java.util.Map;

/**
 * L2Hostility EP增强处理器
 * 在生物生成时根据L2等级设置增强的EP值
 */
@Mod.EventBusSubscriber(modid = "l2tensura", bus = Mod.EventBusSubscriber.Bus.FORGE)
public class SimpleEPHandler {

    // 存储需要延迟处理的生物和其EP值
    private static final Map<LivingEntity, Double> pendingEPUpdates = new HashMap<>();

    // 存储已经处理过的生物，避免重复处理
    private static final Map<LivingEntity, Boolean> processedEntities = new HashMap<>();

    // 存储生物的原始状态，用于验证和恢复
    private static final Map<LivingEntity, EntityState> originalStates = new HashMap<>();

    // 生物状态记录类
    private static class EntityState {
        final float originalHealth;
        final float originalMaxHealth;
        final boolean wasAlive;

        EntityState(LivingEntity entity) {
            this.originalHealth = entity.getHealth();
            this.originalMaxHealth = entity.getMaxHealth();
            this.wasAlive = entity.isAlive();
        }
    }

    @SubscribeEvent(priority = EventPriority.LOWEST)
    public static void onEntityJoinLevel(EntityJoinLevelEvent event) {
        // 确保在服务器端执行
        if (event.getLevel().isClientSide()) {
            return;
        }

        // 只处理生物实体
        if (!(event.getEntity() instanceof LivingEntity livingEntity)) {
            return;
        }

        // 跳过玩家
        if (livingEntity instanceof Player) {
            return;
        }
        
        try {
            // 检查生物是否有L2Hostility等级
            if (MobTraitCap.HOLDER.isProper(livingEntity)) {
                MobTraitCap mobCap = MobTraitCap.HOLDER.get(livingEntity);
                int hostilityLevel = mobCap.getLevel();

                if (hostilityLevel > 0) {
                    // 检查是否已经被我们的系统处理过（使用NBT标记）
                    if (isAlreadyProcessedByUs(livingEntity)) {
                        if (EPConfig.isDebugLoggingEnabled()) {
                            System.out.println(String.format(
                                "[L2Tensura] %s already processed by L2Tensura, skipping",
                                livingEntity.getType().getDescriptionId()
                            ));
                        }
                        return;
                    }

                    // 记录生物的原始状态
                    originalStates.put(livingEntity, new EntityState(livingEntity));

                    // 使用配置计算EP增强倍率
                    double epMultiplier = EPConfig.calculateEPMultiplier(hostilityLevel);

                    // 获取生物的基础EP值
                    double baseEP = TensuraEPCapability.getEP(livingEntity);
                    if (baseEP <= 0) {
                        // 如果生物没有EP，使用配置的计算方法
                        baseEP = EPConfig.calculateBaseEP(livingEntity.getMaxHealth());
                    }

                    // 计算增强后的EP值
                    double enhancedEP = baseEP * epMultiplier;

                    // 将生物添加到待处理列表，延迟设置EP
                    pendingEPUpdates.put(livingEntity, enhancedEP);

                    // EP更新已安排，不输出调试信息
                }
            }
            
        } catch (Exception e) {
            System.err.println("[L2Tensura] Error setting EP for entity: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 在生物第一次tick时立即设置正确的EP
     */
    @SubscribeEvent(priority = EventPriority.LOWEST)
    public static void onLivingTick(LivingEvent.LivingTickEvent event) {
        LivingEntity livingEntity = event.getEntity();

        // 确保在服务器端执行
        if (livingEntity.level.isClientSide) {
            return;
        }

        // 跳过玩家
        if (livingEntity instanceof Player) {
            return;
        }

        // 检查是否已经处理过这个生物
        if (processedEntities.containsKey(livingEntity)) {
            return;
        }

        try {
            // 检查生物是否有L2Hostility等级
            if (MobTraitCap.HOLDER.isProper(livingEntity)) {
                MobTraitCap mobCap = MobTraitCap.HOLDER.get(livingEntity);
                int hostilityLevel = mobCap.getLevel();

                if (hostilityLevel > 0) {
                    // 获取当前EP
                    double currentEP = TensuraEPCapability.getEP(livingEntity);

                    // 如果EP大于0，说明Tensura已经设置了EP，我们需要重新计算并设置正确的EP
                    if (currentEP > 0) {
                        // 检查是否已经被我们的系统处理过（使用NBT标记）
                        if (isAlreadyProcessedByUs(livingEntity)) {
                            // 标记为已处理（内存中）
                            processedEntities.put(livingEntity, true);
                            if (EPConfig.isDebugLoggingEnabled()) {
                                System.out.println(String.format(
                                    "[L2Tensura] %s already processed by L2Tensura in LivingTick, skipping",
                                    livingEntity.getType().getDescriptionId()
                                ));
                            }
                            return;
                        }

                        // 验证生物状态是否正常
                        if (!isEntityStateValid(livingEntity)) {
                            // 如果状态异常，跳过处理
                            processedEntities.put(livingEntity, true);
                            return;
                        }

                        // 计算基础EP（假设当前EP是基础值）
                        double baseEP = currentEP;

                        // 使用配置计算EP增强倍率
                        double epMultiplier = EPConfig.calculateEPMultiplier(hostilityLevel);

                        // 计算最终EP值
                        double finalEP = baseEP * epMultiplier;

                        // 立即设置最终EP值（保留原有的立即设置功能）
                        setEPInstantly(livingEntity, finalEP);

                        // 智能恢复生命值（避免异常高血量）
                        restoreHealthSafely(livingEntity);

                        // 在NBT中标记为已处理
                        markAsProcessedByUs(livingEntity);

                        // 标记为已处理
                        processedEntities.put(livingEntity, true);
                    }
                }
            }

        } catch (Exception e) {
            System.err.println("[L2Tensura] Error in LivingTick EP enhancement: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理延迟的EP更新
     */
    @SubscribeEvent
    public static void onServerTick(TickEvent.ServerTickEvent event) {
        if (event.phase != TickEvent.Phase.END || pendingEPUpdates.isEmpty()) {
            return;
        }

        // 清理已死亡生物的记录
        cleanupDeadEntities();

        // 处理所有待更新的EP
        pendingEPUpdates.entrySet().removeIf(entry -> {
            LivingEntity entity = entry.getKey();
            Double enhancedEP = entry.getValue();

            try {
                // 检查生物是否仍然有效
                if (entity.isRemoved() || !entity.isAlive() || !isEntityStateValid(entity)) {
                    return true; // 移除无效的生物
                }

                // 检查是否已经被我们的系统处理过
                if (isAlreadyProcessedByUs(entity)) {
                    if (EPConfig.isDebugLoggingEnabled()) {
                        System.out.println(String.format(
                            "[L2Tensura] %s already processed by L2Tensura in ServerTick, skipping",
                            entity.getType().getDescriptionId()
                        ));
                    }
                    return true; // 移除已处理的生物
                }

                // 立即设置EP（保留原有的立即设置功能）
                setEPInstantly(entity, enhancedEP);

                // 智能恢复生命值
                restoreHealthSafely(entity);

                // 在NBT中标记为已处理
                markAsProcessedByUs(entity);

                return true; // 移除已处理的生物

            } catch (Exception e) {
                System.err.println("[L2Tensura] Error processing delayed EP update: " + e.getMessage());
                return true; // 移除出错的生物
            }
        });
    }

    /**
     * 检查生物是否已经被我们的系统处理过（通过NBT标记）
     */
    private static boolean isAlreadyProcessedByUs(LivingEntity entity) {
        try {
            CompoundTag entityNBT = new CompoundTag();
            entity.saveWithoutId(entityNBT);

            // 检查是否有我们的处理标记
            if (entityNBT.contains("ForgeCaps")) {
                CompoundTag forgeCaps = entityNBT.getCompound("ForgeCaps");
                if (forgeCaps.contains("l2tensura:ep_enhanced")) {
                    CompoundTag l2tensuraData = forgeCaps.getCompound("l2tensura:ep_enhanced");
                    return l2tensuraData.getBoolean("processed");
                }
            }

            return false;

        } catch (Exception e) {
            // 如果检查出错，假设未处理过
            return false;
        }
    }

    /**
     * 在NBT中标记生物已经被我们的系统处理过
     */
    private static void markAsProcessedByUs(LivingEntity entity) {
        try {
            CompoundTag entityNBT = new CompoundTag();
            entity.saveWithoutId(entityNBT);

            // 确保ForgeCaps存在
            if (!entityNBT.contains("ForgeCaps")) {
                entityNBT.put("ForgeCaps", new CompoundTag());
            }

            CompoundTag forgeCaps = entityNBT.getCompound("ForgeCaps");

            // 确保我们的数据存在
            if (!forgeCaps.contains("l2tensura:ep_enhanced")) {
                forgeCaps.put("l2tensura:ep_enhanced", new CompoundTag());
            }

            CompoundTag l2tensuraData = forgeCaps.getCompound("l2tensura:ep_enhanced");

            // 设置处理标记和相关信息
            l2tensuraData.putBoolean("processed", true);
            l2tensuraData.putLong("processTime", System.currentTimeMillis());
            l2tensuraData.putString("version", "1.0"); // 版本标记，便于将来升级

            // 重新加载NBT数据
            entity.load(entityNBT);

            if (EPConfig.isDebugLoggingEnabled()) {
                System.out.println(String.format(
                    "[L2Tensura] Marked %s as processed by L2Tensura",
                    entity.getType().getDescriptionId()
                ));
            }

        } catch (Exception e) {
            System.err.println("[L2Tensura] Error marking entity as processed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 清理已死亡生物的记录
     */
    private static void cleanupDeadEntities() {
        processedEntities.entrySet().removeIf(entry -> {
            LivingEntity entity = entry.getKey();
            return entity.isRemoved() || !entity.isAlive();
        });

        originalStates.entrySet().removeIf(entry -> {
            LivingEntity entity = entry.getKey();
            return entity.isRemoved() || !entity.isAlive();
        });
    }

    /**
     * 验证生物状态是否正常
     */
    private static boolean isEntityStateValid(LivingEntity entity) {
        try {
            // 基本状态检查
            if (entity.isRemoved() || !entity.isAlive()) {
                return false;
            }

            // 检查生命值是否合理
            float health = entity.getHealth();
            float maxHealth = entity.getMaxHealth();

            if (health <= 0 || maxHealth <= 0 || Float.isNaN(health) || Float.isNaN(maxHealth)) {
                return false;
            }

            // 检查是否处于正常状态（不是假死状态）
            if (entity.deathTime > 0) {
                return false;
            }

            return true;

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 安全地增强生物EP和生命值
     */
    private static boolean safelyEnhanceEntity(LivingEntity entity, double targetEP) {
        try {
            // 再次验证生物状态
            if (!isEntityStateValid(entity)) {
                return false;
            }

            // 记录当前状态
            float currentHealth = entity.getHealth();
            float currentMaxHealth = entity.getMaxHealth();

            // 尝试设置EP（使用更安全的方法）
            boolean epSetSuccess = safelySetEP(entity, targetEP);

            if (!epSetSuccess) {
                return false;
            }

            // 验证EP设置后生物状态仍然正常
            if (!isEntityStateValid(entity)) {
                // 如果状态异常，尝试恢复
                restoreEntityState(entity);
                return false;
            }

            // 安全地恢复满血
            safelyHealToFullHealth(entity);

            // 最终验证
            return isEntityStateValid(entity);

        } catch (Exception e) {
            System.err.println("[L2Tensura] Error in safelyEnhanceEntity: " + e.getMessage());
            // 尝试恢复生物状态
            restoreEntityState(entity);
            return false;
        }
    }

    /**
     * 立即设置EP（保留原有功能，确保EP立即生效）
     */
    private static void setEPInstantly(LivingEntity entity, double ep) {
        try {
            // 验证生物状态
            if (!isEntityStateValid(entity)) {
                return;
            }

            // 方法1：使用API设置
            TensuraEPCapability.setLivingEP(entity, ep);

            // 方法2：直接修改NBT数据（确保设置成功）
            CompoundTag entityNBT = new CompoundTag();
            entity.saveWithoutId(entityNBT);

            // 确保ForgeCaps存在
            if (!entityNBT.contains("ForgeCaps")) {
                entityNBT.put("ForgeCaps", new CompoundTag());
            }

            CompoundTag forgeCaps = entityNBT.getCompound("ForgeCaps");

            // 确保tensura:ep能力存在
            if (!forgeCaps.contains("tensura:ep")) {
                forgeCaps.put("tensura:ep", new CompoundTag());
            }

            CompoundTag tensuraEP = forgeCaps.getCompound("tensura:ep");

            // 设置所有相关的EP值
            tensuraEP.putDouble("EP", ep);
            tensuraEP.putDouble("currentEP", ep);
            tensuraEP.putDouble("gainedEP", 0.0);

            // 重新加载NBT数据
            entity.load(entityNBT);

            // 方法3：强制同步EP能力
            TensuraEPCapability.getFrom(entity).ifPresent(cap -> {
                try {
                    cap.setEP(entity, ep, true);
                    cap.setCurrentEP(entity, ep);
                } catch (Exception e) {
                    // 忽略错误，继续执行
                }
            });

            // 如果启用调试，输出EP设置信息
            if (EPConfig.isDebugLoggingEnabled()) {
                double verifyEP = TensuraEPCapability.getEP(entity);
                System.out.println(String.format(
                    "[L2Tensura] Instantly set %s EP: %.1f (Verified: %.1f)",
                    entity.getType().getDescriptionId(),
                    ep,
                    verifyEP
                ));
            }

        } catch (Exception e) {
            System.err.println("[L2Tensura] Error setting EP instantly: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 安全地设置EP
     */
    private static boolean safelySetEP(LivingEntity entity, double ep) {
        try {
            // 方法1：使用API设置
            TensuraEPCapability.setLivingEP(entity, ep);

            // 验证设置是否成功
            double verifyEP = TensuraEPCapability.getEP(entity);
            if (Math.abs(verifyEP - ep) < 0.1) {
                return true;
            }

            // 方法2：如果API失败，尝试能力接口
            return TensuraEPCapability.getFrom(entity).map(cap -> {
                try {
                    cap.setEP(entity, ep, true);
                    cap.setCurrentEP(entity, ep);
                    return true;
                } catch (Exception e) {
                    return false;
                }
            }).orElse(false);

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 恢复生物的原始状态
     */
    private static void restoreEntityState(LivingEntity entity) {
        try {
            EntityState originalState = originalStates.get(entity);
            if (originalState != null && originalState.wasAlive) {
                // 恢复生命值
                entity.setHealth(Math.min(originalState.originalHealth, entity.getMaxHealth()));
            }
        } catch (Exception e) {
            // 忽略恢复错误
        }
    }

    /**
     * 直接修改生物的EP数据（已弃用，保留用于兼容性）
     */
    @Deprecated
    private static void setEPDirectly(LivingEntity entity, double ep) {
        try {
            // 方法1：使用API设置
            TensuraEPCapability.setLivingEP(entity, ep);

            // 方法2：直接修改NBT数据（确保设置成功）
            CompoundTag entityNBT = new CompoundTag();
            entity.saveWithoutId(entityNBT);

            // 确保ForgeCaps存在
            if (!entityNBT.contains("ForgeCaps")) {
                entityNBT.put("ForgeCaps", new CompoundTag());
            }

            CompoundTag forgeCaps = entityNBT.getCompound("ForgeCaps");

            // 确保tensura:ep能力存在
            if (!forgeCaps.contains("tensura:ep")) {
                forgeCaps.put("tensura:ep", new CompoundTag());
            }

            CompoundTag tensuraEP = forgeCaps.getCompound("tensura:ep");

            // 设置所有相关的EP值
            tensuraEP.putDouble("EP", ep);
            tensuraEP.putDouble("currentEP", ep);
            tensuraEP.putDouble("gainedEP", 0.0);

            // 重新加载NBT数据
            entity.load(entityNBT);

            // 方法3：强制同步EP能力
            TensuraEPCapability.getFrom(entity).ifPresent(cap -> {
                try {
                    cap.setEP(entity, ep, true);
                    cap.setCurrentEP(entity, ep);
                } catch (Exception e) {
                    // 忽略错误，继续执行
                }
            });

            // EP设置完成，不输出调试信息

        } catch (Exception e) {
            System.err.println("[L2Tensura] Error setting EP directly: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 智能恢复生命值（精确计算预期血量，只在不匹配时调整）
     */
    private static void restoreHealthSafely(LivingEntity entity) {
        try {
            // 验证生物状态
            if (!isEntityStateValid(entity)) {
                return;
            }

            // 获取原始状态
            EntityState originalState = originalStates.get(entity);
            if (originalState == null) {
                // 如果没有原始状态，不进行任何调整
                if (EPConfig.isDebugLoggingEnabled()) {
                    System.out.println(String.format(
                        "[L2Tensura] No original state for %s, skipping health adjustment",
                        entity.getType().getDescriptionId()
                    ));
                }
                return;
            }

            // 计算预期的生命值
            float expectedHealth = calculateExpectedHealth(entity, originalState);
            float currentHealth = entity.getHealth();
            float currentMaxHealth = entity.getMaxHealth();

            // 只有当当前血量与预期血量不匹配时才进行调整
            float healthDifference = Math.abs(currentHealth - expectedHealth);
            float tolerance = Math.max(1.0f, currentMaxHealth * 0.01f); // 1%的容差或至少1点血

            if (healthDifference > tolerance) {
                // 需要调整血量
                entity.setHealth(expectedHealth);

                if (EPConfig.isDebugLoggingEnabled()) {
                    System.out.println(String.format(
                        "[L2Tensura] Adjusted %s health: %.1f -> %.1f (Expected: %.1f, Max: %.1f)",
                        entity.getType().getDescriptionId(),
                        currentHealth,
                        expectedHealth,
                        expectedHealth,
                        currentMaxHealth
                    ));
                }
            } else {
                // 血量已经正确，无需调整
                if (EPConfig.isDebugLoggingEnabled()) {
                    System.out.println(String.format(
                        "[L2Tensura] %s health already correct: %.1f/%.1f (Expected: %.1f)",
                        entity.getType().getDescriptionId(),
                        currentHealth,
                        currentMaxHealth,
                        expectedHealth
                    ));
                }
            }

            // 验证设置后状态仍然正常
            if (!isEntityStateValid(entity)) {
                // 如果设置后状态异常，恢复原始生命值
                entity.setHealth(Math.min(originalState.originalHealth, entity.getMaxHealth()));
                if (EPConfig.isDebugLoggingEnabled()) {
                    System.out.println(String.format(
                        "[L2Tensura] Health adjustment caused invalid state, reverted %s to %.1f",
                        entity.getType().getDescriptionId(),
                        entity.getHealth()
                    ));
                }
            }

        } catch (Exception e) {
            System.err.println("[L2Tensura] Error in smart health restore: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 计算生物在EP增强后的预期生命值
     */
    private static float calculateExpectedHealth(LivingEntity entity, EntityState originalState) {
        try {
            float currentMaxHealth = entity.getMaxHealth();
            float originalMaxHealth = originalState.originalMaxHealth;
            float originalHealth = originalState.originalHealth;

            // 如果最大生命值没有变化，预期血量就是原始血量
            if (Math.abs(currentMaxHealth - originalMaxHealth) < 0.1f) {
                return originalHealth;
            }

            // 计算生命值增长比例
            float healthGrowthRatio = currentMaxHealth / originalMaxHealth;

            // 根据原始血量比例计算预期血量
            float originalHealthRatio = originalHealth / originalMaxHealth;

            // 有几种策略来计算预期血量：

            // 策略1：如果原始血量是满血，EP增强后也应该是满血
            if (originalHealthRatio >= 0.99f) {
                return currentMaxHealth;
            }

            // 策略2：如果原始血量不是满血，有两种可能：
            // a) 生物本来就不是满血状态（保持比例）
            // b) 生物是满血，但由于某种原因显示不满（应该调整为满血）

            // 判断是否应该是满血：如果原始血量比例很高（>95%），可能是精度问题
            if (originalHealthRatio >= 0.95f) {
                return currentMaxHealth; // 调整为满血
            }

            // 策略3：保持原始血量比例
            float expectedByRatio = currentMaxHealth * originalHealthRatio;

            // 策略4：按增长比例调整原始血量
            float expectedByGrowth = originalHealth * healthGrowthRatio;

            // 选择更合理的策略：
            // 如果增长比例不大（<2倍），使用比例策略
            // 如果增长比例很大（>=2倍），可能是EP大幅增强，使用增长策略但有上限
            if (healthGrowthRatio < 2.0f) {
                return expectedByRatio;
            } else {
                // 大幅增长时，限制血量增长，避免异常高血量
                float maxReasonableHealth = originalHealth * 2.0f; // 最多增长2倍
                return Math.min(expectedByGrowth, Math.min(maxReasonableHealth, currentMaxHealth));
            }

        } catch (Exception e) {
            // 计算出错时，返回当前血量（不调整）
            return entity.getHealth();
        }
    }

    /**
     * 安全地将生物恢复到满血状态
     */
    private static void safelyHealToFullHealth(LivingEntity entity) {
        try {
            // 验证生物状态
            if (!isEntityStateValid(entity)) {
                return;
            }

            // 获取生物的最大生命值
            float maxHealth = entity.getMaxHealth();

            // 确保最大生命值合理
            if (maxHealth <= 0 || Float.isNaN(maxHealth)) {
                return;
            }

            // 设置当前生命值为最大生命值
            entity.setHealth(maxHealth);

            // 验证设置后状态仍然正常
            if (!isEntityStateValid(entity)) {
                // 如果设置后状态异常，尝试恢复原始状态
                restoreEntityState(entity);
                return;
            }

            // 如果生物有调试日志启用，输出信息
            if (EPConfig.isDebugLoggingEnabled()) {
                System.out.println(String.format(
                    "[L2Tensura] Safely healed %s to full health: %.1f/%.1f",
                    entity.getType().getDescriptionId(),
                    entity.getHealth(),
                    maxHealth
                ));
            }

        } catch (Exception e) {
            System.err.println("[L2Tensura] Error safely healing entity to full health: " + e.getMessage());
            // 尝试恢复原始状态
            restoreEntityState(entity);
        }
    }

    /**
     * 将生物恢复到满血状态（保留用于兼容性）
     */
    @Deprecated
    private static void healToFullHealth(LivingEntity entity) {
        safelyHealToFullHealth(entity);
    }
}
