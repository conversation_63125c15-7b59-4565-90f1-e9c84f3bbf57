buildscript {
    repositories {
        maven { url = 'https://maven.minecraftforge.net/' }
        maven { url = 'https://libraries.minecraft.net' }
        maven { url = 'https://maven.aliyun.com/repository/public' }
        maven { url = 'https://maven.aliyun.com/repository/central' }
        maven { url = 'https://maven.aliyun.com/repository/jcenter' }
        mavenCentral()
        maven { url = 'https://repo.spongepowered.org/repository/maven-public/' }
        maven { url = 'https://maven.parchmentmc.org' }
    }
    dependencies {
        classpath 'org.spongepowered:mixingradle:0.7-SNAPSHOT'
        classpath 'net.minecraftforge.gradle:ForgeGradle:[6.0.15,6.2)'
        classpath 'org.spongepowered:mixin:0.8.5'
        classpath 'org.parchmentmc:librarian:1.1.0'
    }
}

plugins {
    id 'eclipse'
    id 'idea'
}

apply plugin: 'net.minecraftforge.gradle'
apply plugin: 'org.spongepowered.mixin'
apply plugin: 'org.parchmentmc.librarian.forgegradle'

group = mod_group_id
version = mod_version

base {
    archivesName = mod_id
}

java {
    toolchain.languageVersion = JavaLanguageVersion.of(17)
}

minecraft {
    // The mappings can be changed at any time and must be in the following format.
    // Channel:   Version:
    // official   MCVersion             Official field/method names from Mojang mapping files
    // parchment  YYYY.MM.DD-MCVersion  Open community-sourced parameter names and javadocs layered on top of official
    //
    // You must be aware of the Mojang license when using the 'official' or 'parchment' mappings.
    // See more information here: https://github.com/MinecraftForge/MCPConfig/blob/master/Mojang.md
    //
    // Parchment is an unofficial project maintained by ParchmentMC, separate from MinecraftForge
    // Additional setup is needed to use their mappings: https://parchmentmc.org/docs/getting-started
    //
    // Use non-default mappings at your own risk. They may not always work.
    // Simply re-run your setup task after changing the mappings to update your workspace.
    mappings channel: mapping_channel, version: mapping_version

    // When true, this property will have all Eclipse/IntelliJ IDEA run configurations run the "prepareX" task for the given run configuration before launching the game.
    // In most cases, it is not necessary to enable.
    // enableEclipsePrepareRuns = true
    // enableIdeaPrepareRuns = true

    // This property allows configuring Gradle's ProcessResources task(s) to run on IDE output locations before launching the game.
    // It is REQUIRED to be set to true for this template to function.
    // See https://docs.gradle.org/current/dsl/org.gradle.language.jvm.tasks.ProcessResources.html
    copyIdeResources = true

    // When true, this property will add the folder name of all declared run configurations to generated IDE run configurations.
    // The folder name can be set on a run configuration using the "folderName" property.
    // By default, the folder name of a run configuration is the name of the Gradle project containing it.
    // generateRunFolders = true

    // This property enables access transformers for use in development.
    // They will be applied to the Minecraft artifact.
    // The access transformer file can be anywhere in the project.
    // However, it must be at "META-INF/accesstransformer.cfg" in the final mod jar to be loaded by Forge.
    // This default location is a best practice to automatically put the file in the right place in the final jar.
    // See https://docs.minecraftforge.net/en/latest/advanced/accesstransformers/ for more information.
    // accessTransformer = file('src/main/resources/META-INF/accesstransformer.cfg')

    // Default run configurations.
    // These can be tweaked, removed, or duplicated as needed.
    runs {
        // applies to all the run configs below
        configureEach {
            workingDirectory project.file('run')

            // Recommended logging data for a userdev environment
            // The markers can be added/remove as needed separated by commas.
            // "SCAN": For mods scan.
            // "REGISTRIES": For firing of registry events.
            // "REGISTRYDUMP": For getting the contents of all registries.
            property 'forge.logging.markers', 'REGISTRIES'


            // Recommended logging level for the console
            // You can set various levels here.
            // Please read: https://stackoverflow.com/questions/2031163/when-to-use-the-different-log-levels
            property 'forge.logging.console.level', 'debug'

            property 'forge.enableGameTest', 'true'
            property 'forge.enableGameTestNamespaces', mod_id
            property 'forge.disableCertificateCheck', 'true'
            property 'forge.disableNetwork', 'true'

            mods {
                "${mod_id}" {
                    source sourceSets.main
                }
            }
        }

        client {
            // Comma-separated list of namespaces to load gametests from. Empty = all namespaces.
            property 'forge.enabledGameTestNamespaces', mod_id
        }

        server {
            property 'forge.enabledGameTestNamespaces', mod_id
            args '--nogui'
        }

        // This run config launches GameTestServer and runs all registered gametests, then exits.
        // By default, the server will crash when no gametests are provided.
        // The gametest system is also enabled by default for other run configs under the /test command.
        gameTestServer {
            property 'forge.enabledGameTestNamespaces', mod_id
        }

        data {
            // example of overriding the workingDirectory set in configureEach above
            workingDirectory project.file('run-data')

            // Specify the modid for data generation, where to output the resulting resource, and where to look for existing resources.
            args '--mod', mod_id, '--all', '--output', file('src/generated/resources/'), '--existing', file('src/main/resources/')
        }
    }
}

mixin {
    add sourceSets.main, "${mod_id}.refmap.json"

    config "${mod_id}.mixins.json"
}

// Include resources generated by data generators.
sourceSets.main.resources { srcDir 'src/generated/resources' }

repositories {
    maven { url = 'https://maven.minecraftforge.net/' }
    maven { url = 'https://libraries.minecraft.net' }
    maven { url = 'https://maven.aliyun.com/repository/public' }
    maven { url = 'https://maven.aliyun.com/repository/central' }
    maven { url = 'https://maven.aliyun.com/repository/jcenter' }
    mavenCentral()
    maven { url = 'https://repo.spongepowered.org/repository/maven-public/' }
    maven { url = 'https://maven.parchmentmc.org/' }

    flatDir {
        dirs 'lib'
    }

    exclusiveContent {
        forRepository {
            maven {
                url "https://cursemaven.com/"
            }
        }
        filter {
            includeGroup "curse.maven"
        }
    }

    maven { url 'https://dl.cloudsmith.io/public/geckolib3/geckolib/maven' }
    maven { url 'https://dl.cloudsmith.io/public/manasmods/manascore/maven/' }
    maven { url 'https://dl.cloudsmith.io/public/manasmods/tensura/maven/' }
    maven { url 'https://jitpack.io/' }
    maven { url "https://maven.firstdarkdev.xyz/snapshots" }
}

dependencies {
    // Specify the version of Minecraft to use.
    // Any artifact can be supplied so long as it has a "userdev" classifier artifact and is a compatible patcher artifact.
    // The "userdev" classifier will be requested and setup by ForgeGradle.
    // If the group id is "net.minecraft" and the artifact id is one of ["client", "server", "joined"],
    // then special handling is done to allow a setup of a vanilla dependency without the use of an external repository.
    minecraft "net.minecraftforge:forge:${minecraft_version}-${forge_version}"
    annotationProcessor 'org.spongepowered:mixin:0.8.5:processor'
    implementation fg.deobf("com.github.manasmods:ManasCore:${minecraft_version}-${manasCoreVersion}")
    implementation fg.deobf("com.github.manasmods:tensura:${minecraft_version}-${tensuraVersion}")
    implementation fg.deobf('software.bernie.geckolib:geckolib-forge-1.19:3.1.40')
    //implementation fg.deobf("curse.maven:gecko-${geckoVersion}")
    implementation fg.deobf('com.github.glitchfiend:TerraBlender-forge:1.19.2-*********')
    //implementation fg.deobf("curse.maven:terrablender-${terrablenderVersion}")

    // Local lib dependencies
    implementation fg.deobf("blank:Patchouli-1.19.2:77")
    implementation fg.deobf("blank:curios-forge-1.19.2:*******")
    implementation fg.deobf("blank:l2complements:1.4.10")
    implementation fg.deobf("blank:l2hostility:1.1.0")
    implementation fg.deobf("blank:l2library:1.10.0")


    // Example mod dependency with JEI - using fg.deobf() ensures the dependency is remapped to your development mappings
    // The JEI API is declared for compile time use, while the full JEI artifact is used at runtime
    // compileOnly fg.deobf("mezz.jei:jei-${mc_version}-common-api:${jei_version}")
    // compileOnly fg.deobf("mezz.jei:jei-${mc_version}-forge-api:${jei_version}")
    // runtimeOnly fg.deobf("mezz.jei:jei-${mc_version}-forge:${jei_version}")

    // Example mod dependency using a mod jar from ./libs with a flat dir repository
    // This maps to ./libs/coolmod-${mc_version}-${coolmod_version}.jar
    // The group id is ignored when searching -- in this case, it is "blank"
    // implementation fg.deobf("blank:coolmod-${mc_version}:${coolmod_version}")

    // For more info:
    // http://www.gradle.org/docs/current/userguide/artifact_dependencies_tutorial.html
    // http://www.gradle.org/docs/current/userguide/dependency_management.html

    annotationProcessor 'org.spongepowered:mixin:0.8.5:processor'

}

// This block of code expands all declared replace properties in the specified resource targets.
// A missing property will result in an error. Properties are expanded using ${} Groovy notation.
// When "copyIdeResources" is enabled, this will also run before the game launches in IDE environments.
// See https://docs.gradle.org/current/dsl/org.gradle.language.jvm.tasks.ProcessResources.html
tasks.named('processResources', ProcessResources).configure {
    var replaceProperties = [minecraft_version   : minecraft_version, minecraft_version_range: minecraft_version_range,
                             forge_version       : forge_version, forge_version_range: forge_version_range,
                             loader_version_range: loader_version_range,
                             mod_id              : mod_id, mod_name: mod_name, mod_license: mod_license, mod_version: mod_version,
                             mod_authors         : mod_authors, mod_description: mod_description,]

    inputs.properties replaceProperties

    filesMatching(['META-INF/mods.toml', 'pack.mcmeta']) {
        expand replaceProperties + [project: project]
    }
}

// Example for how to get properties into the manifest for reading at runtime.
tasks.named('jar', Jar).configure {
    manifest {
        attributes(["Specification-Title"     : mod_id,
                    "Specification-Vendor"    : mod_authors,
                    "Specification-Version"   : "1", // We are version 1 of ourselves
                    "Implementation-Title"    : project.name,
                    "Implementation-Version"  : project.jar.archiveVersion,
                    "Implementation-Vendor"   : mod_authors,
                    "Implementation-Timestamp": new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")])
    }

    // This is the preferred method to reobfuscate your jar file
    finalizedBy 'reobfJar'
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8' // Use the UTF-8 charset for Java compilation
}
