{"advancements.l2hostility.advancements.hostility.abrahadabra.description": "ɐɹqɐpɐɥɐɹqⱯ uıɐʇqO", "advancements.l2hostility.advancements.hostility.abrahadabra.title": "ǝןɐuıℲ ǝɥ⟘", "advancements.l2hostility.advancements.hostility.breed.description": "sqoɯ uo ɯǝʇı ʇıɐɹʇ ɐ ǝs∩", "advancements.l2hostility.advancements.hostility.breed.title": "sqoW buıpǝǝɹᗺ", "advancements.l2hostility.advancements.hostility.detector.description": "ɹoʇɔǝʇǝᗡ ʎʇıןıʇsoH uıɐʇqO", "advancements.l2hostility.advancements.hostility.detector.title": "ssɐdɯoƆ ʎʇǝɟɐS", "advancements.l2hostility.advancements.hostility.effect_kill_adaptive.description": "ʇı ןןıʞ puɐ ǝʌıʇdɐpⱯ ɥʇıʍ sqoɯ uo ǝɹıɟ ɹo ʇɔǝɟɟǝ ǝɯɐןɟ ןnos/ɹǝɥʇıʍ/uosıod ǝs∩", "advancements.l2hostility.advancements.hostility.effect_kill_adaptive.title": "uoıʇdɐpⱯ ʇuǝʌǝɹԀ", "advancements.l2hostility.advancements.hostility.effect_kill_regen.description": "ʇı ןןıʞ puɐ uoıʇɐɹǝuǝbǝᴚ ɥʇıʍ sqoɯ uo ʇɔǝɟɟǝ ǝsɹnɔ ǝs∩", "advancements.l2hostility.advancements.hostility.effect_kill_regen.title": "buıןɐǝH ʇuǝʌǝɹԀ", "advancements.l2hostility.advancements.hostility.effect_kill_teleport.description": "ʇı ןןıʞ puɐ ʇɹodǝןǝ⟘ ɥʇıʍ sqoɯ uo ʇɔǝɟɟǝ uoıʇɐɹǝɔɹɐɔuı ǝs∩", "advancements.l2hostility.advancements.hostility.effect_kill_teleport.title": "buıʇɹodǝןǝ⟘ ʇuǝʌǝɹԀ", "advancements.l2hostility.advancements.hostility.effect_kill_undead.description": "ʇı ןןıʞ puɐ buıʎpu∩ ɥʇıʍ sqoɯ uo ʇɔǝɟɟǝ ǝsɹnɔ ǝs∩", "advancements.l2hostility.advancements.hostility.effect_kill_undead.title": "buıʌıʌǝᴚ ʇuǝʌǝɹԀ", "advancements.l2hostility.advancements.hostility.envy.description": "ʎʌuƎ ɟo ǝsɹnƆ uıɐʇqO", "advancements.l2hostility.advancements.hostility.envy.title": "¡ʇɐɥʇ ʇuɐʍ I", "advancements.l2hostility.advancements.hostility.glasses.description": "sqoɯ ǝןqısıʌuı ʇno puıɟ oʇ sǝssɐן⅁ ɹoʇɔǝʇǝᗡ uıɐʇqO", "advancements.l2hostility.advancements.hostility.glasses.title": "sʇɐǝɹɥ⟘ ǝןqısıʌuI ǝɥ⟘", "advancements.l2hostility.advancements.hostility.gluttony.description": "ʎuoʇʇnן⅁ ɟo ǝsɹnƆ uıɐʇqO", "advancements.l2hostility.advancements.hostility.gluttony.title": "pǝʇıɯıןu∩ ʎʇıןıʇsoH", "advancements.l2hostility.advancements.hostility.greed.description": "pǝǝɹ⅁ ɟo ǝsɹnƆ uıɐʇqO", "advancements.l2hostility.advancements.hostility.greed.title": "ɹǝʇʇǝᗺ ǝɥʇ ǝɹoW ǝɥ⟘", "advancements.l2hostility.advancements.hostility.imagine_breaker.description": "ɹǝʞɐǝɹᗺ ǝuıbɐɯI uıɐʇqO", "advancements.l2hostility.advancements.hostility.imagine_breaker.title": "ɥbnoɹɥʇʞɐǝɹᗺ ʎʇıןɐǝᴚ", "advancements.l2hostility.advancements.hostility.ingot.description": "ʇobuI soɐɥƆ ɐ uıɐʇqO", "advancements.l2hostility.advancements.hostility.ingot.title": "xoᗺ s,ɐɹopuɐԀ", "advancements.l2hostility.advancements.hostility.kill_10_traits.description": "sʇıɐɹʇ 0Ɩ ɥʇıʍ qoɯ ɐ ןןıʞ", "advancements.l2hostility.advancements.hostility.kill_10_traits.title": "ɹǝʎɐןS puǝbǝꞀ", "advancements.l2hostility.advancements.hostility.kill_5_traits.description": "sʇıɐɹʇ ϛ ɥʇıʍ qoɯ ɐ ןןıʞ", "advancements.l2hostility.advancements.hostility.kill_5_traits.title": "ǝןʇʇɐᗺ ʎɹɐpuǝbǝꞀ", "advancements.l2hostility.advancements.hostility.kill_adaptive.description": "ʇıɐɹ⟘ ǝʌıʇdɐpⱯ puɐ 'ʎʞuɐ⟘ 'uoıʇɐɹǝuǝbǝᴚ 'uoıʇɔǝʇoɹԀ ɥʇıʍ qoɯ ɐ ןןıʞ", "advancements.l2hostility.advancements.hostility.kill_adaptive.title": "ǝɹnsɐǝW ǝʌısuǝɟǝᗡ-ɹǝʇunoƆ", "advancements.l2hostility.advancements.hostility.kill_dementor.description": "ʇıɐɹ⟘ ןןǝdsıᗡ puɐ ɹoʇuǝɯǝᗡ ɥʇıʍ qoɯ ɐ ןןıʞ", "advancements.l2hostility.advancements.hostility.kill_dementor.title": "ɹoʇɐpıןɐʌuI ʎʇıunɯɯI", "advancements.l2hostility.advancements.hostility.kill_first.description": "sʇıɐɹʇ ɥʇıʍ qoɯ ɐ ןןıʞ", "advancements.l2hostility.advancements.hostility.kill_first.title": "ʇuǝuoddO ʎɥʇɹoM", "advancements.l2hostility.advancements.hostility.kill_ragnarok.description": "ʇıɐɹ⟘ ʞoɹɐubɐᴚ puɐ ɐɹnⱯ ɹǝןןıʞ ɥʇıʍ qoɯ ɐ ןןıʞ", "advancements.l2hostility.advancements.hostility.kill_ragnarok.title": "ǝןʇʇɐᗺ ןɐuıℲ ǝɥ⟘", "advancements.l2hostility.advancements.hostility.kill_tanky.description": "ʇıɐɹ⟘ ʎʞuɐ⟘ puɐ uoıʇɔǝʇoɹԀ ɥʇıʍ qoɯ ɐ ןןıʞ", "advancements.l2hostility.advancements.hostility.kill_tanky.title": "ɹǝuǝdO uɐƆ", "advancements.l2hostility.advancements.hostility.lust.description": "ʇsnꞀ ɟo ǝsɹnƆ uıɐʇqO", "advancements.l2hostility.advancements.hostility.lust.title": "ǝsdɹoƆ pǝʞɐN", "advancements.l2hostility.advancements.hostility.miracle.description": "ʇobuI ǝןɔɐɹıW uıɐʇqO", "advancements.l2hostility.advancements.hostility.miracle.title": "pןɹoM ǝɥʇ ɟo ǝןɔɐɹıW", "advancements.l2hostility.advancements.hostility.patchouli.description": "ǝpınb ʎʇıןıʇsoɥ ǝɥʇ pɐǝᴚ", "advancements.l2hostility.advancements.hostility.patchouli.title": "ʎʇıןıʇsoHᄅꞀ oʇ oɹʇuI", "advancements.l2hostility.advancements.hostility.pride.description": "ǝpıɹԀ ɟo ǝsɹnƆ uıɐʇqO", "advancements.l2hostility.advancements.hostility.pride.title": "ʎʇıןıʇsoH ɟo buıʞ", "advancements.l2hostility.advancements.hostility.root.description": "ǝpınb ןɐʌıʌɹns ɹnoʎ", "advancements.l2hostility.advancements.hostility.root.title": "ʎʇıןıʇsoHᄅꞀ oʇ ǝɯoɔןǝM", "advancements.l2hostility.advancements.hostility.sloth.description": "ɥʇoןS ɟo ǝsɹnƆ uıɐʇqO", "advancements.l2hostility.advancements.hostility.sloth.title": "ʞɐǝɹq ɐ ʇuɐʍ I", "advancements.l2hostility.advancements.hostility.trait.description": "ɯǝʇı ʇıɐɹʇ ɐ uıɐʇqO", "advancements.l2hostility.advancements.hostility.trait.title": "pןɹoM ʍǝN ǝɥʇ oʇ ǝʇɐ⅁", "advancements.l2hostility.advancements.hostility.wrath.description": "ɥʇɐɹM ɟo ǝsɹnƆ uıɐʇqO", "advancements.l2hostility.advancements.hostility.wrath.title": "ǝɯı⟘ ǝbuǝʌǝᴚ", "block.l2hostility.hostility_spawner": "ɹǝuʍɐdS ʎʇıןıʇsoH", "config.jade.plugin_l2hostility.mob": "ʎʇıןıʇsoHᄅꞀ", "curios.identifier.hostility_curse": "ǝsɹnƆ - ʎʇıןıʇsoHᄅꞀ", "curios.modifiers.hostility_curse": ":ǝsɹnƆ sɐ uɹoʍ uǝɥM", "death.attack.killer_aura": "ɐɹnɐ ɹǝןןıʞ ʎq pǝןןıʞ sɐʍ %s", "death.attack.killer_aura.player": "ɐɹnɐ ɹǝןןıʞ s,%s ʎq pǝןןıʞ sɐʍ %s", "effect.l2hostility.antibuild": "pןınqıʇuⱯ", "effect.l2hostility.antibuild.description": "˙ʞɔoןq ǝɔɐןd ʇouuɐɔ ɹǝʎɐןd ǝʞɐW", "effect.l2hostility.gravity": "ʎʇıʌɐɹ⅁", "effect.l2hostility.gravity.description": "˙ʎʇıʌɐɹb ʎʇıʇuǝ ǝsɐǝɹɔuI", "effect.l2hostility.moonwalk": "ʞןɐʍuooW", "effect.l2hostility.moonwalk.description": "˙ʎʇıʌɐɹb ʎʇıʇuǝ ǝsɐǝɹɔǝᗡ", "enchantment.l2hostility.insulator": "ɹoʇɐןnsuI", "enchantment.l2hostility.insulator.desc": "noʎ sǝɥsnd ɹo sןןnd ʇɐɥʇ sʇɔǝɟɟǝ ʇıɐɹʇ ǝɔnpǝᴚ", "enchantment.l2hostility.split_suppressor": "ɹossǝɹddnS ʇıןdS", "enchantment.l2hostility.split_suppressor.desc": "ʇıɥ uo sǝıɯǝuǝ uo ʇıɐɹʇ ʇıןdS ǝןqɐsıᗡ", "enchantment.l2hostility.vanish": "ɥsıuɐΛ", "enchantment.l2hostility.vanish.desc": "ɹǝʎɐןd ǝɹnʇuǝʌpɐ / ןɐʌıʌɹns ɟo puɐɥ uı ɹo punoɹb uo uǝɥʍ sǝɥsıuɐʌ ɯǝʇı sıɥ⟘", "entity.l2hostility.hostility_bullet": "ʇǝןןnᗺ ʎʇıןıʇsoH", "entity.l2hostility.hostility_charge": "ǝbɹɐɥƆ ʎʇıןıʇsoH", "item.l2hostility.abrahadabra": "ɐɹqɐpɐɥɐɹqⱯ", "item.l2hostility.ai_config_wand": "puɐM bıɟuoƆ ıⱯ", "item.l2hostility.book_of_omniscience": "ǝɔuǝıɔsıuɯO ɟO ʞooᗺ", "item.l2hostility.book_of_reprint": "ʇuıɹdǝᴚ ɟO ʞooᗺ", "item.l2hostility.booster_potion": "uoıʇoԀ ɹǝʇsooᗺ", "item.l2hostility.bottle_of_curse": "ǝsɹnƆ ɟO ǝןʇʇoᗺ", "item.l2hostility.bottle_of_sanity": "ʎʇıuɐS ɟO ǝןʇʇoᗺ", "item.l2hostility.chaos_ingot": "ʇobuI soɐɥƆ", "item.l2hostility.charm_of_looting_1": "ɯɹɐɥƆ buıʇooꞀ pǝɥsıןodu∩", "item.l2hostility.charm_of_looting_2": "ɯɹɐɥƆ buıʇooꞀ ןɐɔıbɐW", "item.l2hostility.charm_of_looting_3": "ɯɹɐɥƆ buıʇooꞀ ɔıʇoɐɥƆ", "item.l2hostility.charm_of_looting_4": "ɯɹɐɥƆ buıʇooꞀ snoןnɔɐɹıW", "item.l2hostility.curse_of_envy": "ʎʌuƎ ɟO ǝsɹnƆ", "item.l2hostility.curse_of_gluttony": "ʎuoʇʇnן⅁ ɟO ǝsɹnƆ", "item.l2hostility.curse_of_greed": "pǝǝɹ⅁ ɟO ǝsɹnƆ", "item.l2hostility.curse_of_lust": "ʇsnꞀ ɟO ǝsɹnƆ", "item.l2hostility.curse_of_pride": "ǝpıɹԀ ɟO ǝsɹnƆ", "item.l2hostility.curse_of_sloth": "ɥʇoןS ɟO ǝsɹnƆ", "item.l2hostility.curse_of_wrath": "ɥʇɐɹM ɟO ǝsɹnƆ", "item.l2hostility.detector_glasses": "sǝssɐן⅁ ɹoʇɔǝʇǝᗡ", "item.l2hostility.equipment_wand": "puɐM ʇuǝɯdınbƎ", "item.l2hostility.eternal_witch_charge": "ǝbɹɐɥƆ ɥɔʇıM ןɐuɹǝʇƎ", "item.l2hostility.flaming_thorn": "uɹoɥ⟘ buıɯɐןℲ", "item.l2hostility.greed_of_nidhoggur": "ɹnbboɥpıN ɟO pǝǝɹ⅁", "item.l2hostility.hostility_detector": "ɹoʇɔǝʇǝᗡ ʎʇıןıʇsoH", "item.l2hostility.hostility_essence": "ǝɔuǝssƎ ʎʇıןıʇsoH", "item.l2hostility.hostility_orb": "qɹO ʎʇıןıʇsoH", "item.l2hostility.imagine_breaker": "ɹǝʞɐǝɹᗺ ǝuıbɐɯI", "item.l2hostility.miracle_ingot": "ʇobuI ǝןɔɐɹıW", "item.l2hostility.miracle_powder": "ɹǝpʍoԀ ǝןɔɐɹıW", "item.l2hostility.pocket_of_restoration": "uoıʇɐɹoʇsǝᴚ ɟO ʇǝʞɔoԀ", "item.l2hostility.ring_of_corrosion": "uoısoɹɹoƆ ɟO buıᴚ", "item.l2hostility.ring_of_divinity": "ʎʇıuıʌıᗡ ɟO buıᴚ", "item.l2hostility.ring_of_healing": "buıןɐǝH ɟO buıᴚ", "item.l2hostility.ring_of_incarceration": "uoıʇɐɹǝɔɹɐɔuI ɟO buıᴚ", "item.l2hostility.ring_of_life": "ǝɟıꞀ ɟO buıᴚ", "item.l2hostility.ring_of_ocean": "uɐǝɔO ɟO buıᴚ", "item.l2hostility.ring_of_reflection": "uoıʇɔǝןɟǝᴚ ɟO buıᴚ", "item.l2hostility.sealed_item": "ɯǝʇI pǝןɐǝS", "item.l2hostility.target_select_wand": "puɐM ʇɔǝןǝS ʇǝbɹɐ⟘", "item.l2hostility.trait_adder_wand": "puɐM ɹǝppⱯ ʇıɐɹ⟘", "item.l2hostility.witch_charge": "ǝbɹɐɥƆ ɥɔʇıM", "item.l2hostility.witch_droplet": "ʇǝןdoɹᗡ ɥɔʇıM", "item.l2hostility.witch_wand": "puɐM ɥɔʇıM", "itemGroup.l2hostility.hostility": "ʎʇıןıʇsoH ᄅꞀ", "l2hostility.boss_event": "%s/%s :ssǝɹboɹԀ buıɹɐǝןƆ ʎʇıןıʇsoH", "l2hostility.command.mob.success": "sqoɯ %s uo suoıʇɔɐ pǝɯɹoɟɹǝԀ", "l2hostility.command.player.fail": "ʇɔǝɟɟǝ ou ɹo ʇǝbɹɐʇ ou sɐɥ puɐɯɯoƆ", "l2hostility.command.player.get_base": "%s ןǝʌǝן ʎʇןnɔıɟɟıp ǝsɐq sɐɥ %s", "l2hostility.command.player.get_dim": "suoısuǝɯıp %s pǝʇısıʌ sɐɥ %s", "l2hostility.command.player.get_total": "%s ןǝʌǝן ʎʇןnɔıɟɟıp ןןɐɹǝʌo sɐɥ %s", "l2hostility.command.player.success": "sɹǝʎɐןd %s uo suoıʇɔɐ pǝɯɹoɟɹǝԀ", "l2hostility.command.player.trait_cap": "%s ʞuɐɹ sı pǝןןıʞ sɐɥ %s ʞuɐɹ xɐɯ ǝɥ⟘", "l2hostility.command.region.clear": "pǝɹɐǝןƆ uoıʇɔǝS", "l2hostility.command.region.count": "suoıʇɔǝs ʞunɥɔ %s ɹoɟ suoıʇɔⱯ pǝɯɹoɟɹǝԀ", "l2hostility.command.region.get_base": "%s ןǝʌǝן ʎʇןnɔıɟɟıp ǝsɐq sɐɥ uoıʇɔǝs ʇǝbɹɐ⟘", "l2hostility.command.region.get_scale": "%s ǝןɐɔs ʎʇןnɔıɟɟıp sɐɥ uoıʇɔǝs ʇǝbɹɐ⟘", "l2hostility.command.region.get_total": "%s ןǝʌǝן ʎʇןnɔıɟɟıp ןɐʇoʇ sɐɥ uoıʇɔǝs ʇǝbɹɐ⟘", "l2hostility.command.region.local_off": "ɟɟo pǝuɹnʇ sı ʎʇןnɔıɟɟıp uoıʇɔǝS", "l2hostility.command.region.not_clear": "pǝɹɐǝןƆ ʇoN uoıʇɔǝS", "l2hostility.command.region.success": "pǝǝɔɔnS uoıʇɔⱯ", "l2hostility.info.chunk_level": "%s :ʎʇןnɔıɟɟıp ǝsɐq ʞunɥƆ", "l2hostility.info.chunk_scale": "%s :ǝןɐɔs ʎʇןnɔıɟɟıp ʞunɥƆ", "l2hostility.info.clear": "pǝɹɐǝןɔ ʎʇןnɔıɟɟıp uoıʇɔǝS ʞunɥƆ", "l2hostility.info.player_cap": "%s :ʇıɯıן ʞuɐɹ ʇıɐɹʇ qoW", "l2hostility.info.player_detail.adaptive": "%s :ןǝʌǝן ǝʌıʇdɐpⱯ", "l2hostility.info.player_detail.dim": "%s :snuoq uoısuǝɯıp pǝʇısıΛ", "l2hostility.info.player_detail.external": "%s :ʎʇןnɔıɟɟıp ןɐuɹǝʇxƎ", "l2hostility.info.player_detail.item": "%s :snuoq ɯǝʇI", "l2hostility.info.player_exp": "%s%% :ssǝɹboɹd ʎʇןnɔıɟɟıᗡ", "l2hostility.info.player_level": "%s :ןǝʌǝן ʎʇןnɔıɟɟıp ɹǝʎɐןԀ", "l2hostility.info.reward": "spɹɐʍǝɹ %s pǝuıɐʇqO", "l2hostility.info.section_detail.adaptive": "%s :ןǝʌǝן ǝʌıʇdɐpⱯ", "l2hostility.info.section_detail.biome": "%s :ןǝʌǝן ǝɯoıᗺ", "l2hostility.info.section_detail.dim": "%s :ןǝʌǝן uoısuǝɯıᗡ", "l2hostility.info.section_detail.distance": "%s :snuoq ǝɔuɐʇsıᗡ", "l2hostility.info.title": "uoıʇɐɯɹoɟuI ʎʇןnɔıɟɟıᗡ", "l2hostility.item.book.copy": "˙ʎdoɔ oʇ ןıʌuɐ uı ʞooq pǝʇuɐɥɔuǝ ɥʇıʍ ʇı ǝbɹǝW", "l2hostility.item.book.everything_forbidden": "ǝןqɐuıɐʇqo ʇou sı %s", "l2hostility.item.book.everything_invalid": "ʇuǝɯʇuɐɥɔuǝ uɐ ɟo ᗡI ʇou sı ǝɯɐN", "l2hostility.item.book.everything_ready": "sןǝʌǝן %s ʇsoɔ %s ʇuǝɯʇuɐɥɔuƎ", "l2hostility.item.book.everything_shift": "˙ǝןqɐʇ ʇuǝɯʇuɐɥɔuǝ ɯoɹɟ ǝןqɐuıɐʇqo sʇuǝɯʇuɐɥɔuǝ ןןɐ ɥʇıʍ ʞooq ɐ oʇuı ʇı uɹnʇ oʇ ʇı ɥʇıʍ ʞɔıןɔ ʇɥbıɹ ʇɟıɥS", "l2hostility.item.book.everything_use": "˙ǝɔuǝıɹǝdxǝ ɹǝʎɐןd buıʇsoɔ 'ʇuǝɯʇuɐɥɔuǝ ʇɐɥʇ ɟo ʞooq ɐ ǝʇɐɹǝuǝb oʇ ʞɔıןɔ ʇɥbıɹ uǝɥʇ 'ǝןqɐʇ ʇuǝɯʇuɐɥɔuǝ ɯoɹɟ ǝןqɐuıɐʇqo ʇuǝɯʇuɐɥɔuǝ uɐ ɟo ᗡI ɥʇıʍ ɯǝʇı sıɥʇ ǝɯɐuǝᴚ", "l2hostility.item.consumable.bottle_of_curse": "%s ʎq ʎʇןnɔıɟɟıp ɹǝʎɐןd ǝsɐǝɹɔuI", "l2hostility.item.consumable.bottle_of_sanity": "ʎʇןnɔıɟɟıp ɹǝʎɐןd ǝsɐq ןןɐ ɹɐǝןƆ", "l2hostility.item.consumable.orb": "˙sןǝʌǝן ɥʇıʍ sqoɯ uʍɐds ɹǝbuoן ou suoıʇɔǝs ʞunɥɔ %sx%sx%s ǝʞɐW", "l2hostility.item.equipment.abrahadabra": "˙pɐǝʇsuı noʎ ʇǝbɹɐʇ ʇɐɥʇ sǝıɯǝuǝ buıpunoɹɹns oʇ ʇı ʎןddɐ 'noʎ uo ʇɔǝɟɟǝ ʇıɐɹʇ ɐ ʎןddɐ oʇ sǝıɹʇ ʇıɐɹʇ qoɯ ɐ uǝɥM", "l2hostility.item.equipment.curse_add_level": "ןǝʌǝן ʎʇןnɔıɟɟıp ɐɹʇxǝ %s uıɐ⅁", "l2hostility.item.equipment.curse_no_drop": "ʇooן ʎʇıןıʇsoɥ doɹp ʇou ןןıʍ ןןıʞ noʎ sqoW", "l2hostility.item.equipment.curse_of_envy": "ʞuɐɹ ʇıɐɹʇ ɹǝd %s%% ɟo ǝɔuɐɥɔ ɐ ɥʇıʍ 'sʇıɐɹʇ ɥʇıʍ sqoɯ ןןıʞ noʎ uǝɥʍ sɯǝʇı ʇıɐɹʇ ʇǝ⅁", "l2hostility.item.equipment.curse_of_gluttony": "ןǝʌǝן ɹǝd %s%% ɟo ǝɔuɐɥɔ ɐ ɥʇıʍ 'ןǝʌǝן ɥʇıʍ sqoɯ ןןıʞ noʎ uǝɥʍ ǝsɹnƆ ɟo ǝןʇʇoᗺ ʇǝ⅁", "l2hostility.item.equipment.curse_of_greed": "ʇooן ʎʇıןıʇsoɥ %s%%+ doɹp ןןıʍ ןןıʞ noʎ sqoW", "l2hostility.item.equipment.curse_of_lust": "sʇuǝɯdınbǝ ןןɐ doɹp ןןıʍ ןןıʞ noʎ sqoW", "l2hostility.item.equipment.curse_of_pride": "ןǝʌǝן ʎʇןnɔıɟɟıp ɹǝd ǝbɐɯɐp ʞɔɐʇʇɐ %s%% puɐ ɥʇןɐǝɥ %s%% uıɐ⅁", "l2hostility.item.equipment.curse_of_sloth": "sqoɯ buıןןıʞ ʎq ʎʇןnɔıɟɟıp uıɐb ʇou ןןıʍ noʎ", "l2hostility.item.equipment.curse_of_wrath": "˙noʎ uɐɥʇ ןǝʌǝן ɹǝɥbıɥ ɥʇıʍ sqoɯ ʇsuıɐbɐ ǝɔuǝɹǝɟɟıp ןǝʌǝן ʎʇןnɔıɟɟıp ɹǝd ǝbɐɯɐp ʞɔɐʇʇɐ %s%% uıɐ⅁", "l2hostility.item.equipment.curse_trait_cheap": "ʇuǝnbǝɹɟ ǝɹoɯ %s%%+ ǝq ןןıʍ sʇıɐɹʇ qoW", "l2hostility.item.equipment.flame_thorn": "%ss ɹoɟ 'sɐɥ qoɯ ʇɐɥʇ sʇɔǝɟɟǝ ɟo ɹǝqɯnu ןɐʇoʇ oʇ sןɐnbǝ ןǝʌǝן ɥʇıʍ ǝɯɐןℲ ןnoS ʇɔıןɟuı 'qoɯ ɐ ǝbɐɯɐp noʎ uǝɥM", "l2hostility.item.equipment.glasses": "sʇɔǝɟɟǝ ssǝuʞɹɐp ɹo ssǝupuıןq ǝʌɐɥ noʎ uǝɥʍ sqoɯ ǝǝs puɐ 'sqoɯ ǝןqısıʌuı ǝǝs oʇ noʎ ʍoןןⱯ", "l2hostility.item.equipment.imagine_breaker": "˙uoıʇɔǝʇoɹd ןɐɔıbɐɯ ssɐdʎq ǝbɐɯɐp ǝǝןǝɯ ɹnoʎ ןןⱯ", "l2hostility.item.equipment.looting": "˙ןıɐʇǝp ɹoɟ IƎſ ʞɔǝɥƆ ˙sdoɹp ʇıɐɹʇ ʎʇıןıʇsoɥ ǝɯos sǝןqɐuƎ", "l2hostility.item.equipment.nidhoggur": "ןǝʌǝן qoɯ ɹǝd ʇooן %s%%+ doɹp ןןıʍ ןןıʞ noʎ sqoW", "l2hostility.item.equipment.platinum_star": "˙uʍop ןooɔ ǝbɐɯɐp ssɐdʎq ǝbɐɯɐp ǝǝןǝɯ ɹnoʎ ןןⱯ", "l2hostility.item.equipment.pocket_of_restoration": "˙pǝɥsıuıɟ uǝɥʍ ʞɔɐq ʇı ʇnd puɐ 'ʇı ןɐǝsun 'ǝpısuı ɯǝʇı pǝןɐǝs ʇnd ʎןןɐɔıʇɐɯoʇnⱯ", "l2hostility.item.equipment.ring_of_corrosion": "˙ʎʇıןıqɐɹnp xɐɯ ɟo %s%% ʎq sʇuǝɯdınbǝ ɹıǝɥʇ ןןɐ ǝbɐɯɐp 'ǝbɐɯɐp ןɐǝp noʎ uǝɥM", "l2hostility.item.equipment.ring_of_corrosion_neg": "˙ʎʇıןıqɐɹnp xɐɯ ɟo %s%% ʎq sʇuǝɯdınbǝ ɹnoʎ ןןɐ ǝbɐɯɐp 'ǝbɐɯɐp ǝʞɐʇ noʎ uǝɥM", "l2hostility.item.equipment.ring_of_divinity": "ʇɔǝɟɟǝ ǝsuɐǝןƆ ʇuǝuɐɯɹǝd sʇǝ⅁ ˙ǝbɐɯɐp ɔıbɐɯ oʇ ǝunɯɯI", "l2hostility.item.equipment.ring_of_healing": "˙puoɔǝs ʎɹǝʌǝ ɥʇןɐǝɥ xɐɯ ɟo %s%% sןɐǝH", "l2hostility.item.equipment.ring_of_incarceration": "˙ǝbuɐɹ ʞɔɐʇʇɐ ɹnoʎ uıɥʇıʍ sqoɯ ןןɐ puɐ noʎ uo ʇɔǝɟɟǝ uoıʇɐɹǝɔɹɐɔuI ʎןddɐ 'buıʞɐǝus uǝɥM", "l2hostility.item.equipment.ring_of_life": "ǝɔuo ʇɐ ɥʇןɐǝɥ xɐɯ ɹnoʎ ɟo %s%% uɐɥʇ ǝɹoɯ ǝsoן ʇou ןןıʍ noʎ", "l2hostility.item.equipment.ring_of_ocean": "ʇǝʍ ǝq sʎɐʍןɐ ןןıʍ noʎ", "l2hostility.item.equipment.ring_of_reflection": "˙pɐǝʇsuı noʎ ʇǝbɹɐʇ ʇɐɥʇ sǝıɯǝuǝ buıpunoɹɹns oʇ ʇı ʎןddɐ 'noʎ uo ʇɔǝɟɟǝ ǝʌıʇɐbǝu ɐ ʎןddɐ oʇ sǝıɹʇ ʇıɐɹʇ qoɯ ɐ uǝɥM", "l2hostility.item.equipment.witch_wand": "˙sʇıɐɹʇ uoıʇod ɯopuɐɹ ɯoɹɟ pǝʇɔǝןǝs sǝdʎ⟘ ˙ʞɔıןɔ ʇɥbıɹ uo suoıʇod ɥsɐןds ɯopuɐɹ ʍoɹɥ⟘", "l2hostility.item.spawner": "sןǝʌǝן ɥʇıʍ sqoɯ uʍɐds ɹǝbuoן ou uoıʇɔǝs ʞunɥɔ ɐ ǝʞɐɯ oʇ ןןɐ ɯǝɥʇ ןןıʞ ˙sqoɯ buoɹʇs uoɯɯnS", "l2hostility.item.wand.adder": "˙uoıʇɔǝɹıp ǝʇısoddo uı ʇɔǝןǝs oʇ ʇɟıɥs ssǝɹԀ ˙ʞuɐɹ ʇıɐɹʇ ʇɔǝןǝs oʇ sqoɯ ʞɔıןɔ ʇɥbıᴚ ˙ʇıɐɹʇ ʇɔǝןǝs oʇ sʞɔoןq ʞɔıןɔ ʇɥbıᴚ", "l2hostility.item.wand.ai": "˙IⱯ qoɯ ǝɹoʇsǝɹ ɹo ǝʌoɯǝɹ oʇ ʞɔıןɔ ʇɥbıᴚ", "l2hostility.item.wand.equipment": "˙nuǝɯ soıɹnɔ qoɯ uǝdo oʇ ʞɔıןɔ ʇɥbıɹ ʇɟıɥS ˙nuǝɯ ʇuǝɯdınbǝ qoɯ uǝdo oʇ ʞɔıןɔ ʇɥbıᴚ", "l2hostility.item.wand.target": "˙ɹǝɥʇo ɥɔɐǝ ʇɥbıɟ ɯǝɥʇ ǝʞɐɯ oʇ sqoɯ ᄅ ʞɔıןɔ ʇɥbıᴚ", "l2hostility.jei.envy": "ʇıɐɹʇ sıɥʇ ɟo sqoɯ buıןןıʞ ǝןıɥʍ ʎʌuƎ ɟo ǝsɹnƆ sdınbǝ noʎ uǝɥʍ sdoɹᗡ", "l2hostility.jei.gluttony": "sןǝʌǝן ɥʇıʍ sqoɯ buıןןıʞ ǝןıɥʍ ʎuoʇʇnן⅁ ɟo ǝsɹnƆ sdınbǝ noʎ uǝɥʍ sdoɹᗡ", "l2hostility.jei.loot_chance": "%s ʞuɐɹ %s ɹoɟ ǝɔuɐɥɔ %s", "l2hostility.jei.loot_title": "ʇooꞀ ʇıɐɹ⟘", "l2hostility.jei.min_health": "ɥʇןɐǝɥ xɐɯ ɹǝɥbıɥ ɹo %s ɥʇıʍ sqoɯ uo sdoɹᗡ", "l2hostility.jei.min_level": "ɹǝɥbıɥ ɹo %s ןǝʌǝן ɥʇıʍ sqoɯ uo sdoɹᗡ", "l2hostility.jei.no_trait": "ʇǝɯ suoıʇıpuoɔ uǝɥʍ doɹp oʇ ǝɔuɐɥɔ %s", "l2hostility.jei.other_trait": "%s ʞuɐɹ ʇɐ %s sǝɹınbǝᴚ", "l2hostility.jei.required": "sqoɯ buıןןıʞ ǝןıɥʍ %s dınbǝ oʇ noʎ sǝɹınbǝᴚ", "l2hostility.msg.ai": "˙%s oʇ IⱯoN ʇǝS :%s ǝɹnbıɟuoƆ", "l2hostility.msg.err_disallow": "ʎʇıʇuǝ sıɥʇ uo ǝןqɐɔıןddɐ ʇou ʇıɐɹ⟘", "l2hostility.msg.err_max": "ǝnןɐʌ xɐɯ pǝɥɔɐǝɹ ʎpɐǝɹןɐ ןǝʌǝן ʇıɐɹ⟘", "l2hostility.msg.select_trait": "%s :ʇıɐɹʇ pǝʇɔǝןǝS", "l2hostility.msg.set_target": "ʇɥbıɟ oʇ %s puɐ %s ʇǝS", "l2hostility.msg.set_trait": "%3$s ןǝʌǝן oʇ %2$s ʎʇıʇuǝ uo %1$s ʇıɐɹʇ ʇǝS", "l2hostility.msg.target_fail": "ʇɥbıɟ ʇouuɐɔ %s puɐ %s", "l2hostility.msg.target_record": "%s pǝpɹoɔǝᴚ", "l2hostility.patchouli.landing": "poɯ buıןɐɔs ʎʇןnɔıɟɟıp ǝʞıן-uoıdɯɐɥƆ oʇ ǝɯoɔןǝM", "l2hostility.patchouli.title": "ǝpın⅁ ʎʇıןıʇsoHᄅꞀ", "l2hostility.tooltip.ban_item": "bıɟuoɔ uı pǝןqɐsıp sı ɯǝʇı sıɥ⟘", "l2hostility.tooltip.banned": "˙pǝןqɐsıp sı ʇıɐɹʇ sıɥ⟘", "l2hostility.tooltip.disable": "˙ɥsıuɐʌ ןןıʍ ʇuǝɯʇuɐɥɔuǝ ʍǝu ʎuⱯ ˙%ss ɹoɟ pǝןqɐsıp sʇuǝɯʇuɐɥɔuǝ %s", "l2hostility.tooltip.legendary": "ʎɹɐpuǝbǝꞀ", "l2hostility.tooltip.level_cost": "%s :ʇsoɔ ןǝʌǝן qoW", "l2hostility.tooltip.lv": "%s˙ʌꞀ", "l2hostility.tooltip.min_level": "%s :ןǝʌǝן qoɯ ɯnɯıuıW", "l2hostility.tooltip.seal_time": "˙%ss sǝʞɐ⟘ ˙ןɐǝsun oʇ ǝsn pןoH", "l2hostility.tooltip.sealed_item": ":uıɥʇıʍ pǝןɐǝs ɯǝʇI", "l2hostility.tooltip.self_effect": " :ʇɔǝɟɟǝ snonuıʇuoɔ suıɐb qoW", "l2hostility.tooltip.shift": "uoıʇıpuoɔ uʍɐds ʍoɥs oʇ ⟘ℲIHS ssǝɹԀ", "l2hostility.tooltip.target_effect": " :ʇıɥ uo ʇɔǝɟɟǝ ʇɔıןɟuı qoW", "l2hostility.tooltip.weight": "%s :ʇɥbıǝM", "l2hostility.tooltip.witch_bottle": "˙%ss oʇ ǝsɐǝɹɔuı ʇsoɯ ʇⱯ ˙%s%% ʎq %ss uɐɥʇ ɹǝbuoן sʇɔǝɟɟǝ ןןɐ ɟo uoıʇɐɹnp ǝsɐǝɹɔuI", "l2hostility.tooltip.witch_charge": "˙%ss oʇ ǝsɐǝɹɔuı ʇsoɯ ʇⱯ ˙%s%% ʎq %ss uɐɥʇ ɹǝbuoן sʇɔǝɟɟǝ ןnɟɯɹɐɥ ןןɐ ɟo uoıʇɐɹnp ǝsɐǝɹɔuı 'ʎʇıʇuǝ ʇıɥ uǝɥM ˙ǝɹıɟ oʇ ʞɔıןɔ ʇɥbıᴚ", "l2hostility.tooltip.witch_eternal": "˙uoıʇɐɹnp ǝʇıuıɟuı sǝɯoɔǝq %ss uɐɥʇ ɹǝbuoן sʇɔǝɟɟǝ ןnɟɯɹɐɥ ןןɐ 'ʎʇıʇuǝ ʇıɥ uǝɥM ˙ǝɹıɟ oʇ ʞɔıןɔ ʇɥbıᴚ", "patchouli.l2hostility.landing": "ɹoɟ ǝɹɐdǝɹd oʇ ʇɐɥʍ ʍouʞ oʇ sʇıɐɹʇ qoɯ puɐ sɔıuɐɥɔǝɯ ǝɥʇ ʇno puıℲ", "patchouli.l2hostility.title": "ǝpın⅁ ʎʇıןıʇsoHᄅꞀ", "trait.l2hostility.adaptive": "ǝʌıʇdɐpⱯ", "trait.l2hostility.adaptive.desc": "˙sǝdʎʇ ǝbɐɯɐp ʇuǝɹǝɟɟıp %s ʇsɐן sǝzıɹoɯǝW ˙ǝɯıʇ ʎɹǝʌǝ ǝbɐɯɐp ǝsoɥʇ ɹoɟ uoıʇɔnpǝɹ ǝbɐɯɐp %s%% ʞɔɐʇs puɐ uǝʞɐʇ sǝdʎʇ ǝbɐɯɐp ǝzıɹoɯǝW", "trait.l2hostility.arena": "ɐuǝɹⱯ", "trait.l2hostility.arena.desc": "˙sıɥʇ ʎq pǝʇɔǝɟɟɐ ʇou sǝıʇıʇuǝ ɯoɹɟ ǝbɐɯɐp ǝunɯɯI ˙sʞɔoןq ʞɐǝɹq ɹo ǝɔɐןd ʇouuɐɔ ʇı punoɹɐ sɹǝʎɐןԀ", "trait.l2hostility.blindness": "ɹǝpuıןᗺ", "trait.l2hostility.corrosion": "uoısoɹɹoƆ", "trait.l2hostility.corrosion.desc": "ǝɔǝıd ɹǝd %s ʎq ǝbɐɯɐp ǝsɐǝɹɔuı 'sʇuǝɯdınbǝ ɥbnouǝ ʇ,uǝɹɐ ǝɹǝɥʇ uǝɥM ˙%s ʎq ssoן ʎʇıןıqɐɹnp ɹıǝɥʇ ǝsɐǝɹɔuı puɐ sʇuǝɯdınbǝ %s sʞɔıd ʎןɯopuɐɹ 'ʇǝbɹɐʇ ʇıɥ uǝɥM", "trait.l2hostility.counter_strike": "ǝʞıɹʇS ɹǝʇunoƆ", "trait.l2hostility.counter_strike.desc": "˙ǝʞıɹʇs ɹǝʇunoɔ ɐ ɯɹoɟɹǝd oʇ ʇdɯǝʇʇɐ ןןıʍ ʇı 'pǝʞɔɐʇʇɐ ɹǝʇɟⱯ", "trait.l2hostility.cursed": "pǝsɹnƆ", "trait.l2hostility.dementor": "ɹoʇuǝɯǝᗡ", "trait.l2hostility.dementor.desc": "˙ɹoɯɹɐ ssɐdʎq ǝbɐɯɐᗡ ˙ǝbɐɯɐp ןɐɔısʎɥd oʇ ǝunɯɯI", "trait.l2hostility.dispell": "ןןǝdsıᗡ", "trait.l2hostility.dispell.desc": "˙spuoɔǝs %s ɹoɟ ɯǝɥʇ uo sʇuǝɯʇuɐɥɔuǝ ǝןqɐsıp puɐ ʇuǝɯdınbǝ pǝʇuɐɥɔuǝ %s sʞɔıd ʎןɯopuɐᴚ ˙suoıʇɔǝʇoɹd ןɐɔıbɐɯ ssɐdʎq ǝbɐɯɐᗡ ˙ǝbɐɯɐp ɔıbɐɯ oʇ ǝunɯɯI", "trait.l2hostility.drain": "uıɐɹᗡ", "trait.l2hostility.drain.desc": "˙%ss oʇ ǝsɐǝɹɔuı ʇsoɯ ʇⱯ ˙%s ʎq uoıʇɐɹnp ɹıǝɥʇ ǝsɐǝɹɔuı puɐ 'sʇɔǝɟɟǝ ןnɟɯɹɐɥ ʎɹǝʌǝ ɹoɟ ǝbɐɯɐp ǝɹoɯ %s ןɐǝp 'sʇɔǝɟɟǝ ןɐıɔıɟǝuǝq %s ǝʌoɯǝɹ 'ʇǝbɹɐʇ ʇıɥ uǝɥM ˙ןǝʌǝן ǝɯɐs ɥʇıʍ ʇıɐɹʇ uoıʇod ɯopuɐɹ ɐ sʇuɐɹ⅁", "trait.l2hostility.erosion": "uoısoɹƎ", "trait.l2hostility.erosion.desc": "ǝɔǝıd ɹǝd %s ʎq ǝbɐɯɐp ǝsɐǝɹɔuı 'sʇuǝɯdınbǝ ɥbnouǝ ʇ,uǝɹɐ ǝɹǝɥʇ uǝɥM ˙%s ʎq ʎʇıןıqɐɹnp ɹıǝɥʇ ǝɔnpǝɹ puɐ sʇuǝɯdınbǝ %s sʞɔıd ʎןɯopuɐɹ 'ʇǝbɹɐʇ ʇıɥ uǝɥM", "trait.l2hostility.fiery": "ʎɹǝıℲ", "trait.l2hostility.fiery.desc": "˙ǝɹıɟ oʇ ǝunɯɯı qoɯ sǝʞɐW ˙spuoɔǝs %s ɹoɟ ʇǝbɹɐʇ ʞɔɐʇʇɐ puɐ ɹǝʞɔɐʇʇɐ ǝʇıubI", "trait.l2hostility.freezing": "buızǝǝɹℲ", "trait.l2hostility.gravity": "ʎʇıʌɐɹ⅁", "trait.l2hostility.gravity.desc": "ʇı punoɹɐ sqoɯ ɹoɟ ʎʇıʌɐɹb ǝsɐǝɹɔuI", "trait.l2hostility.grenade": "ǝpɐuǝɹ⅁", "trait.l2hostility.grenade.desc": "˙sɹɐǝddɐsıp ʇǝןןnq snoıʌǝɹd ǝɥʇ ɹǝʇɟɐ spuoɔǝs %s ʎɹǝʌǝ sʇǝןןnq ǝʌısoןdxǝ ʇooɥS", "trait.l2hostility.growth": "ɥʇʍoɹ⅁", "trait.l2hostility.growth.desc": "˙ʇıɐɹʇ ǝʇɐɹǝuǝbǝᴚ uıɐb ʎןןɐɔıʇɐɯoʇnⱯ ˙ɥʇןɐǝɥ ןןnɟ ʇɐ uǝɥʍ ɹǝbɹɐן ʍoɹb ןןıʍ ǝɯıןS", "trait.l2hostility.invisible": "ǝןqısıʌuI", "trait.l2hostility.killer_aura": "ɐɹnⱯ ɹǝןןıʞ", "trait.l2hostility.killer_aura.desc": "%ss ʎɹǝʌǝ ɹoɟ sʇɔǝɟɟǝ ʇıɐɹʇ ʎןddɐ puɐ sʞɔoןq %s uıɥʇıʍ ʇı buıʇǝbɹɐʇ sǝıʇıʇuǝ puɐ sɹǝʎɐןd oʇ ǝbɐɯɐp ɔıbɐɯ %s ןɐǝᗡ", "trait.l2hostility.levitation": "ɹǝʇɐʇıʌǝꞀ", "trait.l2hostility.moonwalk": "ʞןɐʍuooW", "trait.l2hostility.moonwalk.desc": "ʇı punoɹɐ sqoɯ ɹoɟ ʎʇıʌɐɹb ǝsɐǝɹɔǝᗡ", "trait.l2hostility.nausea": "ɹǝʇɹoʇsıᗡ", "trait.l2hostility.poison": "snouosıoԀ", "trait.l2hostility.protection": "pǝʇɔǝʇoɹԀ", "trait.l2hostility.pulling": "buıןןnԀ", "trait.l2hostility.pulling.desc": "˙sʞɔoןq %s uıɥʇıʍ ʇı oʇ ǝןıʇsoɥ sǝıʇıʇuǝ ןןnd ןןıʍ qoW", "trait.l2hostility.ragnarok": "ʞoɹɐubɐᴚ", "trait.l2hostility.ragnarok.desc": "˙ןɐǝsun oʇ %ss sǝʞɐʇ ɥɔıɥʍ 'ɯǝɥʇ ןɐǝs puɐ sʇuǝɯdınbǝ %s sʞɔıd ʎןɯopuɐɹ 'ʇǝbɹɐʇ ʇıɥ uǝɥM", "trait.l2hostility.reflect": "ʇɔǝןɟǝᴚ", "trait.l2hostility.reflect.desc": "ǝbɐɯɐp ןɐɔıbɐɯ %s%% sɐ ǝbɐɯɐp ןɐɔısʎɥd ʇɔǝɹıp ʇɔǝןɟǝᴚ", "trait.l2hostility.regenerate": "buıʇɐɹǝuǝbǝᴚ", "trait.l2hostility.regenerate.desc": "˙puoɔǝs ʎɹǝʌǝ ɥʇןɐǝɥ ןןnɟ ɟo %s%% sןɐǝH", "trait.l2hostility.repelling": "buıןןǝdǝᴚ", "trait.l2hostility.repelling.desc": "˙sǝןıʇɔǝظoɹd oʇ ǝunɯɯı puɐ 'sʞɔoןq %s uıɥʇıʍ ʇı oʇ ǝןıʇsoɥ sǝıʇıʇuǝ ʎɐʍɐ ɥsnd ןןıʍ qoW", "trait.l2hostility.reprint": "ʇuıɹdǝᴚ", "trait.l2hostility.reprint.desc": "ʇuıod ʇuǝɯʇuɐɥɔuǝ ɹǝd ǝbɐɯɐp ǝɹoɯ %s ןɐǝp puɐ 'sʇuǝɯʇuɐɥɔuǝ ʇǝbɹɐʇ ʎdoɔ ןןıʍ qoW", "trait.l2hostility.shulker": "ɹǝʞןnɥS", "trait.l2hostility.shulker.desc": "spuoɔǝs %s ʎɹǝʌǝ sʇǝןןnq ʇooɥS", "trait.l2hostility.slowness": "ʎɐɹʇS", "trait.l2hostility.soul_burner": "ɹǝuɹnᗺ ןnoS", "trait.l2hostility.speedy": "ʎpǝǝdS", "trait.l2hostility.split": "ʇıןdS", "trait.l2hostility.split.desc": "˙ʇıןds uǝɥʍ Ɩ ʎq ǝɔnpǝɹ ʇıɐɹʇ sıɥ⟘ ˙ʇıɐɹʇ ǝɯɐs ʇnq sןǝʌǝן ɟןɐɥ ɥʇıʍ ɟןǝsʇı ɟo ᄅ oʇuı ʇıןds ןןıʍ ʇı 'sǝıp qoɯ uǝɥM", "trait.l2hostility.tank": "ʎʞuɐ⟘", "trait.l2hostility.teleport": "ʇɹodǝןǝ⟘", "trait.l2hostility.teleport.desc": "˙sʇǝbɹɐʇ ʞɔɐɹʇ puɐ ǝbɐɯɐp ןɐɔısʎɥd pıoʌɐ oʇ ʇɹodǝןǝʇ oʇ ʇdɯǝʇʇɐ ןןıʍ qoW", "trait.l2hostility.undying": "buıʎpu∩", "trait.l2hostility.undying.desc": "˙sǝıp ʇı ǝɯıʇ ʎɹǝʌǝ ɥʇןɐǝɥ ןןnɟ oʇ ןɐǝɥ ןןıʍ qoW", "trait.l2hostility.weakness": "ɹǝuǝʞɐǝM", "trait.l2hostility.wither": "buıɹǝɥʇıM"}