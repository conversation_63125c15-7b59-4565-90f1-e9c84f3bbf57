{"advancements.l2hostility.advancements.hostility.abrahadabra.description": "<PERSON><PERSON><PERSON>", "advancements.l2hostility.advancements.hostility.abrahadabra.title": "The Finale", "advancements.l2hostility.advancements.hostility.breed.description": "Use a trait item on mobs", "advancements.l2hostility.advancements.hostility.breed.title": "Breeding Mobs", "advancements.l2hostility.advancements.hostility.detector.description": "Obtain Hostility Detector", "advancements.l2hostility.advancements.hostility.detector.title": "Safety Compass", "advancements.l2hostility.advancements.hostility.effect_kill_adaptive.description": "Use poison/wither/soul flame effect or fire on mobs with Adaptive and kill it", "advancements.l2hostility.advancements.hostility.effect_kill_adaptive.title": "Prevent Adaption", "advancements.l2hostility.advancements.hostility.effect_kill_regen.description": "Use curse effect on mobs with Regeneration and kill it", "advancements.l2hostility.advancements.hostility.effect_kill_regen.title": "Prevent Healing", "advancements.l2hostility.advancements.hostility.effect_kill_teleport.description": "Use incarceration effect on mobs with Teleport and kill it", "advancements.l2hostility.advancements.hostility.effect_kill_teleport.title": "Prevent Teleporting", "advancements.l2hostility.advancements.hostility.effect_kill_undead.description": "Use curse effect on mobs with Undying and kill it", "advancements.l2hostility.advancements.hostility.effect_kill_undead.title": "Prevent Reviving", "advancements.l2hostility.advancements.hostility.envy.description": "Obtain Curse of Envy", "advancements.l2hostility.advancements.hostility.envy.title": "I want that!", "advancements.l2hostility.advancements.hostility.glasses.description": "Obtain Detector <PERSON>es to find out invisible mobs", "advancements.l2hostility.advancements.hostility.glasses.title": "The Invisible Threats", "advancements.l2hostility.advancements.hostility.gluttony.description": "Obtain Curse of Gluttony", "advancements.l2hostility.advancements.hostility.gluttony.title": "Hostility Unlimited", "advancements.l2hostility.advancements.hostility.greed.description": "Obtain Curse of Greed", "advancements.l2hostility.advancements.hostility.greed.title": "The More the Better", "advancements.l2hostility.advancements.hostility.imagine_breaker.description": "Obtain <PERSON> Breaker", "advancements.l2hostility.advancements.hostility.imagine_breaker.title": "Reality Breakthrough", "advancements.l2hostility.advancements.hostility.ingot.description": "Obtain a Chaos Ingot", "advancements.l2hostility.advancements.hostility.ingot.title": "Pandora's Box", "advancements.l2hostility.advancements.hostility.kill_10_traits.description": "Kill a mob with 10 traits", "advancements.l2hostility.advancements.hostility.kill_10_traits.title": "Legend Slayer", "advancements.l2hostility.advancements.hostility.kill_5_traits.description": "Kill a mob with 5 traits", "advancements.l2hostility.advancements.hostility.kill_5_traits.title": "Legendary Battle", "advancements.l2hostility.advancements.hostility.kill_adaptive.description": "Kill a mob with Protection, Regeneration, Tanky, and Adaptive Trait", "advancements.l2hostility.advancements.hostility.kill_adaptive.title": "Counter-Defensive Measure", "advancements.l2hostility.advancements.hostility.kill_dementor.description": "Kill a mob with <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "advancements.l2hostility.advancements.hostility.kill_dementor.title": "Immunity Invalidator", "advancements.l2hostility.advancements.hostility.kill_first.description": "Kill a mob with traits", "advancements.l2hostility.advancements.hostility.kill_first.title": "Worthy Opponent", "advancements.l2hostility.advancements.hostility.kill_ragnarok.description": "Kill a mob with <PERSON> and <PERSON><PERSON><PERSON>", "advancements.l2hostility.advancements.hostility.kill_ragnarok.title": "The Final Battle", "advancements.l2hostility.advancements.hostility.kill_tanky.description": "Kill a mob with Protection and <PERSON><PERSON>", "advancements.l2hostility.advancements.hostility.kill_tanky.title": "Can Opener", "advancements.l2hostility.advancements.hostility.lust.description": "Obtain Curse of Lust", "advancements.l2hostility.advancements.hostility.lust.title": "Naked Corpse", "advancements.l2hostility.advancements.hostility.miracle.description": "Obtain Miracle Ingot", "advancements.l2hostility.advancements.hostility.miracle.title": "Miracle of the World", "advancements.l2hostility.advancements.hostility.patchouli.description": "Read the hostility guide", "advancements.l2hostility.advancements.hostility.patchouli.title": "Intro to L2Hostility", "advancements.l2hostility.advancements.hostility.pride.description": "Obtain Curse of Pride", "advancements.l2hostility.advancements.hostility.pride.title": "King of Hostility", "advancements.l2hostility.advancements.hostility.root.description": "Your survival guide", "advancements.l2hostility.advancements.hostility.root.title": "Welcome to L2Hostility", "advancements.l2hostility.advancements.hostility.sloth.description": "Obtain Curse of Sloth", "advancements.l2hostility.advancements.hostility.sloth.title": "I want a break", "advancements.l2hostility.advancements.hostility.trait.description": "Obtain a trait item", "advancements.l2hostility.advancements.hostility.trait.title": "Gate to the New World", "advancements.l2hostility.advancements.hostility.wrath.description": "Obtain Curse of Wrath", "advancements.l2hostility.advancements.hostility.wrath.title": "Revenge Time", "block.l2hostility.hostility_spawner": "Hostility Spawner", "config.jade.plugin_l2hostility.mob": "L2Hostility", "curios.identifier.hostility_curse": "L2Hostility - Curse", "curios.modifiers.hostility_curse": "When worn as Curse:", "death.attack.killer_aura": "%s was killed by killer aura", "death.attack.killer_aura.player": "%s was killed by %s's killer aura", "effect.l2hostility.antibuild": "Antibuild", "effect.l2hostility.antibuild.description": "Make player cannot place block.", "effect.l2hostility.gravity": "Gravity", "effect.l2hostility.gravity.description": "Increase entity gravity.", "effect.l2hostility.moonwalk": "Moonwalk", "effect.l2hostility.moonwalk.description": "Decrease entity gravity.", "enchantment.l2hostility.insulator": "Insulator", "enchantment.l2hostility.insulator.desc": "Reduce trait effects that pulls or pushes you", "enchantment.l2hostility.split_suppressor": "Split Suppressor", "enchantment.l2hostility.split_suppressor.desc": "Disable Split trait on enemies on hit", "enchantment.l2hostility.vanish": "Vanish", "enchantment.l2hostility.vanish.desc": "This item vanishes when on ground or in hand of survival / adventure player", "entity.l2hostility.hostility_bullet": "Hostility Bullet", "entity.l2hostility.hostility_charge": "Hostility Charge", "item.l2hostility.abrahadabra": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.l2hostility.ai_config_wand": "<PERSON>", "item.l2hostility.book_of_omniscience": "Book Of Omniscience", "item.l2hostility.book_of_reprint": "Book Of Reprint", "item.l2hostility.booster_potion": "<PERSON><PERSON><PERSON>", "item.l2hostility.bottle_of_curse": "Bottle Of Curse", "item.l2hostility.bottle_of_sanity": "Bottle Of Sanity", "item.l2hostility.chaos_ingot": "Chaos Ingot", "item.l2hostility.charm_of_looting_1": "Unpolished Looting Charm", "item.l2hostility.charm_of_looting_2": "Magical Looting Charm", "item.l2hostility.charm_of_looting_3": "Chaotic Looting Charm", "item.l2hostility.charm_of_looting_4": "Miraculous Looting Charm", "item.l2hostility.curse_of_envy": "Curse Of Envy", "item.l2hostility.curse_of_gluttony": "Curse Of Gluttony", "item.l2hostility.curse_of_greed": "Curse Of Greed", "item.l2hostility.curse_of_lust": "Curse Of Lust", "item.l2hostility.curse_of_pride": "Curse Of Pride", "item.l2hostility.curse_of_sloth": "Curse Of Sloth", "item.l2hostility.curse_of_wrath": "Curse Of Wrath", "item.l2hostility.detector_glasses": "Detector Glasses", "item.l2hostility.equipment_wand": "Equipment Wand", "item.l2hostility.eternal_witch_charge": "Eternal Witch Charge", "item.l2hostility.flaming_thorn": "Flaming Thorn", "item.l2hostility.greed_of_nidhoggur": "Greed Of Nidhoggur", "item.l2hostility.hostility_detector": "Hostility Detector", "item.l2hostility.hostility_essence": "Hostility Essence", "item.l2hostility.hostility_orb": "Hostility Orb", "item.l2hostility.imagine_breaker": "Imagine Breaker", "item.l2hostility.miracle_ingot": "Miracle Ingot", "item.l2hostility.miracle_powder": "<PERSON>", "item.l2hostility.pocket_of_restoration": "Pocket Of Restoration", "item.l2hostility.ring_of_corrosion": "Ring Of Corrosion", "item.l2hostility.ring_of_divinity": "Ring Of Divinity", "item.l2hostility.ring_of_healing": "Ring Of Healing", "item.l2hostility.ring_of_incarceration": "Ring Of Incarceration", "item.l2hostility.ring_of_life": "Ring Of Life", "item.l2hostility.ring_of_ocean": "Ring Of Ocean", "item.l2hostility.ring_of_reflection": "Ring Of Reflection", "item.l2hostility.sealed_item": "Sealed Item", "item.l2hostility.target_select_wand": "Target Select Wand", "item.l2hostility.trait_adder_wand": "<PERSON><PERSON><PERSON> Wan<PERSON>", "item.l2hostility.witch_charge": "Witch Charge", "item.l2hostility.witch_droplet": "Witch Droplet", "item.l2hostility.witch_wand": "Witch Wand", "itemGroup.l2hostility.hostility": "L2 Hostility", "l2hostility.boss_event": "Hostility Clearing Progress: %s/%s", "l2hostility.command.mob.success": "Performed actions on %s mobs", "l2hostility.command.player.fail": "Command has no target or no effect", "l2hostility.command.player.get_base": "%s has base difficulty level %s", "l2hostility.command.player.get_dim": "%s has visited %s dimensions", "l2hostility.command.player.get_total": "%s has overall difficulty level %s", "l2hostility.command.player.success": "Performed actions on %s players", "l2hostility.command.player.trait_cap": "The max rank %s has killed is rank %s", "l2hostility.command.region.clear": "Section Cleared", "l2hostility.command.region.count": "Performed Actions for %s chunk sections", "l2hostility.command.region.get_base": "Target section has base difficulty level %s", "l2hostility.command.region.get_scale": "Target section has difficulty scale %s", "l2hostility.command.region.get_total": "Target section has total difficulty level %s", "l2hostility.command.region.local_off": "Section difficulty is turned off", "l2hostility.command.region.not_clear": "Section Not Cleared", "l2hostility.command.region.success": "Action Succeed", "l2hostility.info.chunk_level": "Chunk base difficulty: %s", "l2hostility.info.chunk_scale": "Chunk difficulty scale: %s", "l2hostility.info.clear": "Chunk Section difficulty cleared", "l2hostility.info.player_cap": "Mob trait rank limit: %s", "l2hostility.info.player_detail.adaptive": "Adaptive level: %s", "l2hostility.info.player_detail.dim": "Visited dimension bonus: %s", "l2hostility.info.player_detail.external": "External difficulty: %s", "l2hostility.info.player_detail.item": "Item bonus: %s", "l2hostility.info.player_exp": "Difficulty progress: %s%%", "l2hostility.info.player_level": "Player difficulty level: %s", "l2hostility.info.reward": "Obtained %s rewards", "l2hostility.info.section_detail.adaptive": "Adaptive level: %s", "l2hostility.info.section_detail.biome": "Biome level: %s", "l2hostility.info.section_detail.dim": "Dimension level: %s", "l2hostility.info.section_detail.distance": "Distance bonus: %s", "l2hostility.info.title": "Difficulty Information", "l2hostility.item.book.copy": "Merge it with enchanted book in anvil to copy.", "l2hostility.item.book.everything_forbidden": "%s is not obtainable", "l2hostility.item.book.everything_invalid": "Name is not ID of an enchantment", "l2hostility.item.book.everything_ready": "Enchantment %s cost %s levels", "l2hostility.item.book.everything_shift": "Shift right click with it to turn it into a book with all enchantments obtainable from enchantment table.", "l2hostility.item.book.everything_use": "Rename this item with ID of an enchantment obtainable from enchantment table, then right click to generate a book of that enchantment, costing player experience.", "l2hostility.item.consumable.bottle_of_curse": "Increase player difficulty by %s", "l2hostility.item.consumable.bottle_of_sanity": "Clear all base player difficulty", "l2hostility.item.consumable.orb": "Make %sx%sx%s chunk sections no longer spawn mobs with levels.", "l2hostility.item.equipment.abrahadabra": "When a mob trait tries to apply a trait effect on you, apply it to surrounding enemies that target you instead.", "l2hostility.item.equipment.curse_add_level": "Gain %s extra difficulty level", "l2hostility.item.equipment.curse_no_drop": "Mobs you kill will not drop hostility loot", "l2hostility.item.equipment.curse_of_envy": "Get trait items when you kill mobs with traits, with a chance of %s%% per trait rank", "l2hostility.item.equipment.curse_of_gluttony": "Get Bottle of Curse when you kill mobs with level, with a chance of %s%% per level", "l2hostility.item.equipment.curse_of_greed": "Mobs you kill will drop +%s%% hostility loot", "l2hostility.item.equipment.curse_of_lust": "Mobs you kill will drop all equipments", "l2hostility.item.equipment.curse_of_pride": "Gain %s%% health and %s%% attack damage per difficulty level", "l2hostility.item.equipment.curse_of_sloth": "You will not gain difficulty by killing mobs", "l2hostility.item.equipment.curse_of_wrath": "Gain %s%% attack damage per difficulty level difference against mobs with higher level than you.", "l2hostility.item.equipment.curse_trait_cheap": "Mob traits will be +%s%% more frequent", "l2hostility.item.equipment.flame_thorn": "When you damage a mob, inflict Soul Flame with level equals to total number of effects that mob has, for %ss", "l2hostility.item.equipment.glasses": "Allow you to see invisible mobs, and see mobs when you have blindness or darkness effects", "l2hostility.item.equipment.imagine_breaker": "All your melee damage bypass magical protection.", "l2hostility.item.equipment.looting": "Enables some hostility trait drops. Check JEI for detail.", "l2hostility.item.equipment.nidhoggur": "Mobs you kill will drop +%s%% loot per mob level", "l2hostility.item.equipment.platinum_star": "All your melee damage bypass damage cool down.", "l2hostility.item.equipment.pocket_of_restoration": "Automatically put sealed item inside, unseal it, and put it back when finished.", "l2hostility.item.equipment.ring_of_corrosion": "When you deal damage, damage all their equipments by %s%% of max durability.", "l2hostility.item.equipment.ring_of_corrosion_neg": "When you take damage, damage all your equipments by %s%% of max durability.", "l2hostility.item.equipment.ring_of_divinity": "Immune to magic damage. Gets permanent Cleanse effect", "l2hostility.item.equipment.ring_of_healing": "Heals %s%% of max health every second.", "l2hostility.item.equipment.ring_of_incarceration": "When sneaking, apply Incarceration effect on you and all mobs within your attack range.", "l2hostility.item.equipment.ring_of_life": "You will not lose more than %s%% of your max health at once", "l2hostility.item.equipment.ring_of_ocean": "You will always be wet", "l2hostility.item.equipment.ring_of_reflection": "When a mob trait tries to apply a negative effect on you, apply it to surrounding enemies that target you instead.", "l2hostility.item.equipment.witch_wand": "Throw random splash potions on right click. Types selected from random potion traits.", "l2hostility.item.spawner": "Summon strong mobs. Kill them all to make a chunk section no longer spawn mobs with levels", "l2hostility.item.wand.adder": "Right click blocks to select trait. Right click mobs to select trait rank. Press shift to select in opposite direction.", "l2hostility.item.wand.ai": "Right click to remove or restore mob AI.", "l2hostility.item.wand.equipment": "Right click to open mob equipment menu. Shift right click to open mob curios menu.", "l2hostility.item.wand.target": "Right click 2 mobs to make them fight each other.", "l2hostility.jei.envy": "Drops when you equips Curse of Envy while killing mobs of this trait", "l2hostility.jei.gluttony": "Drops when you equips Curse of Gluttony while killing mobs with levels", "l2hostility.jei.loot_chance": "%s chance for %s rank %s", "l2hostility.jei.loot_title": "<PERSON><PERSON><PERSON>", "l2hostility.jei.min_health": "Drops on mobs with %s or higher max health", "l2hostility.jei.min_level": "Drops on mobs with level %s or higher", "l2hostility.jei.no_trait": "%s chance to drop when conditions met", "l2hostility.jei.other_trait": "Requires %s at rank %s", "l2hostility.jei.required": "Requires you to equip %s while killing mobs", "l2hostility.msg.ai": "Configure %s: Set NoAI to %s.", "l2hostility.msg.err_disallow": "Trait not applicable on this entity", "l2hostility.msg.err_max": "Trait level already reached max value", "l2hostility.msg.select_trait": "Selected trait: %s", "l2hostility.msg.set_target": "Set %s and %s to fight", "l2hostility.msg.set_trait": "Set trait %1$s on entity %2$s to level %3$s", "l2hostility.msg.target_fail": "%s and %s cannot fight", "l2hostility.msg.target_record": "Recorded %s", "l2hostility.patchouli.landing": "Welcome to Champion-like difficulty scaling mod", "l2hostility.patchouli.title": "L2Hostility Guide", "l2hostility.tooltip.ban_item": "This item is disabled in config", "l2hostility.tooltip.banned": "This trait is disabled.", "l2hostility.tooltip.disable": "%s enchantments disabled for %ss. Any new enchantment will vanish.", "l2hostility.tooltip.legendary": "Legendary", "l2hostility.tooltip.level_cost": "Mob level cost: %s", "l2hostility.tooltip.lv": "Lv.%s", "l2hostility.tooltip.min_level": "Minimum mob level: %s", "l2hostility.tooltip.seal_time": "Hold use to unseal. Takes %ss.", "l2hostility.tooltip.sealed_item": "Item sealed within:", "l2hostility.tooltip.self_effect": "<PERSON><PERSON> gains continuous effect: ", "l2hostility.tooltip.shift": "Press SHIFT to show spawn condition", "l2hostility.tooltip.target_effect": "<PERSON>b inflict effect on hit: ", "l2hostility.tooltip.weight": "Weight: %s", "l2hostility.tooltip.witch_bottle": "Increase duration of all effects longer than %ss by %s%%. At most increase to %ss.", "l2hostility.tooltip.witch_charge": "Right click to fire. When hit entity, increase duration of all harmful effects longer than %ss by %s%%. At most increase to %ss.", "l2hostility.tooltip.witch_eternal": "Right click to fire. When hit entity, all harmful effects longer than %ss becomes infinite duration.", "patchouli.l2hostility.landing": "Find out the mechanics and mob traits to know what to prepare for", "patchouli.l2hostility.title": "L2Hostility Guide", "trait.l2hostility.adaptive": "Adaptive", "trait.l2hostility.adaptive.desc": "Memorize damage types taken and stack %s%% damage reduction for those damage every time. Memorizes last %s different damage types.", "trait.l2hostility.arena": "Arena", "trait.l2hostility.arena.desc": "Players around it cannot place or break blocks. Immune damage from entities not affected by this.", "trait.l2hostility.blindness": "Blinder", "trait.l2hostility.corrosion": "Corrosion", "trait.l2hostility.corrosion.desc": "When hit target, randomly picks %s equipments and increase their durability loss by %s. When there aren't enough equipments, increase damage by %s per piece", "trait.l2hostility.counter_strike": "Counter Strike", "trait.l2hostility.counter_strike.desc": "After attacked, it will attempt to perform a counter strike.", "trait.l2hostility.cursed": "Cursed", "trait.l2hostility.dementor": "Dementor", "trait.l2hostility.dementor.desc": "Immune to physical damage. Damage bypass armor.", "trait.l2hostility.dispell": "<PERSON><PERSON>ell", "trait.l2hostility.dispell.desc": "Immune to magic damage. Damage bypass magical protections. Randomly picks %s enchanted equipment and disable enchantments on them for %s seconds.", "trait.l2hostility.drain": "<PERSON><PERSON>", "trait.l2hostility.drain.desc": "Grants a random potion trait with same level. When hit target, remove %s beneficial effects, deal %s more damage for every harmful effects, and increase their duration by %s. At most increase to %ss.", "trait.l2hostility.erosion": "E<PERSON>ion", "trait.l2hostility.erosion.desc": "When hit target, randomly picks %s equipments and reduce their durability by %s. When there aren't enough equipments, increase damage by %s per piece", "trait.l2hostility.fiery": "<PERSON><PERSON>", "trait.l2hostility.fiery.desc": "Ignite attacker and attack target for %s seconds. Makes mob immune to fire.", "trait.l2hostility.freezing": "Freezing", "trait.l2hostility.gravity": "Gravity", "trait.l2hostility.gravity.desc": "Increase gravity for mobs around it", "trait.l2hostility.grenade": "Grenade", "trait.l2hostility.grenade.desc": "Shoot explosive bullets every %s seconds after the previous bullet disappears.", "trait.l2hostility.growth": "Growth", "trait.l2hostility.growth.desc": "Slime will grow larger when at full health. Automatically gain Regenerate trait.", "trait.l2hostility.invisible": "Invisible", "trait.l2hostility.killer_aura": "<PERSON>", "trait.l2hostility.killer_aura.desc": "Deal %s magic damage to players and entities targeting it within %s blocks and apply trait effects for every %ss", "trait.l2hostility.levitation": "Levitater", "trait.l2hostility.moonwalk": "Moonwalk", "trait.l2hostility.moonwalk.desc": "Decrease gravity for mobs around it", "trait.l2hostility.nausea": "Distorter", "trait.l2hostility.poison": "Poisonous", "trait.l2hostility.protection": "Protected", "trait.l2hostility.pulling": "Pulling", "trait.l2hostility.pulling.desc": "Mob will pull entities hostile to it within %s blocks.", "trait.l2hostility.ragnarok": "<PERSON><PERSON><PERSON>", "trait.l2hostility.ragnarok.desc": "When hit target, randomly picks %s equipments and seal them, which takes %ss to unseal.", "trait.l2hostility.reflect": "Reflect", "trait.l2hostility.reflect.desc": "Reflect direct physical damage as %s%% magical damage", "trait.l2hostility.regenerate": "Regenerating", "trait.l2hostility.regenerate.desc": "Heals %s%% of full health every second.", "trait.l2hostility.repelling": "Repelling", "trait.l2hostility.repelling.desc": "Mob will push away entities hostile to it within %s blocks, and immune to projectiles.", "trait.l2hostility.reprint": "Reprint", "trait.l2hostility.reprint.desc": "Mob will copy target enchantments, and deal %s more damage per enchantment point", "trait.l2hostility.shulker": "<PERSON><PERSON><PERSON>", "trait.l2hostility.shulker.desc": "Shoot bullets every %s seconds", "trait.l2hostility.slowness": "Stray", "trait.l2hostility.soul_burner": "Soul Burner", "trait.l2hostility.speedy": "<PERSON>y", "trait.l2hostility.split": "Split", "trait.l2hostility.split.desc": "When mob dies, it will split into 2 of itself with half levels but same trait. This trait reduce by 1 when split.", "trait.l2hostility.tank": "Tanky", "trait.l2hostility.teleport": "Teleport", "trait.l2hostility.teleport.desc": "<PERSON><PERSON> will attempt to teleport to avoid physical damage and track targets.", "trait.l2hostility.undying": "Undying", "trait.l2hostility.undying.desc": "<PERSON><PERSON> will heal to full health every time it dies.", "trait.l2hostility.weakness": "<PERSON><PERSON><PERSON>", "trait.l2hostility.wither": "Withering"}