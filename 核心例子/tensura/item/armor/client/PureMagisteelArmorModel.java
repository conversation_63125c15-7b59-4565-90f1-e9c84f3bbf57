package com.github.manasmods.tensura.item.armor.client;

import com.github.manasmods.tensura.item.armor.PureMagisteelArmorItem;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class PureMagisteelArmorModel extends AnimatedGeoModel<PureMagisteelArmorItem> {
   public ResourceLocation getModelResource(PureMagisteelArmorItem object) {
      return new ResourceLocation("tensura", "geo/armor/pure_magisteel_armor.geo.json");
   }

   public ResourceLocation getTextureResource(PureMagisteelArmorItem object) {
      return new ResourceLocation("tensura", "textures/models/armor/pure_magisteel_layer_0.png");
   }

   public ResourceLocation getAnimationResource(PureMagisteelArmorItem item) {
      return new ResourceLocation("tensura", "animations/armor.animation.json");
   }
}
