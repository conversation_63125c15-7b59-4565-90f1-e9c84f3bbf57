package com.github.manasmods.tensura.item.custom;

import com.github.manasmods.tensura.api.entity.subclass.ILivingPartEntity;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestTotemDisplayPacket;
import com.github.manasmods.tensura.util.TensuraRarity;
import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Multimap;
import java.util.UUID;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.animal.horse.AbstractHorse;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item.Properties;
import net.minecraftforge.network.PacketDistributor;

public class OrbOfDominationItem extends Item {
   protected static final UUID DOMINATION = UUID.fromString("dfff1a4a-93a1-11ee-b9d1-0242ac120002");

   public OrbOfDominationItem() {
      super((new Properties()).m_41497_(TensuraRarity.UNIQUE).m_41491_(TensuraCreativeTab.GEAR).m_41486_().m_41487_(1));
   }

   public Multimap<Attribute, AttributeModifier> getAttributeModifiers(EquipmentSlot pEquipmentSlot, ItemStack stack) {
      return pEquipmentSlot != EquipmentSlot.MAINHAND ? ImmutableMultimap.of() : ImmutableMultimap.builder().put(Attributes.f_22281_, new AttributeModifier(DOMINATION, "Domination Orb Modifier", -0.8D, Operation.MULTIPLY_TOTAL)).build();
   }

   public boolean m_7579_(ItemStack pStack, LivingEntity pTarget, LivingEntity pAttacker) {
      if (pAttacker instanceof Player) {
         Player player = (Player)pAttacker;
         if (player.m_7500_() || TensuraEPCapability.getEP(pTarget) < 800000.0D) {
            label30: {
               pTarget = ILivingPartEntity.checkForHead(pTarget);
               if (pTarget instanceof AbstractHorse) {
                  AbstractHorse horse = (AbstractHorse)pTarget;
                  if (!horse.m_30614_()) {
                     horse.m_30637_(player);
                     if (horse instanceof TensuraHorseEntity) {
                        TensuraHorseEntity tensuraHorse = (TensuraHorseEntity)horse;
                        tensuraHorse.setSitting(false);
                     }

                     horse.m_21573_().m_26573_();
                     horse.m_6710_((LivingEntity)null);
                     horse.f_19853_.m_7605_(horse, (byte)7);
                     break label30;
                  }
               }

               if (pTarget instanceof TamableAnimal) {
                  TamableAnimal tamableAnimal = (TamableAnimal)pTarget;
                  if (!tamableAnimal.m_21824_()) {
                     tamableAnimal.m_21828_(player);
                     tamableAnimal.m_21573_().m_26573_();
                     tamableAnimal.m_6710_((LivingEntity)null);
                     tamableAnimal.m_21839_(false);
                     tamableAnimal.f_19853_.m_7605_(tamableAnimal, (byte)7);
                  }
               }
            }

            TensuraEPCapability.getFrom(pTarget).ifPresent((cap) -> {
               cap.setTemporaryOwner(pAttacker.m_20148_());
            });
            TensuraEPCapability.sync(pTarget);
            if (player instanceof ServerPlayer) {
               ServerPlayer serverPlayer = (ServerPlayer)player;
               TensuraNetwork.INSTANCE.send(PacketDistributor.PLAYER.with(() -> {
                  return serverPlayer;
               }), new RequestTotemDisplayPacket(pStack));
            }

            if (!player.m_150110_().f_35937_) {
               pStack.m_41774_(1);
            }
         }
      }

      return super.m_7579_(pStack, pTarget, pAttacker);
   }
}
