package com.github.manasmods.tensura.command;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.mojang.brigadier.tree.LiteralCommandNode;
import net.minecraft.ChatFormatting;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.commands.arguments.MessageArgument;
import net.minecraft.commands.arguments.MessageArgument.ChatMessage;
import net.minecraft.network.chat.ChatType;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.OutgoingPlayerChatMessage;
import net.minecraft.network.chat.Style;
import net.minecraft.network.chat.ChatType.Bound;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.server.players.PlayerList;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class TelepathyCommand {
   @SubscribeEvent
   public static void register(RegisterCommandsEvent e) {
      LiteralCommandNode<CommandSourceStack> command = e.getDispatcher().register((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("telepathy").then(Commands.m_82127_("player").then(Commands.m_82129_("target", EntityArgument.m_91466_()).then(Commands.m_82129_("message", MessageArgument.m_96832_()).executes((context) -> {
         ChatMessage message = MessageArgument.m_232163_(context, "message");

         try {
            return sendMessage((CommandSourceStack)context.getSource(), EntityArgument.m_91474_(context, "target"), message);
         } catch (Exception var3) {
            message.m_241074_((CommandSourceStack)context.getSource());
            throw var3;
         }
      }))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("subordinate").then(Commands.m_82127_("stay").executes((context) -> {
         return stayCommand((CommandSourceStack)context.getSource());
      }))).then(Commands.m_82127_("follow").executes((context) -> {
         return followCommand((CommandSourceStack)context.getSource());
      }))).then(Commands.m_82127_("wander").executes((context) -> {
         return wanderCommand((CommandSourceStack)context.getSource());
      }))));
      e.getDispatcher().register((LiteralArgumentBuilder)Commands.m_82127_("t").redirect(command));
   }

   private static int sendMessage(CommandSourceStack pSource, ServerPlayer serverplayer, ChatMessage pChatMessage) {
      if (noTelepathy(serverplayer)) {
         pSource.m_81352_(Component.m_237115_("tensura.telepathy.no_telepathy"));
         return 1;
      } else {
         Bound bound = ChatType.m_241073_(ChatType.f_240674_, pSource);
         pChatMessage.m_241987_(pSource, (message) -> {
            OutgoingPlayerChatMessage chatMessage = OutgoingPlayerChatMessage.m_242676_(message);
            boolean filtered = message.m_243059_();
            Entity entity = pSource.m_81373_();
            Bound targetName = ChatType.m_241073_(ChatType.f_240668_, pSource).m_241018_(serverplayer.m_5446_());
            pSource.m_243079_(chatMessage, false, targetName);
            boolean shouldFilter = pSource.m_243061_(serverplayer);
            serverplayer.m_243093_(chatMessage, shouldFilter, bound);
            if (filtered && shouldFilter && serverplayer != entity) {
               pSource.m_243053_(PlayerList.f_243017_);
            }

            chatMessage.m_241051_(pSource.m_81377_().m_6846_());
         });
         return 1;
      }
   }

   public static int stayCommand(CommandSourceStack pSource) {
      Player player = pSource.m_230896_();
      if (player != null) {
         if (noTelepathy(player)) {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.no_telepathy"));
            return 1;
         }

         Mob mob = (Mob)SkillHelper.getTargetingEntity(Mob.class, player, 30.0D, 0.2D, false, false);
         if (mob != null && SkillHelper.isSubordinate(player, mob)) {
            SkillHelper.setStay(mob);
            player.m_5661_(Component.m_237110_("tensura.message.pet.stay", new Object[]{mob.m_5446_()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
         } else {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.subordinate.not_found"));
         }
      }

      return 1;
   }

   public static int followCommand(CommandSourceStack pSource) {
      Player player = pSource.m_230896_();
      if (player != null) {
         if (noTelepathy(player)) {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.no_telepathy"));
            return 1;
         }

         Mob mob = (Mob)SkillHelper.getTargetingEntity(Mob.class, player, 30.0D, 0.2D, false, false);
         if (mob != null && SkillHelper.isSubordinate(player, mob)) {
            SkillHelper.setFollow(mob);
            player.m_5661_(Component.m_237110_("tensura.message.pet.follow", new Object[]{mob.m_5446_()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
         } else {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.subordinate.not_found"));
         }
      }

      return 1;
   }

   public static int wanderCommand(CommandSourceStack pSource) {
      Player player = pSource.m_230896_();
      if (player != null) {
         if (noTelepathy(player)) {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.no_telepathy"));
            return 1;
         }

         Mob mob = (Mob)SkillHelper.getTargetingEntity(Mob.class, player, 30.0D, 0.2D, false, false);
         if (mob != null && SkillHelper.isSubordinate(player, mob)) {
            SkillHelper.setWander(mob);
            player.m_5661_(Component.m_237110_("tensura.message.pet.wander", new Object[]{mob.m_5446_()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
         } else {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.subordinate.not_found"));
         }
      }

      return 1;
   }

   public static boolean noTelepathy(Player player) {
      return !SkillUtils.hasSkill(player, (ManasSkill)CommonSkills.TELEPATHY.get());
   }
}
