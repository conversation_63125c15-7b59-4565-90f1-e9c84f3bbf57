package com.github.manasmods.tensura.command;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class EvolveCommand {
   @SubscribeEvent
   public static void register(RegisterCommandsEvent e) {
      e.getDispatcher().register((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("evolve").then(Commands.m_82127_("demonlord").executes((context) -> {
         return evolveTrueDemonLord((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_());
      }))).then(Commands.m_82127_("hero").executes((context) -> {
         return evolveTrueHero((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_());
      })));
   }

   private static int evolveTrueDemonLord(CommandSourceStack stack, Player player) {
      TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
         Level level = player.m_9236_();
         int requirement = level.m_46469_().m_46215_(TensuraGameRules.DEMON_LORD_AWAKEN);
         if (!cap.isDemonLordSeed()) {
            stack.m_81352_(Component.m_237115_("tensura.evolve.demon_lord.not_seed"));
         } else if (RaceHelper.shouldNamingStopAwakening(player)) {
            stack.m_81352_(Component.m_237115_("tensura.evolve.demon_lord.seed_lost"));
         } else if (cap.getSoulPoints() / 1000 < requirement) {
            stack.m_81352_(Component.m_237115_("tensura.evolve.demon_lord.lack_soul"));
         } else if (cap.isTrueDemonLord()) {
            stack.m_81352_(Component.m_237115_("tensura.evolve.demon_lord.already"));
         } else if (cap.isTrueHero()) {
            stack.m_81352_(Component.m_237115_("tensura.evolve.demon_lord.hero"));
         } else {
            cap.setSoulPoints(cap.getSoulPoints() - requirement * 1000);
            cap.setTrueDemonLord(true);
            RaceHelper.awakening(player, false);
         }

      });
      return 1;
   }

   private static int evolveTrueHero(CommandSourceStack stack, Player player) {
      TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
         if (!cap.isHeroEgg()) {
            stack.m_81352_(Component.m_237115_("tensura.evolve.hero.not_egg"));
         } else if (RaceHelper.shouldNamingStopAwakening(player)) {
            stack.m_81352_(Component.m_237115_("tensura.evolve.hero.egg_lost"));
         } else if (cap.isTrueDemonLord()) {
            stack.m_81352_(Component.m_237115_("tensura.evolve.hero.demon_lord"));
         } else if (cap.isTrueHero()) {
            stack.m_81352_(Component.m_237115_("tensura.evolve.hero.already"));
         } else if (!RaceHelper.fightingBossForHero(player)) {
            stack.m_81352_(Component.m_237115_("tensura.evolve.hero.boss_requirement"));
         } else {
            cap.setTrueHero(true);
            RaceHelper.awakening(player, true);
         }

      });
      return 1;
   }
}
