package com.github.manasmods.tensura.command.argument;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.mojang.brigadier.StringReader;
import com.mojang.brigadier.arguments.ArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import com.mojang.brigadier.exceptions.DynamicCommandExceptionType;
import com.mojang.brigadier.suggestion.SuggestionProvider;
import com.mojang.brigadier.suggestion.Suggestions;
import com.mojang.brigadier.suggestion.SuggestionsBuilder;
import java.util.Collection;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.SharedSuggestionProvider;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.registries.IForgeRegistry;

public class RaceArgument implements ArgumentType<Race> {
   private static final DynamicCommandExceptionType ERROR_INVALID_VALUE = new DynamicCommandExceptionType((o) -> {
      return Component.m_237115_("tensura.argument.race.invalid");
   });

   public Race parse(StringReader reader) throws CommandSyntaxException {
      String remaining = reader.getRemaining();
      String registryName = remaining.contains(" ") ? remaining.split(" ")[0] : remaining;
      reader.setCursor(reader.getString().indexOf(registryName) + registryName.length());
      return (Race)((IForgeRegistry)TensuraRaces.RACE_REGISTRY.get()).getValues().stream().filter((race) -> {
         return ((IForgeRegistry)TensuraRaces.RACE_REGISTRY.get()).getKey(race).toString().equalsIgnoreCase(registryName);
      }).findFirst().orElseThrow(() -> {
         return ERROR_INVALID_VALUE.create(registryName);
      });
   }

   public <S> CompletableFuture<Suggestions> listSuggestions(CommandContext<S> context, SuggestionsBuilder builder) {
      return context.getSource() instanceof SharedSuggestionProvider ? SharedSuggestionProvider.m_82957_(((IForgeRegistry)TensuraRaces.RACE_REGISTRY.get()).getValues().stream().map((race) -> {
         return ((IForgeRegistry)TensuraRaces.RACE_REGISTRY.get()).getKey(race);
      }), builder) : Suggestions.empty();
   }

   public static RaceArgument race() {
      return new RaceArgument();
   }

   public static Race getRace(CommandContext<CommandSourceStack> context, String name) {
      return (Race)context.getArgument(name, Race.class);
   }

   public Collection<String> getExamples() {
      return (Collection)Stream.of(TensuraRaces.HUMAN.getId(), TensuraRaces.SLIME.getId()).map(ResourceLocation::toString).collect(Collectors.toList());
   }

   public static SuggestionProvider<CommandSourceStack> getNextEvolutions() {
      return (context, builder) -> {
         return SharedSuggestionProvider.m_82957_(((IForgeRegistry)TensuraRaces.RACE_REGISTRY.get()).getValues().stream().filter((race) -> {
            Player player = ((CommandSourceStack)context.getSource()).m_230896_();
            if (player == null) {
               return false;
            } else {
               Race originalRace = TensuraPlayerCapability.getRace(player);
               return originalRace != null && !originalRace.getNextEvolutions(player).isEmpty() ? originalRace.getNextEvolutions(player).contains(race) : false;
            }
         }).map((race) -> {
            return ((IForgeRegistry)TensuraRaces.RACE_REGISTRY.get()).getKey(race);
         }), builder);
      };
   }
}
