package com.github.manasmods.tensura.command;

import com.github.manasmods.tensura.util.PermissionHelper;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;
import net.minecraftforge.server.permission.events.PermissionGatherEvent.Nodes;
import net.minecraftforge.server.permission.nodes.PermissionNode;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class TensuraPermissions {
   public static final PermissionNode<Boolean> PLAYER_EDIT_RESET = PermissionHelper.createNode("edit.reset", true, 2);
   public static final PermissionNode<Boolean> PLAYER_EDIT_RACE_SELF = PermissionHelper.createNode("race.edit", true, 2);
   public static final PermissionNode<Boolean> PLAYER_EDIT_RACE_OTHERS = PermissionHelper.createNode("race.edit.others", true, 2);
   public static final PermissionNode<Boolean> PLAYER_CHECK_RACE = PermissionHelper.createNode("race.check", true, 2);
   public static final PermissionNode<Boolean> PLAYER_EDIT_AWAKENING = PermissionHelper.createNode("awakening.edit", true, 2);
   public static final PermissionNode<Boolean> PLAYER_CHECK_AWAKENING = PermissionHelper.createNode("awakening.check", true, 2);
   public static final PermissionNode<Boolean> PLAYER_EDIT_SKILL = PermissionHelper.createNode("skill.edit", true, 2);
   public static final PermissionNode<Boolean> PLAYER_CHECK_SKILL = PermissionHelper.createNode("skill.check", true, 2);
   public static final PermissionNode<Boolean> PLAYER_EDIT_STAT = PermissionHelper.createNode("stat.edit", true, 2);
   public static final PermissionNode<Boolean> PLAYER_CHECK_STAT = PermissionHelper.createNode("stat.check", true, 2);
   public static final PermissionNode<Boolean> PLAYER_EDIT_OWNER = PermissionHelper.createNode("owner.edit", true, 2);

   @SubscribeEvent
   public static void registerPermissions(Nodes e) {
      e.addNodes(new PermissionNode[]{PLAYER_EDIT_RESET, PLAYER_CHECK_RACE, PLAYER_EDIT_RACE_SELF, PLAYER_EDIT_RACE_OTHERS, PLAYER_EDIT_AWAKENING, PLAYER_CHECK_AWAKENING, PLAYER_EDIT_SKILL, PLAYER_CHECK_SKILL, PLAYER_EDIT_STAT, PLAYER_CHECK_STAT, PLAYER_EDIT_OWNER});
   }
}
