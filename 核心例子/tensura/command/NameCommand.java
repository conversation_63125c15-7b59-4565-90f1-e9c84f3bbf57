package com.github.manasmods.tensura.command;

import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.mojang.brigadier.arguments.BoolArgumentType;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class NameCommand {
   @SubscribeEvent
   public static void register(RegisterCommandsEvent e) {
      e.getDispatcher().register((LiteralArgumentBuilder)Commands.m_82127_("nameable").then(Commands.m_82129_("boolean", BoolArgumentType.bool()).executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         if (player == null) {
            return 1;
         } else {
            boolean b = BoolArgumentType.getBool(context, "boolean");
            TensuraEPCapability.getFrom(player).ifPresent((data) -> {
               data.setNameable(b);
            });
            TensuraEPCapability.sync(player);
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.naming.nameable_status", new Object[]{b}), true);
            return 1;
         }
      })));
   }
}
