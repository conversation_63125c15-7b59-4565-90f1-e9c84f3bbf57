package com.github.manasmods.tensura.command;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.mojang.brigadier.tree.LiteralCommandNode;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.commands.arguments.MessageArgument;
import net.minecraft.commands.arguments.MessageArgument.ChatMessage;
import net.minecraft.network.chat.ChatType;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.OutgoingPlayerChatMessage;
import net.minecraft.network.chat.ChatType.Bound;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.server.players.PlayerList;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class ThoughtCommunicationCommand {
   @SubscribeEvent
   public static void register(RegisterCommandsEvent e) {
      LiteralCommandNode<CommandSourceStack> command = e.getDispatcher().register((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("thoughtCommunication").then(Commands.m_82127_("player").then(Commands.m_82129_("targets", EntityArgument.m_91470_()).then(Commands.m_82129_("message", MessageArgument.m_96832_()).executes((context) -> {
         ChatMessage message = MessageArgument.m_232163_(context, "message");

         try {
            return sendMessage((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), message);
         } catch (Exception var3) {
            message.m_241074_((CommandSourceStack)context.getSource());
            throw var3;
         }
      }))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("subordinate").then(Commands.m_82127_("stay").executes((context) -> {
         return stayCommand((CommandSourceStack)context.getSource());
      }))).then(Commands.m_82127_("follow").executes((context) -> {
         return followCommand((CommandSourceStack)context.getSource());
      }))).then(Commands.m_82127_("wander").executes((context) -> {
         return wanderCommand((CommandSourceStack)context.getSource());
      }))).then(Commands.m_82127_("attack").executes((context) -> {
         return attackCommand((CommandSourceStack)context.getSource());
      }))));
      e.getDispatcher().register((LiteralArgumentBuilder)Commands.m_82127_("tc").redirect(command));
   }

   private static int sendMessage(CommandSourceStack pSource, Collection<ServerPlayer> pRecipients, ChatMessage pChatMessage) {
      Player player = pSource.m_230896_();
      if (player != null) {
         if (noTelepathy(player)) {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.no_telepathy"));
            return 1;
         }

         Bound bound = ChatType.m_241073_(ChatType.f_240674_, pSource);
         pChatMessage.m_241987_(pSource, (message) -> {
            OutgoingPlayerChatMessage chatMessage = OutgoingPlayerChatMessage.m_242676_(message);
            boolean flag = message.m_243059_();
            Entity entity = pSource.m_81373_();
            boolean flag1 = false;

            ServerPlayer serverplayer;
            boolean flag2;
            for(Iterator var8 = pRecipients.iterator(); var8.hasNext(); flag1 |= flag && flag2 && serverplayer != entity) {
               serverplayer = (ServerPlayer)var8.next();
               Bound targetName = ChatType.m_241073_(ChatType.f_240668_, pSource).m_241018_(serverplayer.m_5446_());
               pSource.m_243079_(chatMessage, false, targetName);
               flag2 = pSource.m_243061_(serverplayer);
               serverplayer.m_243093_(chatMessage, flag2, bound);
            }

            if (flag1) {
               pSource.m_243053_(PlayerList.f_243017_);
            }

            chatMessage.m_241051_(pSource.m_81377_().m_6846_());
         });
      }

      return pRecipients.size();
   }

   public static int stayCommand(CommandSourceStack pSource) {
      Player player = pSource.m_230896_();
      if (player != null) {
         if (noTelepathy(player)) {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.no_telepathy"));
            return 1;
         }

         List<Mob> list = player.m_9236_().m_6443_(Mob.class, player.m_20191_().m_82400_(30.0D), (entity) -> {
            return SkillHelper.isSubordinate(player, entity);
         });
         if (!list.isEmpty()) {
            Iterator var3 = list.iterator();

            while(var3.hasNext()) {
               Mob living = (Mob)var3.next();
               SkillHelper.setStay(living);
            }

            pSource.m_81354_(Component.m_237115_("tensura.telepathy.subordinate_all.stay"), false);
         } else {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.subordinate_all.not_found"));
         }
      }

      return 1;
   }

   public static int followCommand(CommandSourceStack pSource) {
      Player player = pSource.m_230896_();
      if (player != null) {
         if (noTelepathy(player)) {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.no_telepathy"));
            return 1;
         }

         List<Mob> list = player.m_9236_().m_6443_(Mob.class, player.m_20191_().m_82400_(30.0D), (entity) -> {
            return SkillHelper.isSubordinate(player, entity);
         });
         if (!list.isEmpty()) {
            Iterator var3 = list.iterator();

            while(var3.hasNext()) {
               Mob living = (Mob)var3.next();
               SkillHelper.setFollow(living);
            }

            pSource.m_81354_(Component.m_237115_("tensura.telepathy.subordinate_all.follow"), false);
         } else {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.subordinate_all.not_found"));
         }
      }

      return 1;
   }

   public static int wanderCommand(CommandSourceStack pSource) {
      Player player = pSource.m_230896_();
      if (player != null) {
         if (noTelepathy(player)) {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.no_telepathy"));
            return 1;
         }

         List<Mob> list = player.m_9236_().m_6443_(Mob.class, player.m_20191_().m_82400_(30.0D), (entity) -> {
            return SkillHelper.isSubordinate(player, entity);
         });
         if (!list.isEmpty()) {
            Iterator var3 = list.iterator();

            while(var3.hasNext()) {
               Mob living = (Mob)var3.next();
               SkillHelper.setWander(living);
            }

            pSource.m_81354_(Component.m_237115_("tensura.telepathy.subordinate_all.wander"), false);
         } else {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.subordinate_all.not_found"));
         }
      }

      return 1;
   }

   public static int attackCommand(CommandSourceStack pSource) {
      Player player = pSource.m_230896_();
      if (player != null) {
         if (noTelepathy(player)) {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.no_telepathy"));
            return 1;
         }

         LivingEntity target = SkillHelper.getTargetingEntity(player, 30.0D, true, false);
         if (target != null) {
            List<Mob> list = player.m_9236_().m_6443_(Mob.class, player.m_20191_().m_82400_(30.0D), (entity) -> {
               return SkillHelper.isSubordinate(player, entity);
            });
            if (!list.isEmpty()) {
               Iterator var4 = list.iterator();

               while(var4.hasNext()) {
                  Mob mob = (Mob)var4.next();
                  mob.m_6710_(target);
                  if (mob instanceof TamableAnimal) {
                     TamableAnimal tamableAnimal = (TamableAnimal)mob;
                     tamableAnimal.m_21839_(false);
                  } else if (mob instanceof TensuraHorseEntity) {
                     TensuraHorseEntity tensuraHorse = (TensuraHorseEntity)mob;
                     tensuraHorse.setSitting(false);
                  }
               }

               pSource.m_81354_(Component.m_237115_("tensura.telepathy.subordinate_all.success"), false);
            } else {
               pSource.m_81352_(Component.m_237115_("tensura.telepathy.subordinate_all.not_found"));
            }
         } else {
            pSource.m_81352_(Component.m_237115_("tensura.telepathy.subordinate_all.no_target"));
         }
      }

      return 1;
   }

   public static boolean noTelepathy(Player player) {
      return !SkillUtils.hasSkill(player, (ManasSkill)CommonSkills.THOUGHT_COMMUNICATION.get());
   }
}
