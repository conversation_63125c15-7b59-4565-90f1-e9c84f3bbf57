package com.github.manasmods.tensura.command;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.manascore.api.skills.event.RemoveSkillEvent;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.ability.magic.Magic;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.api.magicule.MagiculeAPI;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.magicule.MagiculeChunkCapabilityImpl;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.capability.skill.ITensuraSkillCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.capability.smithing.ISmithingCapability;
import com.github.manasmods.tensura.capability.smithing.SmithingCapability;
import com.github.manasmods.tensura.command.argument.RaceArgument;
import com.github.manasmods.tensura.command.argument.SkillArgument;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.item.custom.ResetScrollItem;
import com.github.manasmods.tensura.menu.RaceSelectionMenu;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.TensuraStats;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.PermissionHelper;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import com.github.manasmods.tensura.world.savedata.LabyrinthSaveData;
import com.github.manasmods.tensura.world.savedata.UniqueSkillSaveData;
import com.mojang.authlib.GameProfile;
import com.mojang.brigadier.arguments.BoolArgumentType;
import com.mojang.brigadier.arguments.DoubleArgumentType;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.mojang.brigadier.builder.RequiredArgumentBuilder;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.commands.arguments.coordinates.BlockPosArgument;
import net.minecraft.commands.arguments.coordinates.Vec3Argument;
import net.minecraft.core.BlockPos;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.stats.ServerStatsCounter;
import net.minecraft.stats.Stat;
import net.minecraft.stats.StatType;
import net.minecraft.stats.Stats;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.chunk.LevelChunk;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;
import net.minecraftforge.network.NetworkHooks;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class TensuraCommand {
   @SubscribeEvent
   public static void register(RegisterCommandsEvent e) {
      e.getDispatcher().register((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("tensura").then(((LiteralArgumentBuilder)Commands.m_82127_("reset").requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_EDIT_RESET);
      })).then(((RequiredArgumentBuilder)((RequiredArgumentBuilder)((RequiredArgumentBuilder)((RequiredArgumentBuilder)((RequiredArgumentBuilder)Commands.m_82129_("targets", EntityArgument.m_91470_()).executes((context) -> {
         return resetEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"));
      })).then(Commands.m_82127_("awakening").executes((context) -> {
         return resetAwakening((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"));
      }))).then(((LiteralArgumentBuilder)Commands.m_82127_("race").executes((context) -> {
         return resetRace((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), (Race)null);
      })).then(Commands.m_82129_("race", RaceArgument.race()).executes((context) -> {
         return resetRace((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), RaceArgument.getRace(context, "race"));
      })))).then(Commands.m_82127_("skill").executes((context) -> {
         Iterator var1 = EntityArgument.m_91477_(context, "targets").iterator();

         while(var1.hasNext()) {
            ServerPlayer player = (ServerPlayer)var1.next();
            ResetScrollItem.resetSkill(player, true);
            sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.reset.skill", new Object[]{player.m_7755_()}));
         }

         return 1;
      }))).then(Commands.m_82127_("schematics").executes((context) -> {
         return resetSchematics((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"));
      }))).then(Commands.m_82127_("statistic").executes((context) -> {
         return resetStat((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"));
      }))))).then(Commands.m_82127_("edit").then(((RequiredArgumentBuilder)((RequiredArgumentBuilder)((RequiredArgumentBuilder)((RequiredArgumentBuilder)((RequiredArgumentBuilder)((RequiredArgumentBuilder)Commands.m_82129_("targets", EntityArgument.m_91460_()).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("spirit").requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_EDIT_STAT);
      })).then(Commands.m_82127_("set").then(Commands.m_82129_("spirit", IntegerArgumentType.integer(0, 6)).then(Commands.m_82129_("amount", IntegerArgumentType.integer(0)).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "targets");
         int spirit = IntegerArgumentType.getInteger(context, "spirit");
         int amount = IntegerArgumentType.getInteger(context, "amount");
         TensuraSkillCapability.getFrom(player).ifPresent((data) -> {
            data.setSpiritLevel(player, spirit, amount);
         });
         TensuraSkillCapability.sync(player);
         sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.spirit.set", new Object[]{player.m_7755_(), MagicElemental.byId(spirit).getName(), amount}));
         return 1;
      }))))).then(Commands.m_82127_("clear").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "targets");
         TensuraSkillCapability.getFrom(player).ifPresent(ITensuraSkillCapability::clearSpiritLevel);
         TensuraSkillCapability.sync(player);
         sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.spirit.clear", new Object[]{player.m_7755_()}));
         return 1;
      }))).then(Commands.m_82127_("cooldown").then(Commands.m_82129_("second", IntegerArgumentType.integer(0)).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "targets");
         int second = IntegerArgumentType.getInteger(context, "second");
         TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
            cap.setSpiritCooldown(second);
         });
         TensuraSkillCapability.sync(player);
         sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.spirit.cooldown.set", new Object[]{player.m_7755_(), second}));
         return 1;
      }))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("forceevo").requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_EDIT_RACE_OTHERS);
      })).then(((LiteralArgumentBuilder)Commands.m_82127_("race").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "targets");
         if (RaceHelper.evolveRace(player)) {
            sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.force_evo.race", new Object[]{player.m_7755_()}));
         } else {
            ((CommandSourceStack)context.getSource()).m_81352_(Component.m_237110_("tensura.command.force_evo.race.fail", new Object[]{player.m_7755_()}));
         }

         return 1;
      })).then(Commands.m_82129_("race", RaceArgument.race()).suggests(RaceArgument.getNextEvolutions()).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "targets");
         Race race = RaceArgument.getRace(context, "race");
         if (RaceHelper.evolveRace(player, race, true)) {
            sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.force_evo.race", new Object[]{player.m_7755_()}));
         } else {
            ((CommandSourceStack)context.getSource()).m_81352_(Component.m_237110_("tensura.command.force_evo.race.fail_specific", new Object[]{player.m_7755_(), race.getName()}));
         }

         return 1;
      })))).then(Commands.m_82127_("demonlord").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "targets");
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            cap.setTrueDemonLord(true);
         });
         RaceHelper.awakening(player, false);
         sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.force_evo.demon_lord", new Object[]{player.m_7755_()}));
         return 1;
      }))).then(Commands.m_82127_("hero").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "targets");
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            cap.setTrueHero(true);
         });
         RaceHelper.awakening(player, true);
         sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.force_evo.hero", new Object[]{player.m_7755_()}));
         return 1;
      })))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("awakening").requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_EDIT_AWAKENING);
      })).then(((LiteralArgumentBuilder)Commands.m_82127_("humanKill").then(Commands.m_82127_("set").then(Commands.m_82129_("amount", IntegerArgumentType.integer(0)).executes((context) -> {
         Entity entity = EntityArgument.m_91452_(context, "targets");
         if (entity instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)entity;
            int amount = IntegerArgumentType.getInteger(context, "amount");
            TensuraEPCapability.getFrom(target).ifPresent((data) -> {
               data.setHumanKill(amount);
            });
            TensuraEPCapability.sync(target);
            sendSuccess((CommandSourceStack)context.getSource(), entity, Component.m_237110_("tensura.command.human_kill", new Object[]{entity.m_7755_(), amount}));
            return 1;
         } else {
            return 1;
         }
      })))).then(Commands.m_82127_("add").then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         Entity entity = EntityArgument.m_91452_(context, "targets");
         if (entity instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)entity;
            int amount = IntegerArgumentType.getInteger(context, "amount");
            TensuraEPCapability.getFrom(target).ifPresent((data) -> {
               data.setHumanKill(data.getHumanKill() + amount);
               sendSuccess((CommandSourceStack)context.getSource(), entity, Component.m_237110_("tensura.command.human_kill", new Object[]{entity.m_7755_(), data.getHumanKill()}));
            });
            TensuraEPCapability.sync(target);
            return 1;
         } else {
            return 1;
         }
      }))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("demonlord").then(((LiteralArgumentBuilder)Commands.m_82127_("soul").then(Commands.m_82127_("set").then(Commands.m_82129_("amount", IntegerArgumentType.integer(0)).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "targets");
         int amount = IntegerArgumentType.getInteger(context, "amount");
         TensuraPlayerCapability.getFrom(player).ifPresent((data) -> {
            data.setSoulPoints(amount * 1000);
         });
         TensuraPlayerCapability.sync(player);
         sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.demon_lord.soul", new Object[]{player.m_7755_(), amount}));
         return 1;
      })))).then(Commands.m_82127_("add").then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "targets");
         int amount = IntegerArgumentType.getInteger(context, "amount");
         TensuraPlayerCapability.getFrom(player).ifPresent((data) -> {
            data.setSoulPoints(data.getSoulPoints() + amount * 1000);
            sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.demon_lord.soul", new Object[]{player.m_7755_(), data.getSoulPoints()}));
         });
         TensuraPlayerCapability.sync(player);
         return 1;
      }))))).then(Commands.m_82127_("seed").then(Commands.m_82129_("boolean", BoolArgumentType.bool()).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "targets");
         boolean b = BoolArgumentType.getBool(context, "boolean");
         TensuraPlayerCapability.getFrom(player).ifPresent((data) -> {
            data.setDemonLordSeed(b);
         });
         TensuraPlayerCapability.sync(player);
         sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.demon_lord.seed", new Object[]{player.m_7755_(), b}));
         return 1;
      })))).then(Commands.m_82127_("awakened").then(Commands.m_82129_("boolean", BoolArgumentType.bool()).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "targets");
         boolean b = BoolArgumentType.getBool(context, "boolean");
         TensuraPlayerCapability.getFrom(player).ifPresent((data) -> {
            data.setTrueDemonLord(b);
         });
         TensuraPlayerCapability.sync(player);
         sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.demon_lord.awakened", new Object[]{player.m_7755_(), b}));
         return 1;
      }))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("hero").then(Commands.m_82127_("blessed").then(Commands.m_82129_("boolean", BoolArgumentType.bool()).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "targets");
         boolean b = BoolArgumentType.getBool(context, "boolean");
         TensuraPlayerCapability.getFrom(player).ifPresent((data) -> {
            data.setBlessed(b);
         });
         TensuraPlayerCapability.sync(player);
         sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.spirit.blessed", new Object[]{player.m_7755_(), b}));
         return 1;
      })))).then(Commands.m_82127_("egg").then(Commands.m_82129_("boolean", BoolArgumentType.bool()).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "targets");
         boolean b = BoolArgumentType.getBool(context, "boolean");
         TensuraPlayerCapability.getFrom(player).ifPresent((data) -> {
            data.setHeroEgg(b);
         });
         TensuraPlayerCapability.sync(player);
         sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.hero.egg", new Object[]{player.m_7755_(), b}));
         return 1;
      })))).then(Commands.m_82127_("awakened").then(Commands.m_82129_("boolean", BoolArgumentType.bool()).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "targets");
         boolean b = BoolArgumentType.getBool(context, "boolean");
         TensuraPlayerCapability.getFrom(player).ifPresent((data) -> {
            data.setTrueHero(b);
         });
         TensuraPlayerCapability.sync(player);
         sendSuccess((CommandSourceStack)context.getSource(), player, Component.m_237110_("tensura.command.hero.awakened", new Object[]{player.m_7755_(), b}));
         return 1;
      })))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("race").requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_EDIT_RACE_SELF);
      })).executes((context) -> {
         ServerPlayer player = EntityArgument.m_91474_(context, "targets");
         List<ResourceLocation> races = TensuraPlayerCapability.loadRaces();
         NetworkHooks.openScreen(player, new SimpleMenuProvider(RaceSelectionMenu::new, Component.m_237115_("tensura.race.selection")), (buf) -> {
            buf.writeBoolean(true);
            buf.m_236828_(races, FriendlyByteBuf::m_130085_);
         });
         ResetScrollItem.resetFlight(player);
         return 1;
      })).requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_EDIT_RACE_OTHERS);
      })).then(((RequiredArgumentBuilder)Commands.m_82129_("race", RaceArgument.race()).executes((context) -> {
         return setRace((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), RaceArgument.getRace(context, "race"), false, false);
      })).then(((RequiredArgumentBuilder)Commands.m_82129_("resetStat", BoolArgumentType.bool()).executes((context) -> {
         return setRace((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), RaceArgument.getRace(context, "race"), BoolArgumentType.getBool(context, "resetStat"), false);
      })).then(Commands.m_82129_("grantIntrinsic", BoolArgumentType.bool()).executes((context) -> {
         return setRace((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), RaceArgument.getRace(context, "race"), BoolArgumentType.getBool(context, "resetStat"), BoolArgumentType.getBool(context, "grantIntrinsic"));
      }))))).requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_EDIT_RACE_OTHERS);
      })).then(Commands.m_82127_("majin").then(Commands.m_82129_("boolean", BoolArgumentType.bool()).executes((context) -> {
         return setMajin((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), BoolArgumentType.getBool(context, "boolean"));
      }))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("ability").requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_EDIT_SKILL);
      })).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("grant").then(Commands.m_82129_("skills", SkillArgument.skill()).executes((context) -> {
         return grantSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), SkillArgument.getSkill(context, "skills"));
      }))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("all").executes((context) -> {
         return grantAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (skill) -> {
            return skill instanceof TensuraSkill;
         });
      })).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("skill").executes((context) -> {
         return grantAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (skill) -> {
            return skill instanceof Skill;
         });
      })).then(Commands.m_82127_("unique").executes((context) -> {
         return grantAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.UNIQUE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("extra").executes((context) -> {
         return grantAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.EXTRA)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("common").executes((context) -> {
         return grantAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.COMMON)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("intrinsic").executes((context) -> {
         return grantAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.INTRINSIC)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("resistance").executes((context) -> {
         return grantAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.RESISTANCE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(Commands.m_82127_("magic").executes((context) -> {
         return grantAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            return manasSkill instanceof Magic;
         });
      }))).then(Commands.m_82127_("battlewill").executes((context) -> {
         return grantAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            return manasSkill instanceof Battewill;
         });
      })))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("random").then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("skill").executes((context) -> {
         return grantRandomSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            return manasSkill instanceof Skill;
         });
      })).then(Commands.m_82127_("resistance").executes((context) -> {
         return grantRandomSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.RESISTANCE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("intrinsic").executes((context) -> {
         return grantRandomSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.INTRINSIC)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("common").executes((context) -> {
         return grantRandomSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.COMMON)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("extra").executes((context) -> {
         return grantRandomSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.EXTRA)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("unique").executes((context) -> {
         return grantRandomSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.UNIQUE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("ultimate").executes((context) -> {
         return grantRandomSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.ULTIMATE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(Commands.m_82127_("magic").executes((context) -> {
         return grantRandomSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            return manasSkill instanceof Magic;
         });
      }))).then(Commands.m_82127_("battlewill").executes((context) -> {
         return grantRandomSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            return manasSkill instanceof Battewill;
         });
      }))))).then(((LiteralArgumentBuilder)Commands.m_82127_("revoke").then(Commands.m_82129_("skills", SkillArgument.skill()).executes((context) -> {
         return revokeSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), SkillArgument.getSkill(context, "skills"));
      }))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("everything").executes((context) -> {
         return revokeAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            return true;
         });
      })).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("skill").executes((context) -> {
         return revokeAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (skill) -> {
            return skill instanceof Skill;
         });
      })).then(Commands.m_82127_("ultimate").executes((context) -> {
         return revokeAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.ULTIMATE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("unique").executes((context) -> {
         return revokeAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.UNIQUE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("extra").executes((context) -> {
         return revokeAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.EXTRA)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("common").executes((context) -> {
         return revokeAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.COMMON)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("intrinsic").executes((context) -> {
         return revokeAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.INTRINSIC)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("resistance").executes((context) -> {
         return revokeAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.RESISTANCE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(Commands.m_82127_("magic").executes((context) -> {
         return revokeAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (skill) -> {
            return skill instanceof Magic;
         });
      }))).then(Commands.m_82127_("battlewill").executes((context) -> {
         return revokeAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (skill) -> {
            return skill instanceof Battewill;
         });
      }))))).then(((LiteralArgumentBuilder)Commands.m_82127_("mastery").then(((RequiredArgumentBuilder)Commands.m_82129_("skills", SkillArgument.skill()).then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return masterSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), SkillArgument.getSkill(context, "skills"), IntegerArgumentType.getInteger(context, "amount"));
      }))).then(Commands.m_82127_("max").executes((context) -> {
         return masterSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), SkillArgument.getSkill(context, "skills"), SkillArgument.getSkill(context, "skills").getMaxMastery());
      })))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("everything").then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return masterAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), IntegerArgumentType.getInteger(context, "amount"), (skill) -> {
            return true;
         });
      }))).then(Commands.m_82127_("max").executes((context) -> {
         return masterMaxAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (skill) -> {
            return true;
         });
      }))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("skill").then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return masterAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), IntegerArgumentType.getInteger(context, "amount"), (manasSkill) -> {
            return manasSkill instanceof Skill;
         });
      }))).then(Commands.m_82127_("max").executes((context) -> {
         return masterMaxAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            return manasSkill instanceof Skill;
         });
      }))).then(((LiteralArgumentBuilder)Commands.m_82127_("ultimate").then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return masterAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), IntegerArgumentType.getInteger(context, "amount"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.ULTIMATE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("max").executes((context) -> {
         return masterMaxAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.ULTIMATE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("unique").then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return masterAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), IntegerArgumentType.getInteger(context, "amount"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.UNIQUE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("max").executes((context) -> {
         return masterMaxAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.UNIQUE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("extra").then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return masterAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), IntegerArgumentType.getInteger(context, "amount"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.EXTRA)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("max").executes((context) -> {
         return masterMaxAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.EXTRA)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("common").then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return masterAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), IntegerArgumentType.getInteger(context, "amount"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.COMMON)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("max").executes((context) -> {
         return masterMaxAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.COMMON)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("intrinsic").then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return masterAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), IntegerArgumentType.getInteger(context, "amount"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.INTRINSIC)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("max").executes((context) -> {
         return masterMaxAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.INTRINSIC)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("resistance").then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return masterAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), IntegerArgumentType.getInteger(context, "amount"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.RESISTANCE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("max").executes((context) -> {
         return masterMaxAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.RESISTANCE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))))).then(((LiteralArgumentBuilder)Commands.m_82127_("magic").then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return masterAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), IntegerArgumentType.getInteger(context, "amount"), (manasSkill) -> {
            return manasSkill instanceof Magic;
         });
      }))).then(Commands.m_82127_("max").executes((context) -> {
         return masterMaxAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            return manasSkill instanceof Magic;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("battlewill").then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return masterAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), IntegerArgumentType.getInteger(context, "amount"), (manasSkill) -> {
            return manasSkill instanceof Battewill;
         });
      }))).then(Commands.m_82127_("max").executes((context) -> {
         return masterMaxAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (manasSkill) -> {
            return manasSkill instanceof Battewill;
         });
      })))))).then(((LiteralArgumentBuilder)Commands.m_82127_("cooldown").then(Commands.m_82129_("skills", SkillArgument.skill()).then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return cooldownSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), SkillArgument.getSkill(context, "skills"), IntegerArgumentType.getInteger(context, "amount"));
      })))).then(Commands.m_82127_("everything").then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return cooldownAllSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), IntegerArgumentType.getInteger(context, "amount"));
      }))))).then(((LiteralArgumentBuilder)Commands.m_82127_("modeLearning").then(Commands.m_82127_("all").executes((context) -> {
         return learnSkillModeEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"));
      }))).then(((RequiredArgumentBuilder)Commands.m_82129_("skills", SkillArgument.skill()).then(Commands.m_82129_("mode", IntegerArgumentType.integer()).then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return learnSkillMode((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), SkillArgument.getSkill(context, "skills"), IntegerArgumentType.getInteger(context, "mode"), IntegerArgumentType.getInteger(context, "amount"));
      })))).then(Commands.m_82127_("all").then(Commands.m_82129_("amount", IntegerArgumentType.integer()).executes((context) -> {
         return learnSkillModeAll((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), SkillArgument.getSkill(context, "skills"), IntegerArgumentType.getInteger(context, "amount"));
      })))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("toggle").then(Commands.m_82129_("ability", SkillArgument.skill()).suggests(SkillArgument.getObtainedSkillSuggestions()).executes((context) -> {
         return toggleSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), SkillArgument.getSkill(context, "ability"));
      }))).then(((LiteralArgumentBuilder)Commands.m_82127_("everything").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), true, (skill) -> {
            return true;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), false, (skill) -> {
            return true;
         });
      })))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("skill").then(((LiteralArgumentBuilder)Commands.m_82127_("all").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), true, (skill) -> {
            return skill instanceof Skill;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), false, (skill) -> {
            return skill instanceof Skill;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("resistance").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), true, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.RESISTANCE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), false, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.RESISTANCE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("intrinsic").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), true, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.INTRINSIC)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), false, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.INTRINSIC)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("common").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), true, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.COMMON)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), false, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.COMMON)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("extra").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), true, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.EXTRA)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), false, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.EXTRA)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("unique").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), true, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.UNIQUE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), false, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.UNIQUE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("ultimate").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), true, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.ULTIMATE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), false, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.ULTIMATE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))))).then(((LiteralArgumentBuilder)Commands.m_82127_("magic").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), true, (manasSkill) -> {
            return manasSkill instanceof Magic;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), false, (manasSkill) -> {
            return manasSkill instanceof Magic;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("battlewill").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), true, (manasSkill) -> {
            return manasSkill instanceof Battewill;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), false, (manasSkill) -> {
            return manasSkill instanceof Battewill;
         });
      })))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("stat").requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_EDIT_STAT);
      })).then(((LiteralArgumentBuilder)Commands.m_82127_("magicule").then(((LiteralArgumentBuilder)Commands.m_82127_("set").then(((LiteralArgumentBuilder)Commands.m_82127_("current").then(Commands.m_82129_("amount", DoubleArgumentType.doubleArg(0.0D)).executes((context) -> {
         return setMagicule((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), false);
      }))).then(Commands.m_82127_("max").executes((context) -> {
         return setMagicule((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), -1.0D, false);
      })))).then(Commands.m_82127_("max").then(((RequiredArgumentBuilder)Commands.m_82129_("amount", DoubleArgumentType.doubleArg(0.0D)).executes((context) -> {
         return setMaxMagicule((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), true, false);
      })).then(Commands.m_82129_("resetCurrent", BoolArgumentType.bool()).executes((context) -> {
         return setMaxMagicule((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), BoolArgumentType.getBool(context, "resetCurrent"), false);
      })))))).then(((LiteralArgumentBuilder)Commands.m_82127_("add").then(Commands.m_82127_("current").then(Commands.m_82129_("amount", DoubleArgumentType.doubleArg()).executes((context) -> {
         return setMagicule((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), true);
      })))).then(Commands.m_82127_("max").then(((RequiredArgumentBuilder)Commands.m_82129_("amount", DoubleArgumentType.doubleArg()).executes((context) -> {
         return setMaxMagicule((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), true, true);
      })).then(Commands.m_82129_("resetCurrent", BoolArgumentType.bool()).executes((context) -> {
         return setMaxMagicule((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), BoolArgumentType.getBool(context, "resetCurrent"), true);
      }))))))).then(((LiteralArgumentBuilder)Commands.m_82127_("aura").then(((LiteralArgumentBuilder)Commands.m_82127_("set").then(((LiteralArgumentBuilder)Commands.m_82127_("current").then(Commands.m_82129_("amount", DoubleArgumentType.doubleArg(0.0D)).executes((context) -> {
         return setAura((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), false);
      }))).then(Commands.m_82127_("max").executes((context) -> {
         return setAura((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), -1.0D, false);
      })))).then(Commands.m_82127_("max").then(((RequiredArgumentBuilder)Commands.m_82129_("amount", DoubleArgumentType.doubleArg(0.0D)).executes((context) -> {
         return setMaxAura((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), true, false);
      })).then(Commands.m_82129_("resetCurrent", BoolArgumentType.bool()).executes((context) -> {
         return setMaxAura((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), BoolArgumentType.getBool(context, "resetCurrent"), false);
      })))))).then(((LiteralArgumentBuilder)Commands.m_82127_("add").then(Commands.m_82127_("current").then(Commands.m_82129_("amount", DoubleArgumentType.doubleArg()).executes((context) -> {
         return setAura((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), true);
      })))).then(Commands.m_82127_("max").then(((RequiredArgumentBuilder)Commands.m_82129_("amount", DoubleArgumentType.doubleArg()).executes((context) -> {
         return setMaxAura((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), true, true);
      })).then(Commands.m_82129_("resetCurrent", BoolArgumentType.bool()).executes((context) -> {
         return setMaxAura((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), BoolArgumentType.getBool(context, "resetCurrent"), true);
      }))))))).then(((LiteralArgumentBuilder)Commands.m_82127_("spiritualHP").then(((LiteralArgumentBuilder)Commands.m_82127_("set").then(Commands.m_82129_("amount", DoubleArgumentType.doubleArg(0.0D)).executes((context) -> {
         return setSpiritualHP((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), false);
      }))).then(Commands.m_82127_("max").executes((context) -> {
         return setSpiritualHP((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), -1.0D, false);
      })))).then(Commands.m_82127_("add").then(Commands.m_82129_("amount", DoubleArgumentType.doubleArg()).executes((context) -> {
         return setSpiritualHP((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), true);
      }))))).then(((LiteralArgumentBuilder)Commands.m_82127_("EP").then(Commands.m_82127_("set").then(((RequiredArgumentBuilder)Commands.m_82129_("amount", DoubleArgumentType.doubleArg(0.0D)).executes((context) -> {
         return setEP((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), false, false);
      })).then(Commands.m_82129_("updateStats", BoolArgumentType.bool()).executes((context) -> {
         return setEP((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), false, BoolArgumentType.getBool(context, "updateStats"));
      }))))).then(Commands.m_82127_("add").then(((RequiredArgumentBuilder)Commands.m_82129_("amount", DoubleArgumentType.doubleArg()).executes((context) -> {
         return setEP((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), true, false);
      })).then(Commands.m_82129_("updateStats", BoolArgumentType.bool()).executes((context) -> {
         return setEP((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), DoubleArgumentType.getDouble(context, "amount"), true, BoolArgumentType.getBool(context, "updateStats"));
      })))))).then(((LiteralArgumentBuilder)Commands.m_82127_("severance").then(Commands.m_82127_("set").then(Commands.m_82129_("amount", DoubleArgumentType.doubleArg(0.0D)).executes((context) -> {
         Iterator var1 = EntityArgument.m_91461_(context, "targets").iterator();

         while(var1.hasNext()) {
            Entity entity = (Entity)var1.next();
            if (entity instanceof LivingEntity) {
               LivingEntity living = (LivingEntity)entity;
               double amount = DoubleArgumentType.getDouble(context, "amount");
               TensuraEffectsCapability.getFrom(living).ifPresent((cap) -> {
                  if (amount != 0.0D) {
                     int removeSec = (Integer)TensuraConfig.INSTANCE.skillsConfig.severanceRemoveSec.get();
                     if (cap.getSeveranceRemoveSec() < removeSec) {
                        cap.setSeveranceRemoveSec(removeSec);
                     }
                  } else {
                     cap.setSeveranceRemoveSec(0);
                  }

                  cap.setSeveranceAmount(amount);
               });
               TensuraEffectsCapability.sync(living);
               ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.severance.set", new Object[]{entity.m_7755_(), amount}).m_130940_(ChatFormatting.DARK_GREEN), false);
            }
         }

         return 1;
      })))).then(Commands.m_82127_("clear").executes((context) -> {
         Iterator var1 = EntityArgument.m_91461_(context, "targets").iterator();

         while(var1.hasNext()) {
            Entity entity = (Entity)var1.next();
            if (entity instanceof LivingEntity) {
               LivingEntity living = (LivingEntity)entity;
               double amount = 0.0D;
               TensuraEffectsCapability.getFrom(living).ifPresent((cap) -> {
                  cap.setSeveranceRemoveSec(0);
                  cap.setSeveranceAmount(amount);
               });
               TensuraEffectsCapability.sync(living);
               ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.severance.set", new Object[]{entity.m_7755_(), amount}).m_130940_(ChatFormatting.DARK_GREEN), false);
            }
         }

         return 1;
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("resetCounter").then(Commands.m_82127_("set").then(Commands.m_82129_("amount", IntegerArgumentType.integer(0)).executes((context) -> {
         return setResetCounter((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), IntegerArgumentType.getInteger(context, "amount"), false);
      })))).then(Commands.m_82127_("add").then(Commands.m_82129_("amount", IntegerArgumentType.integer(0)).executes((context) -> {
         return setResetCounter((CommandSourceStack)context.getSource(), EntityArgument.m_91477_(context, "targets"), IntegerArgumentType.getInteger(context, "amount"), true);
      })))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("owner").requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_EDIT_OWNER);
      })).then(((LiteralArgumentBuilder)Commands.m_82127_("name").then(Commands.m_82127_("remove").executes((context) -> {
         return setName((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (String)null);
      }))).then(Commands.m_82127_("set").then(Commands.m_82129_("name", StringArgumentType.string()).executes((context) -> {
         return setName((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), StringArgumentType.getString(context, "name"));
      }))))).then(((LiteralArgumentBuilder)Commands.m_82127_("permanent").then(Commands.m_82127_("remove").executes((context) -> {
         return setOwner((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (Entity)null, false);
      }))).then(Commands.m_82127_("set").then(Commands.m_82129_("owner", EntityArgument.m_91449_()).executes((context) -> {
         return setOwner((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), EntityArgument.m_91452_(context, "owner"), false);
      }))))).then(((LiteralArgumentBuilder)Commands.m_82127_("temporary").then(Commands.m_82127_("remove").executes((context) -> {
         return setOwner((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), (Entity)null, true);
      }))).then(Commands.m_82127_("set").then(Commands.m_82129_("owner", EntityArgument.m_91449_()).executes((context) -> {
         return setOwner((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), EntityArgument.m_91452_(context, "owner"), true);
      }))))).then(((LiteralArgumentBuilder)Commands.m_82127_("neutral").then(Commands.m_82127_("remove").then(Commands.m_82129_("target", EntityArgument.m_91449_()).executes((context) -> {
         return setNeutral((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), EntityArgument.m_91452_(context, "target"), false);
      })))).then(Commands.m_82127_("add").then(Commands.m_82129_("target", EntityArgument.m_91449_()).executes((context) -> {
         return setNeutral((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), EntityArgument.m_91452_(context, "target"), true);
      })))))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("get").then(((RequiredArgumentBuilder)((RequiredArgumentBuilder)Commands.m_82129_("target", EntityArgument.m_91449_()).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("stat").requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_CHECK_STAT);
      })).then(Commands.m_82127_("ep").executes((context) -> {
         Entity entity = EntityArgument.m_91452_(context, "target");
         if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            Double amount = TensuraEPCapability.getEP(living);
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.ep.get", new Object[]{entity.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
            return 1;
         } else {
            return 1;
         }
      }))).then(Commands.m_82127_("spiritualHP").executes((context) -> {
         Entity entity = EntityArgument.m_91452_(context, "target");
         if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            Double amount = TensuraEPCapability.getSpiritualHealth(living);
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.spiritual.get", new Object[]{living.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
            return 1;
         } else {
            return 1;
         }
      })))).then(Commands.m_82127_("awakening").then(Commands.m_82127_("humanKill").executes((context) -> {
         Entity entity = EntityArgument.m_91452_(context, "target");
         if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            int amount = TensuraEPCapability.getHumanKill(living);
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.human_kill.get", new Object[]{living.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
            return 1;
         } else {
            return 1;
         }
      })))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("ability").requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_CHECK_SKILL);
      })).then(Commands.m_82129_("skills", SkillArgument.skill()).executes((context) -> {
         return getSkill((CommandSourceStack)context.getSource(), EntityArgument.m_91452_(context, "target"), SkillArgument.getSkill(context, "skills"));
      }))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("list").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), EntityArgument.m_91452_(context, "target"), (skill) -> {
            return skill instanceof TensuraSkill;
         });
      })).then(Commands.m_82129_("excludeLearning", BoolArgumentType.bool()).executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), EntityArgument.m_91452_(context, "target"), (skill) -> {
            return skill instanceof TensuraSkill;
         }, BoolArgumentType.getBool(context, "excludeLearning"));
      }))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("skill").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), EntityArgument.m_91452_(context, "target"), (skill) -> {
            return skill instanceof Skill;
         });
      })).then(Commands.m_82127_("resistance").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), EntityArgument.m_91452_(context, "target"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.RESISTANCE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("intrinsic").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), EntityArgument.m_91452_(context, "target"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.INTRINSIC)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("common").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), EntityArgument.m_91452_(context, "target"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.COMMON)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("extra").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), EntityArgument.m_91452_(context, "target"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.EXTRA)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("unique").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), EntityArgument.m_91452_(context, "target"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.UNIQUE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("ultimate").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), EntityArgument.m_91452_(context, "target"), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.ULTIMATE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(Commands.m_82127_("magic").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), EntityArgument.m_91452_(context, "target"), (skill) -> {
            return skill instanceof Magic;
         });
      }))).then(Commands.m_82127_("battlewill").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), EntityArgument.m_91452_(context, "target"), (skill) -> {
            return skill instanceof Battewill;
         });
      })))))).then(((RequiredArgumentBuilder)((RequiredArgumentBuilder)((RequiredArgumentBuilder)Commands.m_82129_("target", EntityArgument.m_91466_()).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("stat").requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_CHECK_STAT);
      })).then(((LiteralArgumentBuilder)Commands.m_82127_("magicule").then(Commands.m_82127_("current").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         Double amount = TensuraPlayerCapability.getMagicule(player);
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.magicule.get", new Object[]{player.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      }))).then(Commands.m_82127_("max").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         Double amount = TensuraPlayerCapability.getBaseMagicule(player);
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.magicule.get_max", new Object[]{player.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("aura").then(Commands.m_82127_("current").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         Double amount = TensuraPlayerCapability.getAura(player);
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.aura.get", new Object[]{player.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      }))).then(Commands.m_82127_("max").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         Double amount = TensuraPlayerCapability.getBaseAura(player);
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.aura.get_max", new Object[]{player.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("resetCounter").then(Commands.m_82127_("value").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         int amount = TensuraPlayerCapability.getResetCounter(player);
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.reset_counter.get", new Object[]{player.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      }))).then(Commands.m_82127_("check").executes((context) -> {
         return checkCounterResetRequirement((CommandSourceStack)context.getSource(), EntityArgument.m_91474_(context, "target"));
      }))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("awakening").requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_CHECK_AWAKENING);
      })).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("demonLord").then(Commands.m_82127_("soul").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         int amount = TensuraPlayerCapability.getSoulPoints(player) / 1000;
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.demon_lord.soul.get", new Object[]{player.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      }))).then(Commands.m_82127_("seed").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.demon_lord.seed.get", new Object[]{player.m_7755_(), TensuraPlayerCapability.isDemonLordSeed(player)}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      }))).then(Commands.m_82127_("awakened").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.demon_lord.awakened.get", new Object[]{player.m_7755_(), TensuraPlayerCapability.isTrueDemonLord(player)}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      })))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("hero").then(Commands.m_82127_("blessed").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.spirit.blessed.get", new Object[]{player.m_7755_(), TensuraPlayerCapability.isBlessed(player)}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      }))).then(Commands.m_82127_("egg").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.hero.egg.get", new Object[]{player.m_7755_(), TensuraPlayerCapability.isHeroEgg(player)}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      }))).then(Commands.m_82127_("awakened").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.hero.awakened.get", new Object[]{player.m_7755_(), TensuraPlayerCapability.isTrueHero(player)}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      }))))).then(((LiteralArgumentBuilder)Commands.m_82127_("race").requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_CHECK_RACE);
      })).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         Race race = TensuraPlayerCapability.getRace(player);
         if (race == null) {
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.race.no_race", new Object[]{player.m_7755_()}).m_130940_(ChatFormatting.RED), false);
         } else {
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.race.get", new Object[]{player.m_7755_(), race.getName()}).m_130940_(ChatFormatting.AQUA), false);
         }

         return 1;
      }))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("spirit").requires((commandSourceStack) -> {
         return PermissionHelper.hasPermissionOrIsConsole(commandSourceStack, TensuraPermissions.PLAYER_CHECK_STAT);
      })).executes((context) -> {
         return getSpiritList((CommandSourceStack)context.getSource(), EntityArgument.m_91474_(context, "target"));
      })).then(Commands.m_82127_("level").then(Commands.m_82129_("spirit", IntegerArgumentType.integer(0, 6)).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         int spirit = IntegerArgumentType.getInteger(context, "spirit");
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.spirit.get", new Object[]{player.m_7755_(), MagicElemental.byId(spirit).getName(), TensuraSkillCapability.getSpiritLevel(player, spirit)}).m_130940_(ChatFormatting.AQUA), true);
         return 1;
      })))).then(Commands.m_82127_("cooldown").executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.spirit.cooldown.get", new Object[]{player.m_7755_(), TensuraSkillCapability.getSpiritCooldown(player)}).m_130940_(ChatFormatting.AQUA), true);
         return 1;
      }))))).then(Commands.m_82127_("race").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         if (player == null) {
            return 1;
         } else {
            Race race = TensuraPlayerCapability.getRace(player);
            if (race == null) {
               ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.race.no_race", new Object[]{player.m_7755_()}).m_130940_(ChatFormatting.RED), false);
            } else {
               ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.race.get", new Object[]{player.m_7755_(), race.getName()}).m_130940_(ChatFormatting.AQUA), false);
            }

            return 1;
         }
      }))).then(((LiteralArgumentBuilder)Commands.m_82127_("ability").then(Commands.m_82129_("skills", SkillArgument.skill()).suggests(SkillArgument.getObtainedSkillSuggestions()).executes((context) -> {
         return getSkill((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_230896_(), SkillArgument.getSkill(context, "skills"));
      }))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("list").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_230896_(), (skill) -> {
            return skill instanceof TensuraSkill;
         });
      })).then(Commands.m_82129_("excludeLearning", BoolArgumentType.bool()).executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_230896_(), (skill) -> {
            return skill instanceof TensuraSkill;
         }, BoolArgumentType.getBool(context, "excludeLearning"));
      }))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("skill").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_230896_(), (skill) -> {
            return skill instanceof Skill;
         });
      })).then(Commands.m_82127_("resistance").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_230896_(), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.RESISTANCE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("intrinsic").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_230896_(), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.INTRINSIC)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("common").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_230896_(), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.COMMON)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("extra").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_230896_(), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.EXTRA)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("unique").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_230896_(), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.UNIQUE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("ultimate").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_230896_(), (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.ULTIMATE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(Commands.m_82127_("magic").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_230896_(), (skill) -> {
            return skill instanceof Magic;
         });
      }))).then(Commands.m_82127_("battlewill").executes((context) -> {
         return getSkillList((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_230896_(), (skill) -> {
            return skill instanceof Battewill;
         });
      }))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("spirit").executes((context) -> {
         return getSpiritList((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_230896_());
      })).then(Commands.m_82129_("spirit", IntegerArgumentType.integer(0, 6)).executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         if (player == null) {
            return 1;
         } else {
            int spirit = IntegerArgumentType.getInteger(context, "spirit");
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.spirit.get", new Object[]{player.m_7755_(), MagicElemental.byId(spirit).getName(), TensuraSkillCapability.getSpiritLevel(player, spirit)}).m_130940_(ChatFormatting.AQUA), true);
            return 1;
         }
      }))).then(Commands.m_82127_("cooldown").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         if (player == null) {
            return 1;
         } else {
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.spirit.cooldown.get", new Object[]{player.m_7755_(), TensuraSkillCapability.getSpiritCooldown(player)}).m_130940_(ChatFormatting.AQUA), true);
            return 1;
         }
      })))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("stat").then(Commands.m_82127_("ep").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         if (player == null) {
            return 1;
         } else {
            Double amount = TensuraEPCapability.getEP(player);
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.ep.get", new Object[]{player.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
            return 1;
         }
      }))).then(((LiteralArgumentBuilder)Commands.m_82127_("magicule").then(Commands.m_82127_("current").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         if (player == null) {
            return 1;
         } else {
            Double amount = TensuraPlayerCapability.getMagicule(player);
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.magicule.get", new Object[]{player.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
            return 1;
         }
      }))).then(Commands.m_82127_("max").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         if (player == null) {
            return 1;
         } else {
            Double amount = TensuraPlayerCapability.getBaseMagicule(player);
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.magicule.get_max", new Object[]{player.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
            return 1;
         }
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("aura").then(Commands.m_82127_("current").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         if (player == null) {
            return 1;
         } else {
            Double amount = TensuraPlayerCapability.getAura(player);
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.aura.get", new Object[]{player.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
            return 1;
         }
      }))).then(Commands.m_82127_("max").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         if (player == null) {
            return 1;
         } else {
            Double amount = TensuraPlayerCapability.getBaseAura(player);
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.aura.get_max", new Object[]{player.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
            return 1;
         }
      })))).then(Commands.m_82127_("spiritualHP").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         if (player == null) {
            return 1;
         } else {
            Double amount = TensuraEPCapability.getSpiritualHealth(player);
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.spiritual.get", new Object[]{player.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
            return 1;
         }
      }))).then(((LiteralArgumentBuilder)Commands.m_82127_("resetCounter").then(Commands.m_82127_("value").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         if (player == null) {
            return 1;
         } else {
            int amount = TensuraPlayerCapability.getResetCounter(player);
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.reset_counter.get", new Object[]{player.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
            return 1;
         }
      }))).then(Commands.m_82127_("check").executes((context) -> {
         return checkCounterResetRequirement((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_230896_());
      }))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("awakening").then(Commands.m_82127_("humanKill").executes((context) -> {
         Player entity = ((CommandSourceStack)context.getSource()).m_230896_();
         int amount = TensuraEPCapability.getHumanKill(entity);
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.human_kill.get", new Object[]{entity.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      }))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("demonLord").then(Commands.m_82127_("soul").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         int amount = TensuraPlayerCapability.getSoulPoints(player) / 1000;
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.demon_lord.soul.get", new Object[]{player.m_7755_(), amount}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      }))).then(Commands.m_82127_("seed").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.demon_lord.seed.get", new Object[]{player.m_7755_(), TensuraPlayerCapability.isDemonLordSeed(player)}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      }))).then(Commands.m_82127_("awakened").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.demon_lord.awakened.get", new Object[]{player.m_7755_(), TensuraPlayerCapability.isTrueDemonLord(player)}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      })))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("hero").then(Commands.m_82127_("blessed").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.spirit.blessed.get", new Object[]{player.m_7755_(), TensuraPlayerCapability.isBlessed(player)}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      }))).then(Commands.m_82127_("egg").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.hero.egg.get", new Object[]{player.m_7755_(), TensuraPlayerCapability.isHeroEgg(player)}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      }))).then(Commands.m_82127_("awakened").executes((context) -> {
         Player player = ((CommandSourceStack)context.getSource()).m_230896_();
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.hero.awakened.get", new Object[]{player.m_7755_(), TensuraPlayerCapability.isTrueHero(player)}).m_130940_(ChatFormatting.AQUA), false);
         return 1;
      })))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("worldData").requires((stack) -> {
         return stack.m_6761_(3);
      })).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("labyrinth").then(Commands.m_82127_("regenerate").then(Commands.m_82129_("regenerate", BoolArgumentType.bool()).executes((context) -> {
         if (BoolArgumentType.getBool(context, "regenerate")) {
            LabyrinthSaveData.get(((CommandSourceStack)context.getSource()).m_81377_().m_129783_()).setLoaded(false);
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237115_("tensura.command.labyrinth.regenerate"), true);
         } else {
            LabyrinthSaveData.get(((CommandSourceStack)context.getSource()).m_81377_().m_129783_()).setLoaded(true);
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237115_("tensura.command.labyrinth.regenerate.false"), true);
         }

         return 1;
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("entrance").executes((context) -> {
         Vec3 location = LabyrinthSaveData.get(((CommandSourceStack)context.getSource()).m_81377_().m_129783_()).getEntrancePos();
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.entrance.get", new Object[]{location.f_82479_, location.f_82480_, location.f_82481_}), true);
         return 1;
      })).then(Commands.m_82129_("location", Vec3Argument.m_120841_()).executes((context) -> {
         Vec3 location = Vec3Argument.m_120844_(context, "location");
         LabyrinthSaveData.get(((CommandSourceStack)context.getSource()).m_81377_().m_129783_()).setEntrancePos(location);
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.entrance.set", new Object[]{location.f_82479_, location.f_82480_, location.f_82481_}), true);
         return 1;
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("colossusPos").executes((context) -> {
         Vec3 location = LabyrinthSaveData.get(((CommandSourceStack)context.getSource()).m_81377_().m_129783_()).getColossusPos();
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.colossus_pos.get", new Object[]{location.f_82479_, location.f_82480_, location.f_82481_}), true);
         return 1;
      })).then(Commands.m_82129_("location", Vec3Argument.m_120841_()).executes((context) -> {
         Vec3 location = Vec3Argument.m_120844_(context, "location");
         LabyrinthSaveData.get(((CommandSourceStack)context.getSource()).m_81377_().m_129783_()).setColossusPos(location);
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.colossus_pos.set", new Object[]{location.f_82479_, location.f_82480_, location.f_82481_}), true);
         return 1;
      })))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("passing").then(Commands.m_82127_("check").then(Commands.m_82129_("target", EntityArgument.m_91449_()).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         if (LabyrinthSaveData.isEntityPassedColossus(player)) {
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.passed_list.check_true", new Object[]{player.m_7755_()}), false);
         } else {
            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.passed_list.check_false", new Object[]{player.m_7755_()}), false);
         }

         return 1;
      })))).then(Commands.m_82127_("add").then(Commands.m_82129_("target", EntityArgument.m_91449_()).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         LabyrinthSaveData.addPassedEntity(player, false);
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.passed_list.add", new Object[]{player.m_7755_()}), true);
         return 1;
      })))).then(Commands.m_82127_("remove").then(Commands.m_82129_("target", EntityArgument.m_91466_()).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         LabyrinthSaveData.removePassedEntity(player, false);
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.passed_list.remove", new Object[]{player.m_7755_()}), true);
         return 1;
      })))).then(Commands.m_82127_("won").then(Commands.m_82129_("target", EntityArgument.m_91449_()).then(Commands.m_82129_("won", BoolArgumentType.bool()).executes((context) -> {
         Player player = EntityArgument.m_91474_(context, "target");
         boolean won = BoolArgumentType.getBool(context, "won");
         TensuraEffectsCapability.getFrom(player).ifPresent((cap) -> {
            cap.setColossusWon(won);
         });
         TensuraEffectsCapability.sync(player);
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.passed_list.won", new Object[]{player.m_7755_(), won}), true);
         return 1;
      })))))).then(((LiteralArgumentBuilder)Commands.m_82127_("passedEntrance").executes((context) -> {
         Vec3 location = LabyrinthSaveData.get(((CommandSourceStack)context.getSource()).m_81377_().m_129783_()).getPassedEntrance();
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.passed_entrance.get", new Object[]{location.f_82479_, location.f_82480_, location.f_82481_}), true);
         return 1;
      })).then(Commands.m_82129_("location", Vec3Argument.m_120841_()).executes((context) -> {
         Vec3 location = Vec3Argument.m_120844_(context, "location");
         LabyrinthSaveData.get(((CommandSourceStack)context.getSource()).m_81377_().m_129783_()).setPassedEntrance(location);
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.passed_entrance.set", new Object[]{location.f_82479_, location.f_82480_, location.f_82481_}), true);
         return 1;
      })))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("fallOff").executes((context) -> {
         Vec3 location = LabyrinthSaveData.get(((CommandSourceStack)context.getSource()).m_81377_().m_129783_()).getFallOffPos();
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.fall_off.get", new Object[]{location.f_82479_, location.f_82480_, location.f_82481_}), true);
         return 1;
      })).then(Commands.m_82129_("location", Vec3Argument.m_120841_()).executes((context) -> {
         Vec3 location = Vec3Argument.m_120844_(context, "location");
         LabyrinthSaveData.get(((CommandSourceStack)context.getSource()).m_81377_().m_129783_()).setFallOffPos(location);
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.fall_off.set", new Object[]{location.f_82479_, location.f_82480_, location.f_82481_}), true);
         return 1;
      }))).then(((LiteralArgumentBuilder)Commands.m_82127_("height").executes((context) -> {
         double y = LabyrinthSaveData.get(((CommandSourceStack)context.getSource()).m_81377_().m_129783_()).getStartFallOffY();
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.fall_off_y.get", new Object[]{y}), true);
         return 1;
      })).then(Commands.m_82129_("height", DoubleArgumentType.doubleArg()).executes((context) -> {
         double y = DoubleArgumentType.getDouble(context, "height");
         LabyrinthSaveData.get(((CommandSourceStack)context.getSource()).m_81377_().m_129783_()).setStartFallOffY(y);
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.labyrinth.fall_off_y.set", new Object[]{y}), true);
         return 1;
      })))))).then(((LiteralArgumentBuilder)Commands.m_82127_("magicule").then(((LiteralArgumentBuilder)Commands.m_82127_("get").executes((context) -> {
         ServerPlayer player = ((CommandSourceStack)context.getSource()).m_81375_();
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.magicule.area_get", new Object[]{MagiculeAPI.getMagicule(player)}), true);
         return 1;
      })).then(Commands.m_82127_("max").executes((context) -> {
         ServerPlayer player = ((CommandSourceStack)context.getSource()).m_81375_();
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.magicule.area_get.max", new Object[]{MagiculeAPI.getMaxMagicule(player)}), true);
         return 1;
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("reinitialize").executes((context) -> {
         ServerPlayer player = ((CommandSourceStack)context.getSource()).m_81375_();
         reinitializeMagiculeAt(player.f_19853_.m_46745_(player.m_20183_()));
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.magicule.reinitialized", new Object[]{1}), true);
         return 1;
      })).then(((RequiredArgumentBuilder)Commands.m_82129_("center", BlockPosArgument.m_118239_()).executes((context) -> {
         ServerPlayer player = ((CommandSourceStack)context.getSource()).m_81375_();
         BlockPos pos = BlockPosArgument.m_118242_(context, "center");
         reinitializeMagiculeAt(player.f_19853_.m_46745_(pos));
         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.magicule.reinitialized", new Object[]{1}), true);
         return 1;
      })).then(Commands.m_82129_("radius", IntegerArgumentType.integer(0)).executes((context) -> {
         ServerPlayer player = ((CommandSourceStack)context.getSource()).m_81375_();
         BlockPos pos = BlockPosArgument.m_118242_(context, "center");
         int radius = IntegerArgumentType.getInteger(context, "radius");
         Set<LevelChunk> chunks = new HashSet();

         for(int x = -radius; x <= radius; ++x) {
            for(int z = -radius; z <= radius; ++z) {
               chunks.add(player.f_19853_.m_46745_(pos.m_7918_(x, 0, z)));
            }
         }

         Iterator var7 = chunks.iterator();

         while(var7.hasNext()) {
            LevelChunk chunk = (LevelChunk)var7.next();
            reinitializeMagiculeAt(chunk);
         }

         ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.magicule.reinitialized", new Object[]{chunks.size()}), true);
         return 1;
      })))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("trulyUnique").then(Commands.m_82127_("add").then(Commands.m_82129_("skill", SkillArgument.skill()).then(Commands.m_82129_("player", EntityArgument.m_91466_()).executes((context) -> {
         return trulyUniqueAdd((CommandSourceStack)context.getSource(), SkillArgument.getSkill(context, "skill"), EntityArgument.m_91474_(context, "player"));
      }))))).then(Commands.m_82127_("remove").then(Commands.m_82129_("skill", SkillArgument.skill()).executes((context) -> {
         return trulyUniqueRemove((CommandSourceStack)context.getSource(), SkillArgument.getSkill(context, "skill"));
      })))).then(Commands.m_82127_("check").then(Commands.m_82129_("skill", SkillArgument.skill()).executes((context) -> {
         return trulyUniqueCheck((CommandSourceStack)context.getSource(), SkillArgument.getSkill(context, "skill"));
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("list").then(Commands.m_82127_("taken").executes((context) -> {
         return getTrulyUniqueTakenList((CommandSourceStack)context.getSource());
      }))).then(Commands.m_82127_("remaining").executes((context) -> {
         return getTrulyUniqueRemainList((CommandSourceStack)context.getSource());
      }))))));
   }

   private static void reinitializeMagiculeAt(LevelChunk chunk) {
      ((MagiculeChunkCapabilityImpl)MagiculeChunkCapabilityImpl.get(chunk)).reinitialize();
   }

   private static int resetEverything(CommandSourceStack stack, Collection<? extends ServerPlayer> pTargets) {
      Iterator var2 = pTargets.iterator();

      while(var2.hasNext()) {
         ServerPlayer player = (ServerPlayer)var2.next();
         ResetScrollItem.resetEverything(player);
         sendSuccess(stack, player, Component.m_237110_("tensura.command.reset.all", new Object[]{player.m_7755_()}));
      }

      return 1;
   }

   private static int resetAwakening(CommandSourceStack stack, Collection<? extends Player> pTargets) {
      Iterator var2 = pTargets.iterator();

      while(var2.hasNext()) {
         Player player = (Player)var2.next();
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            cap.setDemonLordSeed(false);
            cap.setTrueDemonLord(false);
            cap.setHeroEgg(false);
            cap.setHeroEgg(false);
            cap.setSoulPoints(0);
         });
         TensuraPlayerCapability.sync(player);
         sendSuccess(stack, player, Component.m_237110_("tensura.command.reset.awakening", new Object[]{player.m_7755_()}));
      }

      return 1;
   }

   private static int resetRace(CommandSourceStack stack, Collection<? extends ServerPlayer> pTargets, @Nullable Race race) {
      Iterator var3 = pTargets.iterator();

      while(true) {
         while(var3.hasNext()) {
            ServerPlayer player = (ServerPlayer)var3.next();
            if (race == null) {
               ResetScrollItem.resetRace(player);
               sendSuccess(stack, player, Component.m_237110_("tensura.command.reset.race", new Object[]{player.m_7755_()}));
            } else {
               MinecraftServer server = player.m_20194_();
               if (server != null) {
                  ServerStatsCounter stats = server.m_6846_().m_11239_(player);
                  stats.m_12850_();
                  Iterator var7 = stats.m_12851_().iterator();

                  while(var7.hasNext()) {
                     Stat<?> stat = (Stat)var7.next();
                     stats.m_6085_(player, stat, 0);
                  }
               }

               TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                  if (cap.getRace() != null) {
                     SkillStorage storage = SkillAPI.getSkillsFrom(player);
                     Iterator iterator = storage.getLearnedSkills().iterator();

                     while(iterator.hasNext()) {
                        Object patt160826$temp = iterator.next();
                        if (patt160826$temp instanceof TensuraSkillInstance) {
                           TensuraSkillInstance instance = (TensuraSkillInstance)patt160826$temp;
                           if (ResetScrollItem.isIntrinsicSkills(player, cap, cap.getRace(), instance) && !MinecraftForge.EVENT_BUS.post(new RemoveSkillEvent(instance, player))) {
                              iterator.remove();
                           }
                        }
                     }

                     storage.syncAll();
                  }

                  cap.clearIntrinsicSkills();
               });
               TensuraPlayerCapability.sync(player);
               RaceSelectionMenu.setRace(player, race, true, false);
               TensuraEPCapability.getFrom(player).ifPresent((cap) -> {
                  cap.setName((String)null);
                  cap.setTemporaryOwner((UUID)null);
                  cap.setPermanentOwner((UUID)null);
               });
               TensuraEPCapability.sync(player);
               ResetScrollItem.resetFlight(player);
               sendSuccess(stack, player, Component.m_237110_("tensura.command.reset.race", new Object[]{player.m_7755_()}));
            }
         }

         return 1;
      }
   }

   private static int resetSchematics(CommandSourceStack stack, Collection<? extends Player> pTargets) {
      Iterator var2 = pTargets.iterator();

      while(var2.hasNext()) {
         Player player = (Player)var2.next();
         if (player instanceof ServerPlayer) {
            ServerPlayer serverPlayer = (ServerPlayer)player;
            TensuraAdvancementsHelper.revokeAllTensuraAdvancements(serverPlayer);
         }

         SmithingCapability.getFrom(player).ifPresent(ISmithingCapability::clearSchematics);
         SmithingCapability.sync(player);
         sendSuccess(stack, player, Component.m_237110_("tensura.command.reset.schematic", new Object[]{player.m_7755_()}));
      }

      return 1;
   }

   private static int resetStat(CommandSourceStack stack, Collection<? extends Player> pTargets) {
      Player player;
      for(Iterator var2 = pTargets.iterator(); var2.hasNext(); sendSuccess(stack, player, Component.m_237110_("tensura.command.reset.stat", new Object[]{player.m_7755_()}))) {
         player = (Player)var2.next();
         MinecraftServer server = player.m_20194_();
         if (server != null) {
            ServerStatsCounter stats = server.m_6846_().m_11239_(player);
            stats.m_12850_();
            Iterator var6 = stats.m_12851_().iterator();

            while(var6.hasNext()) {
               Stat<?> stat = (Stat)var6.next();
               stats.m_6085_(player, stat, 0);
            }
         }
      }

      return 1;
   }

   private static int trulyUniqueAdd(CommandSourceStack stack, ManasSkill skill, Player player) {
      ServerLevel level = stack.m_81372_();
      if (!level.m_46469_().m_46207_(TensuraGameRules.TRULY_UNIQUE)) {
         stack.m_81352_(Component.m_237115_("tensura.truly_unique.off"));
         return 1;
      } else {
         UniqueSkillSaveData saveData = UniqueSkillSaveData.get(level.m_7654_().m_129783_());
         if (saveData.hasSkill(skill.getRegistryName())) {
            stack.m_81352_(Component.m_237110_("tensura.truly_unique.already_have", new Object[]{skill.getName()}).m_130940_(ChatFormatting.RED));
         } else {
            saveData.addSkill(skill.getRegistryName(), player.m_20148_());
            stack.m_81354_(Component.m_237110_("tensura.truly_unique.added", new Object[]{skill.getName(), player.m_7755_()}).m_130940_(ChatFormatting.DARK_GREEN), true);
         }

         return 1;
      }
   }

   private static int grantRandomSkill(CommandSourceStack stack, Collection<? extends Entity> pTargets, Predicate<ManasSkill> predicate) {
      Iterator var3 = pTargets.iterator();

      while(var3.hasNext()) {
         Entity entity = (Entity)var3.next();
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         List<ManasSkill> list = SkillAPI.getSkillRegistry().getValues().stream().filter((manasSkill) -> {
            return storage.getSkill(manasSkill).isPresent() && ((ManasSkillInstance)storage.getSkill(manasSkill).get()).getMastery() > 0 ? false : predicate.test(manasSkill);
         }).toList();
         if (list.isEmpty()) {
            stack.m_81352_(Component.m_237110_("tensura.skill.granted_all", new Object[]{0, entity.m_7755_()}));
         } else {
            ManasSkill skill = (ManasSkill)list.get(entity.m_9236_().f_46441_.m_188503_(list.size()));
            if (entity instanceof LivingEntity) {
               LivingEntity living = (LivingEntity)entity;
               TensuraSkillInstance instance = new TensuraSkillInstance(skill);
               instance.getOrCreateTag().m_128379_("NoMagiculeCost", true);
               if (SkillUtils.learnSkill(living, (ManasSkillInstance)instance)) {
                  sendSuccess(stack, entity, Component.m_237110_("tensura.skill.granted", new Object[]{skill.getName(), entity.m_7755_()}));
               } else {
                  stack.m_81352_(Component.m_237110_("tensura.skill.already_has", new Object[]{entity.m_7755_(), skill.getName()}));
               }
            }
         }
      }

      return 1;
   }

   private static int grantSkill(CommandSourceStack stack, Collection<? extends Entity> pTargets, ManasSkill skill) {
      Iterator var3 = pTargets.iterator();

      while(var3.hasNext()) {
         Entity entity = (Entity)var3.next();
         if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            TensuraSkillInstance instance = new TensuraSkillInstance(skill);
            instance.getOrCreateTag().m_128379_("NoMagiculeCost", true);
            if (SkillUtils.learnSkill(living, (ManasSkillInstance)instance)) {
               sendSuccess(stack, entity, Component.m_237110_("tensura.skill.granted", new Object[]{skill.getName(), entity.m_7755_()}));
            } else {
               stack.m_81352_(Component.m_237110_("tensura.skill.already_has", new Object[]{entity.m_7755_(), skill.getName()}));
            }
         }
      }

      return 1;
   }

   private static int grantAllSkill(CommandSourceStack stack, Collection<? extends Entity> pTargets, Predicate<ManasSkill> predicate) {
      Iterator var3 = pTargets.iterator();

      while(true) {
         Entity entity;
         do {
            if (!var3.hasNext()) {
               return 1;
            }

            entity = (Entity)var3.next();
         } while(!(entity instanceof LivingEntity));

         LivingEntity living = (LivingEntity)entity;
         int i = 0;
         List<ManasSkill> newSkills = SkillAPI.getSkillRegistry().getValues().stream().filter((skillx) -> {
            return predicate.test(skillx) && !SkillUtils.hasSkill(entity, skillx);
         }).toList();
         Iterator var8 = newSkills.iterator();

         while(var8.hasNext()) {
            ManasSkill skill = (ManasSkill)var8.next();
            TensuraSkillInstance instance = new TensuraSkillInstance(skill);
            instance.getOrCreateTag().m_128379_("NoMagiculeCost", true);
            if (SkillUtils.learnSkill(living, (ManasSkillInstance)instance)) {
               ++i;
            }
         }

         sendSuccess(stack, entity, Component.m_237110_("tensura.skill.granted_all", new Object[]{i, entity.m_7755_()}));
      }
   }

   private static int revokeSkill(CommandSourceStack stack, Collection<? extends Entity> pTargets, ManasSkill skill) {
      Iterator var3 = pTargets.iterator();

      while(var3.hasNext()) {
         Entity entity = (Entity)var3.next();
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         if (storage.getSkill(skill).isPresent()) {
            storage.forgetSkill(skill);
            storage.syncAll();
            sendSuccess(stack, entity, Component.m_237110_("tensura.skill.revoked", new Object[]{skill.getName(), entity.m_7755_()}), ChatFormatting.RED);
         } else {
            stack.m_81352_(Component.m_237110_("tensura.skill.do_not_have", new Object[]{entity.m_7755_(), skill.getName()}));
         }
      }

      return 1;
   }

   private static int revokeAllSkill(CommandSourceStack stack, Collection<? extends Entity> pTargets, Predicate<ManasSkill> predicate) {
      Entity entity;
      int i;
      for(Iterator var3 = pTargets.iterator(); var3.hasNext(); sendSuccess(stack, entity, Component.m_237110_("tensura.skill.revoked_all", new Object[]{i, entity.m_7755_()}), ChatFormatting.RED)) {
         entity = (Entity)var3.next();
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         i = 0;
         Iterator iterator = storage.getLearnedSkills().iterator();

         while(iterator.hasNext()) {
            Object var9 = iterator.next();
            if (var9 instanceof TensuraSkillInstance) {
               TensuraSkillInstance instance = (TensuraSkillInstance)var9;
               if (predicate.test(instance.getSkill()) && !MinecraftForge.EVENT_BUS.post(new RemoveSkillEvent(instance, entity))) {
                  iterator.remove();
                  ++i;
               }
            }
         }

         if (i > 0) {
            storage.syncAll();
         }
      }

      return 1;
   }

   private static int masterSkill(CommandSourceStack stack, Collection<? extends Entity> pTargets, ManasSkill skill, int amount) {
      Iterator var4 = pTargets.iterator();

      while(var4.hasNext()) {
         Entity entity = (Entity)var4.next();
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         Optional<ManasSkillInstance> optional = storage.getSkill(skill);
         if (optional.isEmpty()) {
            stack.m_81352_(Component.m_237110_("tensura.skill.do_not_have", new Object[]{entity.m_7755_(), skill.getName()}));
         } else if (amount > skill.getMaxMastery()) {
            stack.m_81352_(Component.m_237110_("tensura.skill.mastery.above_max", new Object[]{((ManasSkillInstance)optional.get()).getSkill().getName(), skill.getMaxMastery()}));
         } else {
            ((ManasSkillInstance)optional.get()).setMastery(amount);
            ((ManasSkillInstance)optional.get()).markDirty();
            storage.syncChanges();
            sendSuccess(stack, entity, Component.m_237110_("tensura.skill.mastery_point", new Object[]{skill.getName(), entity.m_7755_(), amount}));
            if (amount == skill.getMaxMastery()) {
               if (entity instanceof LivingEntity) {
                  LivingEntity livingEntity = (LivingEntity)entity;
                  ((ManasSkillInstance)optional.get()).onSkillMastered(livingEntity);
               }

               entity.m_213846_(Component.m_237110_("tensura.skill.mastery", new Object[]{((ManasSkillInstance)optional.get()).getSkill().getName()}).m_130940_(ChatFormatting.GREEN));
            }
         }
      }

      return 1;
   }

   private static int masterAllSkill(CommandSourceStack stack, Collection<? extends Entity> pTargets, int amount, Predicate<ManasSkill> predicate) {
      Iterator var4 = pTargets.iterator();

      while(true) {
         while(var4.hasNext()) {
            Entity entity = (Entity)var4.next();
            SkillStorage storage = SkillAPI.getSkillsFrom(entity);
            if (storage.getLearnedSkills().isEmpty()) {
               stack.m_81352_(Component.m_237110_("tensura.skill.empty_storage", new Object[]{entity.m_7755_()}));
            } else {
               int i = 0;
               int j = 0;
               Iterator var9 = List.copyOf(storage.getLearnedSkills()).iterator();

               while(var9.hasNext()) {
                  ManasSkillInstance instance = (ManasSkillInstance)var9.next();
                  if (instance != null && predicate.test(instance.getSkill())) {
                     ManasSkill skill = instance.getSkill();
                     instance.setMastery(Math.min(amount, skill.getMaxMastery()));
                     instance.markDirty();
                     ++i;
                     if (amount >= skill.getMaxMastery()) {
                        if (entity instanceof LivingEntity) {
                           LivingEntity livingEntity = (LivingEntity)entity;
                           instance.onSkillMastered(livingEntity);
                        }

                        ++j;
                     }
                  }
               }

               if (i > 0) {
                  storage.syncChanges();
                  sendSuccess(stack, entity, Component.m_237110_("tensura.skill.mastery_point.all", new Object[]{entity.m_7755_(), i, amount}));
                  if (j > 0) {
                     entity.m_213846_(Component.m_237110_("tensura.skill.mastery.all", new Object[]{entity.m_7755_(), j}).m_130940_(ChatFormatting.GREEN));
                  }
               } else {
                  stack.m_81352_(Component.m_237110_("tensura.skill.mastery.no_changes", new Object[]{entity.m_7755_()}));
               }
            }
         }

         return 1;
      }
   }

   private static int masterMaxAllSkill(CommandSourceStack stack, Collection<? extends Entity> pTargets, Predicate<ManasSkill> predicate) {
      Iterator var3 = pTargets.iterator();

      while(true) {
         while(var3.hasNext()) {
            Entity entity = (Entity)var3.next();
            SkillStorage storage = SkillAPI.getSkillsFrom(entity);
            if (storage.getLearnedSkills().isEmpty()) {
               stack.m_81352_(Component.m_237110_("tensura.skill.empty_storage", new Object[]{entity.m_7755_()}));
            } else {
               int i = 0;
               Iterator var7 = List.copyOf(storage.getLearnedSkills()).iterator();

               while(var7.hasNext()) {
                  ManasSkillInstance instance = (ManasSkillInstance)var7.next();
                  if (instance != null && predicate.test(instance.getSkill())) {
                     ManasSkill skill = instance.getSkill();
                     instance.setMastery(skill.getMaxMastery());
                     if (entity instanceof LivingEntity) {
                        LivingEntity living = (LivingEntity)entity;
                        instance.onSkillMastered(living);
                     }

                     instance.markDirty();
                     ++i;
                  }
               }

               if (i > 0) {
                  storage.syncChanges();
                  sendSuccess(stack, entity, Component.m_237110_("tensura.skill.mastery.all", new Object[]{entity.m_7755_(), i}).m_130940_(ChatFormatting.GREEN));
               } else {
                  stack.m_81352_(Component.m_237110_("tensura.skill.mastery.no_changes", new Object[]{entity.m_7755_()}));
               }
            }
         }

         return 1;
      }
   }

   private static int cooldownSkill(CommandSourceStack stack, Collection<? extends Entity> pTargets, ManasSkill skill, int amount) {
      Iterator var4 = pTargets.iterator();

      while(var4.hasNext()) {
         Entity entity = (Entity)var4.next();
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         Optional<ManasSkillInstance> optional = storage.getSkill(skill);
         if (optional.isEmpty()) {
            stack.m_81352_(Component.m_237110_("tensura.skill.do_not_have", new Object[]{entity.m_7755_(), skill.getName()}));
         } else {
            if (amount < 0) {
               amount = 0;
            }

            ((ManasSkillInstance)optional.get()).setCoolDown(amount);
            ((ManasSkillInstance)optional.get()).markDirty();
            storage.syncChanges();
            sendSuccess(stack, entity, Component.m_237110_("tensura.skill.set_cooldown", new Object[]{skill.getName(), entity.m_7755_(), amount}));
         }
      }

      return 1;
   }

   private static int cooldownAllSkill(CommandSourceStack stack, Collection<? extends Entity> pTargets, int amount) {
      Iterator var3 = pTargets.iterator();

      while(var3.hasNext()) {
         Entity entity = (Entity)var3.next();
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         if (storage.getLearnedSkills().isEmpty()) {
            stack.m_81352_(Component.m_237115_("tensura.skill.empty_storage"));
         }

         int i = 0;
         Iterator var7 = storage.getLearnedSkills().iterator();

         while(var7.hasNext()) {
            ManasSkillInstance skillInstance = (ManasSkillInstance)var7.next();
            if (skillInstance != null) {
               if (amount < 0) {
                  amount = 0;
               }

               skillInstance.setCoolDown(amount);
               skillInstance.markDirty();
               ++i;
            }
         }

         if (i > 0) {
            storage.syncChanges();
            sendSuccess(stack, entity, Component.m_237110_("tensura.skill.set_cooldown.all", new Object[]{entity.m_7755_(), amount}));
         } else {
            stack.m_81352_(Component.m_237110_("tensura.skill.set_cooldown.all.no_changes", new Object[]{entity.m_7755_()}));
         }
      }

      return 1;
   }

   private static int learnSkillMode(CommandSourceStack stack, Collection<? extends Entity> pTargets, ManasSkill manasSkill, int mode, int amount) {
      if (manasSkill instanceof TensuraSkill) {
         TensuraSkill skill = (TensuraSkill)manasSkill;
         Iterator var6 = pTargets.iterator();

         while(var6.hasNext()) {
            Entity entity = (Entity)var6.next();
            SkillStorage storage = SkillAPI.getSkillsFrom(entity);
            Optional<ManasSkillInstance> optional = storage.getSkill(skill);
            if (optional.isEmpty()) {
               stack.m_81352_(Component.m_237110_("tensura.skill.do_not_have", new Object[]{entity.m_7755_(), skill.getName()}));
            } else if (mode > skill.modes()) {
               stack.m_81352_(Component.m_237110_("tensura.skill.mode_learning.no_mode", new Object[]{skill.getName(), mode}));
            } else {
               String id = skill.modeLearningId(mode);
               if (id.equals("None")) {
                  stack.m_81352_(Component.m_237110_("tensura.skill.mode_learning.no_learn", new Object[]{skill.getModeName(mode), skill.getName()}));
               } else {
                  ((ManasSkillInstance)optional.get()).getOrCreateTag().m_128405_(id, amount);
                  ((ManasSkillInstance)optional.get()).markDirty();
                  storage.syncChanges();
                  sendSuccess(stack, entity, Component.m_237110_("tensura.skill.mode_learning.success", new Object[]{skill.getModeName(mode), skill.getName(), amount, entity.m_7755_()}));
               }
            }
         }

         return 1;
      } else {
         stack.m_81352_(Component.m_237115_("tensura.argument.skill.invalid"));
         return 1;
      }
   }

   private static int learnSkillModeAll(CommandSourceStack stack, Collection<? extends Entity> pTargets, ManasSkill manasSkill, int amount) {
      if (!(manasSkill instanceof TensuraSkill)) {
         stack.m_81352_(Component.m_237115_("tensura.argument.skill.invalid"));
         return 1;
      } else {
         TensuraSkill skill = (TensuraSkill)manasSkill;
         Iterator var5 = pTargets.iterator();

         while(true) {
            while(var5.hasNext()) {
               Entity entity = (Entity)var5.next();
               SkillStorage storage = SkillAPI.getSkillsFrom(entity);
               Optional<ManasSkillInstance> optional = storage.getSkill(skill);
               if (optional.isEmpty()) {
                  stack.m_81352_(Component.m_237110_("tensura.skill.do_not_have", new Object[]{entity.m_7755_(), skill.getName()}));
               } else {
                  int j = 0;

                  for(int i = 1; i <= skill.modes(); ++i) {
                     String id = skill.modeLearningId(i);
                     if (!id.equals("None")) {
                        ((ManasSkillInstance)optional.get()).getOrCreateTag().m_128405_(id, amount);
                        ((ManasSkillInstance)optional.get()).markDirty();
                        ++j;
                     }
                  }

                  if (j > 0) {
                     storage.syncChanges();
                     sendSuccess(stack, entity, Component.m_237110_("tensura.skill.mode_learning.success_all", new Object[]{j, skill.getName(), amount, entity.m_7755_()}));
                  } else {
                     stack.m_81352_(Component.m_237110_("tensura.skill.mode_learning.no_learn_all", new Object[]{skill.getName()}));
                  }
               }
            }

            return 1;
         }
      }
   }

   private static int learnSkillModeEverything(CommandSourceStack stack, Collection<? extends Entity> pTargets) {
      Iterator var2 = pTargets.iterator();

      while(true) {
         label54:
         while(var2.hasNext()) {
            Entity entity = (Entity)var2.next();
            SkillStorage storage = SkillAPI.getSkillsFrom(entity);
            if (storage.getLearnedSkills().isEmpty()) {
               stack.m_81352_(Component.m_237115_("tensura.skill.empty_storage"));
            } else {
               int i = 0;
               Iterator var6 = storage.getLearnedSkills().iterator();

               while(true) {
                  ManasSkillInstance skillInstance;
                  ManasSkill var9;
                  do {
                     do {
                        if (!var6.hasNext()) {
                           if (i > 0) {
                              storage.syncChanges();
                              sendSuccess(stack, entity, Component.m_237110_("tensura.skill.mode_learning.success_everything", new Object[]{entity.m_7755_()}));
                           } else {
                              stack.m_81352_(Component.m_237110_("tensura.skill.set_cooldown.all.no_changes", new Object[]{entity.m_7755_()}));
                           }
                           continue label54;
                        }

                        skillInstance = (ManasSkillInstance)var6.next();
                     } while(skillInstance == null);

                     var9 = skillInstance.getSkill();
                  } while(!(var9 instanceof TensuraSkill));

                  TensuraSkill skill = (TensuraSkill)var9;
                  int j = 0;

                  for(int k = 1; k <= skill.modes(); ++k) {
                     String id = skill.modeLearningId(k);
                     if (!id.equals("None")) {
                        skillInstance.getOrCreateTag().m_128405_(id, 200);
                        skillInstance.markDirty();
                        ++j;
                     }
                  }

                  if (j > 0) {
                     ++i;
                  }
               }
            }
         }

         return 1;
      }
   }

   private static int toggleSkill(CommandSourceStack stack, Collection<? extends Entity> pTargets, ManasSkill skill) {
      Iterator var3 = pTargets.iterator();

      while(var3.hasNext()) {
         Entity entity = (Entity)var3.next();
         if (skill.getRegistryName() != null && entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            SkillStorage storage = SkillAPI.getSkillsFrom(living);
            Optional<ManasSkillInstance> instance = storage.getSkill(skill);
            if (!instance.isEmpty()) {
               ManasSkillInstance skillInstance = (ManasSkillInstance)instance.get();
               if (skill.canBeToggled(skillInstance, living) && skillInstance.canInteractSkill(living)) {
                  if (skillInstance.isToggled()) {
                     skillInstance.setToggled(false);
                     skillInstance.onToggleOff(living);
                     stack.m_81354_(Component.m_237110_("tensura.skill.toggle_off", new Object[]{skill.getName(), living.m_7755_()}), false);
                  } else {
                     skillInstance.setToggled(true);
                     skillInstance.onToggleOn(living);
                     stack.m_81354_(Component.m_237110_("tensura.skill.toggle_on", new Object[]{skill.getName(), living.m_7755_()}), false);
                  }

                  storage.syncChanges();
               }
            }
         }
      }

      return 1;
   }

   private static int toggleEverything(CommandSourceStack stack, Collection<? extends Entity> pTargets, boolean toggle, Predicate<ManasSkill> predicate) {
      Iterator var4 = pTargets.iterator();

      while(true) {
         Entity entity;
         do {
            if (!var4.hasNext()) {
               return 1;
            }

            entity = (Entity)var4.next();
         } while(!(entity instanceof LivingEntity));

         LivingEntity living = (LivingEntity)entity;
         int i = 0;
         Iterator var8 = SkillAPI.getSkillsFrom(living).getLearnedSkills().iterator();

         while(var8.hasNext()) {
            ManasSkillInstance instance = (ManasSkillInstance)var8.next();
            if (predicate.test(instance.getSkill()) && instance.getSkill().canBeToggled(instance, living) && instance.getSkill().canInteractSkill(instance, living) && instance.isToggled() != toggle) {
               instance.setToggled(toggle);
               if (toggle) {
                  instance.onToggleOn(living);
               } else {
                  instance.onToggleOff(living);
               }

               ++i;
            }
         }

         String message = toggle ? "tensura.skill.toggle_all.on" : "tensura.skill.toggle_all.off";
         stack.m_81354_(Component.m_237110_(message, new Object[]{i, living.m_7755_()}), false);
      }
   }

   private static int setRace(CommandSourceStack stack, Collection<? extends Player> pTargets, Race race, boolean resetEP, boolean grantIntrinsic) {
      Iterator var5 = pTargets.iterator();

      while(var5.hasNext()) {
         Player player = (Player)var5.next();
         TensuraPlayerCapability.getFrom(player).ifPresent((data) -> {
            data.setRace(player, race, resetEP);
            data.setTrackedEvolution(player, (Race)null);
         });
         if (grantIntrinsic) {
            List<TensuraSkill> intrinsic = race.getIntrinsicSkills(player);
            if (!intrinsic.isEmpty()) {
               Iterator var8 = intrinsic.iterator();

               while(var8.hasNext()) {
                  ManasSkill skill = (ManasSkill)var8.next();
                  ManasSkillInstance instance = new ManasSkillInstance(skill);
                  if (SkillAPI.getSkillsFrom(player).learnSkill(instance)) {
                     player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                     if (instance.canBeToggled(player)) {
                        instance.setToggled(true);
                        instance.onToggleOn(player);
                     }
                  }
               }
            }
         }

         ResetScrollItem.resetFlight(player);
         TensuraPlayerCapability.sync(player);
         TensuraEPCapability.getEP(player);
         sendSuccess(stack, player, Component.m_237110_("tensura.command.race.edit", new Object[]{player.m_7755_(), Objects.requireNonNull(race.getName())}));
      }

      return 1;
   }

   private static int setMajin(CommandSourceStack stack, Collection<? extends Entity> pTargets, boolean majin) {
      Iterator var3 = pTargets.iterator();

      while(var3.hasNext()) {
         Entity pEntity = (Entity)var3.next();
         if (pEntity instanceof LivingEntity) {
            LivingEntity entity = (LivingEntity)pEntity;
            TensuraEPCapability.setMajin(entity, majin);
            sendSuccess(stack, pEntity, Component.m_237110_("tensura.command.race.majin", new Object[]{entity.m_7755_(), majin}));
         }
      }

      return 1;
   }

   private static int setMagicule(CommandSourceStack stack, Collection<? extends Player> pTargets, double magicule, boolean add) {
      Iterator var5 = pTargets.iterator();

      while(var5.hasNext()) {
         Player player = (Player)var5.next();
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            cap.setMagicule(magicule == -1.0D ? player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()) : (add ? cap.getMagicule() + magicule : magicule));
            sendSuccess(stack, player, Component.m_237110_("tensura.command.magicule.set", new Object[]{player.m_7755_(), cap.getMagicule()}));
         });
         TensuraPlayerCapability.sync(player);
      }

      return 1;
   }

   private static int setMaxMagicule(CommandSourceStack stack, Collection<? extends Player> pTargets, double magicule, boolean resetCurrent, boolean add) {
      Iterator var6 = pTargets.iterator();

      while(var6.hasNext()) {
         Player player = (Player)var6.next();
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            cap.setBaseMagicule(add ? cap.getMagicule() + magicule : magicule, player);
            if (resetCurrent) {
               cap.setMagicule(cap.getBaseMagicule());
            }

            sendSuccess(stack, player, Component.m_237110_("tensura.command.magicule.set_max", new Object[]{player.m_7755_(), cap.getBaseMagicule()}));
         });
         TensuraPlayerCapability.sync(player);
         TensuraEPCapability.updateEP(player);
      }

      return 1;
   }

   private static int setAura(CommandSourceStack stack, Collection<? extends Player> pTargets, double aura, boolean add) {
      Iterator var5 = pTargets.iterator();

      while(var5.hasNext()) {
         Player player = (Player)var5.next();
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            cap.setAura(aura == -1.0D ? player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get()) : (add ? cap.getAura() + aura : aura));
            sendSuccess(stack, player, Component.m_237110_("tensura.command.aura.set", new Object[]{player.m_7755_(), cap.getAura()}));
         });
         TensuraPlayerCapability.sync(player);
      }

      return 1;
   }

   private static int setMaxAura(CommandSourceStack stack, Collection<? extends Player> pTargets, double aura, boolean resetCurrent, boolean add) {
      Iterator var6 = pTargets.iterator();

      while(var6.hasNext()) {
         Player player = (Player)var6.next();
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            cap.setBaseAura(add ? cap.getAura() + aura : aura, player);
            if (resetCurrent) {
               cap.setAura(cap.getBaseAura());
            }

            sendSuccess(stack, player, Component.m_237110_("tensura.command.aura.set_max", new Object[]{player.m_7755_(), cap.getBaseAura()}));
         });
         TensuraPlayerCapability.sync(player);
         TensuraEPCapability.updateEP(player);
      }

      return 1;
   }

   private static int setSpiritualHP(CommandSourceStack stack, Collection<? extends Entity> pTargets, double HP, boolean add) {
      Iterator var5 = pTargets.iterator();

      while(var5.hasNext()) {
         Entity entity = (Entity)var5.next();
         if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            TensuraEPCapability.getFrom(living).ifPresent((cap) -> {
               cap.setSpiritualHealth(HP == -1.0D ? living.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get()) : (add ? cap.getSpiritualHealth() + HP : HP));
               sendSuccess(stack, entity, Component.m_237110_("tensura.command.spiritual.set", new Object[]{entity.m_7755_(), cap.getSpiritualHealth()}));
            });
            TensuraEPCapability.sync(living);
         }
      }

      return 1;
   }

   private static int setEP(CommandSourceStack stack, Collection<? extends Entity> pTargets, double EP, boolean add, boolean update) {
      Iterator var6 = pTargets.iterator();

      while(var6.hasNext()) {
         Entity entity = (Entity)var6.next();
         if (entity instanceof Player) {
            Player player = (Player)entity;
            double half = EP / 2.0D;
            TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
               cap.setBaseMagicule(add ? cap.getBaseMagicule() + half : half, player);
               cap.setMagicule(cap.getBaseMagicule());
               cap.setBaseAura(add ? cap.getBaseAura() + half : half, player);
               cap.setAura(cap.getBaseAura());
               sendSuccess(stack, entity, Component.m_237110_("tensura.command.ep.set", new Object[]{entity.m_7755_(), cap.getBaseMagicule() + cap.getBaseAura()}));
            });
            TensuraPlayerCapability.sync(player);
         } else if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            TensuraEPCapability.getFrom(living).ifPresent((cap) -> {
               cap.setEP(living, add ? cap.getEP() + EP : EP, update);
               cap.setCurrentEP(living, cap.getEP());
               sendSuccess(stack, entity, Component.m_237110_("tensura.command.ep.set", new Object[]{entity.m_7755_(), cap.getEP()}));
            });
         }
      }

      return 1;
   }

   private static int setResetCounter(CommandSourceStack stack, Collection<? extends Player> pTargets, int i, boolean add) {
      Iterator var4 = pTargets.iterator();

      while(var4.hasNext()) {
         Player player = (Player)var4.next();
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            cap.setResetCounter(add ? cap.getResetCounter() + i : i);
            sendSuccess(stack, player, Component.m_237110_("tensura.command.reset_counter.set", new Object[]{player.m_7755_(), cap.getResetCounter()}));
            TensuraPlayerCapability.sync(player);
         });
      }

      return 1;
   }

   private static int setName(CommandSourceStack stack, Collection<? extends Entity> pTargets, @Nullable String name) {
      Iterator var3 = pTargets.iterator();

      while(var3.hasNext()) {
         Entity pEntity = (Entity)var3.next();
         if (pEntity instanceof LivingEntity) {
            LivingEntity entity = (LivingEntity)pEntity;
            TensuraEPCapability.getFrom(entity).ifPresent((cap) -> {
               cap.setName(name);
            });
            TensuraEPCapability.sync(entity);
            if (name != null) {
               sendSuccess(stack, pEntity, Component.m_237110_("tensura.command.owner.name.set", new Object[]{entity.m_7755_(), name}));
            } else {
               sendSuccess(stack, pEntity, Component.m_237110_("tensura.command.owner.name.remove", new Object[]{entity.m_7755_()}), ChatFormatting.RED);
            }
         }
      }

      return 1;
   }

   private static int setOwner(CommandSourceStack stack, Collection<? extends Entity> pTargets, @Nullable Entity owner, boolean temp) {
      Iterator var4 = pTargets.iterator();

      while(var4.hasNext()) {
         Entity pEntity = (Entity)var4.next();
         if (pEntity instanceof LivingEntity) {
            LivingEntity entity = (LivingEntity)pEntity;
            UUID uuid = owner == null ? null : owner.m_20148_();
            TensuraEPCapability.getFrom(entity).ifPresent((cap) -> {
               if (temp) {
                  cap.setTemporaryOwner(uuid);
               } else {
                  cap.setPermanentOwner(uuid);
               }

               if (owner instanceof Player) {
                  Player player = (Player)owner;
                  if (pEntity instanceof TamableAnimal) {
                     TamableAnimal animal = (TamableAnimal)pEntity;
                     animal.m_21828_(player);
                  } else if (pEntity instanceof TensuraHorseEntity) {
                     TensuraHorseEntity horse = (TensuraHorseEntity)pEntity;
                     horse.m_30637_(player);
                  }
               }

            });
            TensuraEPCapability.sync(entity);
            String component;
            if (owner != null) {
               component = temp ? "tensura.command.owner.temporary.set" : "tensura.command.owner.permanent.set";
               sendSuccess(stack, pEntity, Component.m_237110_(component, new Object[]{entity.m_7755_(), owner.m_7755_()}));
            } else {
               component = temp ? "tensura.command.owner.temporary.remove" : "tensura.command.owner.permanent.remove";
               sendSuccess(stack, pEntity, Component.m_237110_(component, new Object[]{entity.m_7755_()}), ChatFormatting.RED);
            }
         }
      }

      return 1;
   }

   private static int setNeutral(CommandSourceStack stack, Collection<? extends Entity> pTargets, Entity target, boolean neutral) {
      Iterator var4 = pTargets.iterator();

      while(var4.hasNext()) {
         Entity pEntity = (Entity)var4.next();
         if (pEntity instanceof LivingEntity) {
            LivingEntity entity = (LivingEntity)pEntity;
            UUID uuid = target.m_20148_();
            TensuraEPCapability.getFrom(entity).ifPresent((cap) -> {
               if (neutral) {
                  cap.addNeutralTarget(uuid);
               } else {
                  cap.removeNeutralTarget(uuid);
               }

            });
            TensuraEPCapability.sync(entity);
            String component = neutral ? "tensura.command.owner.neutral.add" : "tensura.command.owner.neutral.remove";
            sendSuccess(stack, pEntity, Component.m_237110_(component, new Object[]{entity.m_7755_(), target.m_7755_()}));
         }
      }

      return 1;
   }

   private static int getSkill(CommandSourceStack stack, @Nullable Entity entity, ManasSkill skill) {
      if (entity == null) {
         return 1;
      } else {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         Optional<ManasSkillInstance> optional = storage.getSkill(skill);
         if (optional.isEmpty()) {
            stack.m_81352_(Component.m_237110_("tensura.skill.do_not_have", new Object[]{entity.m_7755_(), skill.getName()}));
         } else {
            stack.m_81354_(Component.m_237110_("tensura.skill.skill_get", new Object[]{entity.m_7755_(), skill.getName(), ((ManasSkillInstance)optional.get()).toNBT().m_7916_()}).m_130940_(ChatFormatting.AQUA), true);
         }

         return 1;
      }
   }

   private static int getSkillList(CommandSourceStack stack, @Nullable Entity entity, Predicate<ManasSkill> predicate) {
      return getSkillList(stack, entity, predicate, false);
   }

   private static int getSkillList(CommandSourceStack stack, @Nullable Entity entity, Predicate<ManasSkill> predicate, boolean excludeLearning) {
      if (entity == null) {
         return 1;
      } else {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         MutableComponent component = null;
         List<ManasSkill> filteredSkillList = new ArrayList(storage.getLearnedSkills().stream().filter((skillx) -> {
            return !excludeLearning || skillx.getMastery() >= 0;
         }).map(ManasSkillInstance::getSkill).filter((skillx) -> {
            return skillx.getName() != null;
         }).filter(predicate).sorted(Comparator.comparing((skillx) -> {
            return (ResourceLocation)Objects.requireNonNull(SkillAPI.getSkillRegistry().getKey(skillx));
         })).toList());
         Iterator var7 = filteredSkillList.iterator();

         while(var7.hasNext()) {
            ManasSkill manasSkill = (ManasSkill)var7.next();
            MutableComponent name = manasSkill.getName();
            if (manasSkill instanceof TensuraSkill) {
               TensuraSkill skill = (TensuraSkill)manasSkill;
               name = skill.getColoredName();
            }

            if (name != null) {
               if (component == null) {
                  component = name;
               } else {
                  component = component.m_7220_(Component.m_237113_(", ").m_130940_(ChatFormatting.WHITE)).m_7220_(name);
               }
            }
         }

         if (component == null) {
            stack.m_81352_(Component.m_237110_("tensura.skill.skill_list.empty", new Object[]{entity.m_7755_()}));
         } else {
            stack.m_81354_(Component.m_237110_("tensura.skill.skill_list", new Object[]{entity.m_7755_(), component}), false);
         }

         return 1;
      }
   }

   private static int getSpiritList(CommandSourceStack stack, @Nullable Player player) {
      if (player == null) {
         return 1;
      } else {
         MutableComponent component = null;
         MagicElemental[] var3 = MagicElemental.values();
         int var4 = var3.length;

         for(int var5 = 0; var5 < var4; ++var5) {
            MagicElemental elemental = var3[var5];
            int level = TensuraSkillCapability.getSpiritLevel(player, elemental.getId());
            if (level > 0) {
               MutableComponent name = elemental.getName().m_130940_(elemental.getChatFormatting()).m_130946_(": " + level);
               if (component == null) {
                  component = name;
               } else {
                  component = component.m_7220_(Component.m_237113_(", ").m_130940_(ChatFormatting.WHITE)).m_7220_(name);
               }
            }
         }

         if (component == null) {
            stack.m_81352_(Component.m_237110_("tensura.command.spirit.list.empty", new Object[]{player.m_7755_()}));
         } else {
            stack.m_81354_(Component.m_237110_("tensura.command.spirit.list", new Object[]{player.m_7755_(), component}), false);
         }

         return 1;
      }
   }

   private static int checkCounterResetRequirement(CommandSourceStack stack, @Nullable ServerPlayer player) {
      if (player == null) {
         return 1;
      } else {
         Race race = TensuraPlayerCapability.getRace(player);
         boolean finalRace = race != null && race.getNextEvolutions(player).isEmpty();
         boolean awakened = TensuraPlayerCapability.isTrueHero(player) || TensuraPlayerCapability.isTrueDemonLord(player);
         boolean ifrit = player.m_8951_().m_13015_(((StatType)TensuraStats.BOSS_KILLED.get()).m_12902_((EntityType)TensuraEntityTypes.IFRIT.get())) >= 1;
         boolean orc = player.m_8951_().m_13015_(((StatType)TensuraStats.BOSS_KILLED.get()).m_12902_((EntityType)TensuraEntityTypes.ORC_DISASTER.get())) >= 1;
         boolean colossus = player.m_8951_().m_13015_(((StatType)TensuraStats.BOSS_KILLED.get()).m_12902_((EntityType)TensuraEntityTypes.ELEMENTAL_COLOSSUS.get())) >= 1;
         boolean charybdis = player.m_8951_().m_13015_(((StatType)TensuraStats.BOSS_KILLED.get()).m_12902_((EntityType)TensuraEntityTypes.CHARYBDIS.get())) >= 1;
         boolean hinata = player.m_8951_().m_13015_(Stats.f_12986_.m_12902_((EntityType)TensuraEntityTypes.HINATA_SAKAGUCHI.get())) >= 1;
         if (finalRace && awakened && ifrit && orc && colossus && charybdis && hinata) {
            stack.m_81354_(Component.m_237110_("tensura.command.reset_counter.check.met", new Object[]{player.m_7755_()}).m_130940_(ChatFormatting.GOLD), false);
         } else {
            stack.m_81354_(Component.m_237110_("tensura.command.reset_counter.check", new Object[]{player.m_7755_(), finalRace, awakened, ifrit, orc, colossus, charybdis, hinata}).m_130940_(ChatFormatting.YELLOW), false);
         }

         return 1;
      }
   }

   private static int trulyUniqueRemove(CommandSourceStack stack, ManasSkill skill) {
      ServerLevel level = stack.m_81372_();
      if (!level.m_46469_().m_46207_(TensuraGameRules.TRULY_UNIQUE)) {
         stack.m_81352_(Component.m_237115_("tensura.truly_unique.off"));
         return 1;
      } else {
         UniqueSkillSaveData saveData = UniqueSkillSaveData.get(level.m_7654_().m_129783_());
         if (!saveData.hasSkill(skill.getRegistryName())) {
            stack.m_81352_(Component.m_237110_("tensura.truly_unique.do_not_have", new Object[]{skill.getName()}).m_130940_(ChatFormatting.RED));
         } else {
            saveData.removeSkill(skill.getRegistryName());
            stack.m_81354_(Component.m_237110_("tensura.truly_unique.removed", new Object[]{skill.getName()}).m_130940_(ChatFormatting.DARK_GREEN), true);
         }

         return 1;
      }
   }

   private static int trulyUniqueCheck(CommandSourceStack stack, ManasSkill skill) {
      ServerLevel level = stack.m_81372_();
      if (!level.m_46469_().m_46207_(TensuraGameRules.TRULY_UNIQUE)) {
         stack.m_81352_(Component.m_237115_("tensura.truly_unique.off"));
         return 1;
      } else {
         UniqueSkillSaveData saveData = UniqueSkillSaveData.get(level.m_7654_().m_129783_());
         ResourceLocation location = skill.getRegistryName();
         if (saveData.hasSkill(location)) {
            UUID uuid = (UUID)saveData.getSkillMap().get(location);
            Optional<GameProfile> player = level.m_7654_().m_129927_().m_11002_(uuid);
            String playerUUID = player.isPresent() ? ((GameProfile)player.get()).getName() : String.valueOf(uuid);
            stack.m_81354_(Component.m_237110_("tensura.truly_unique.already_have", new Object[]{skill.getName(), playerUUID}).m_130940_(ChatFormatting.DARK_GREEN), true);
         } else {
            stack.m_81354_(Component.m_237110_("tensura.truly_unique.do_not_have", new Object[]{skill.getName()}).m_130940_(ChatFormatting.RED), true);
         }

         return 1;
      }
   }

   private static int getTrulyUniqueTakenList(CommandSourceStack stack) {
      ServerLevel level = stack.m_81372_();
      MutableComponent component = null;
      Map<ResourceLocation, UUID> skillMap = UniqueSkillSaveData.get(level.m_7654_().m_129783_()).getSkillMap();
      List<ResourceLocation> list = skillMap.keySet().stream().toList();
      Iterator var5 = list.iterator();

      while(var5.hasNext()) {
         ResourceLocation location = (ResourceLocation)var5.next();
         ManasSkill manasSkill = (ManasSkill)SkillAPI.getSkillRegistry().getValue(location);
         if (manasSkill != null) {
            MutableComponent name = manasSkill.getName();
            if (manasSkill instanceof TensuraSkill) {
               TensuraSkill skill = (TensuraSkill)manasSkill;
               name = skill.getColoredName();
            }

            if (name != null) {
               UUID uuid = (UUID)skillMap.get(location);
               Optional<GameProfile> player = level.m_7654_().m_129927_().m_11002_(uuid);
               String playerUUID = player.isPresent() ? ((GameProfile)player.get()).getName() : String.valueOf(uuid);
               name = name.m_130946_(" - ").m_7220_(Component.m_237113_(playerUUID).m_130940_(ChatFormatting.LIGHT_PURPLE));
               if (component == null) {
                  component = name;
               } else {
                  component = component.m_7220_(Component.m_237113_(", ").m_130940_(ChatFormatting.WHITE)).m_7220_(name);
               }
            }
         }
      }

      if (component == null) {
         stack.m_81352_(Component.m_237115_("tensura.truly_unique.list.taken.empty"));
      } else {
         stack.m_81354_(Component.m_237110_("tensura.truly_unique.list.taken", new Object[]{component}), false);
      }

      return 1;
   }

   private static int getTrulyUniqueRemainList(CommandSourceStack stack) {
      MutableComponent component = null;
      ServerLevel level = stack.m_81372_();
      List<ManasSkill> filteredSkillList = RaceSelectionMenu.getReincarnationSkills(level, true, (Player)null);
      Iterator var4 = filteredSkillList.iterator();

      while(var4.hasNext()) {
         ManasSkill manasSkill = (ManasSkill)var4.next();
         MutableComponent name = manasSkill.getName();
         if (manasSkill instanceof TensuraSkill) {
            TensuraSkill skill = (TensuraSkill)manasSkill;
            name = skill.getColoredName();
         }

         if (name != null) {
            if (component == null) {
               component = name;
            } else {
               component = component.m_7220_(Component.m_237113_(", ").m_130940_(ChatFormatting.WHITE)).m_7220_(name);
            }
         }
      }

      if (component == null) {
         stack.m_81352_(Component.m_237115_("tensura.truly_unique.list.remain.empty"));
      } else {
         stack.m_81354_(Component.m_237110_("tensura.truly_unique.list.remain", new Object[]{component}), false);
      }

      return 1;
   }

   private static void sendSuccess(CommandSourceStack stack, Entity entity, MutableComponent component) {
      sendSuccess(stack, entity, component, ChatFormatting.DARK_GREEN);
   }

   private static void sendSuccess(CommandSourceStack stack, Entity entity, MutableComponent component, ChatFormatting formatting) {
      if (stack.m_230896_() != entity) {
         entity.m_213846_(component);
      }

      stack.m_81354_(component.m_130940_(formatting), true);
   }
}
