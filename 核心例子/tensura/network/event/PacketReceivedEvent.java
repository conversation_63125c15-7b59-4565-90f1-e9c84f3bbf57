package com.github.manasmods.tensura.network.event;

import net.minecraftforge.eventbus.api.Event;
import net.minecraftforge.network.NetworkEvent.Context;

public class PacketReceivedEvent extends Event {
   private final Object message;
   private final Context ctx;
   private boolean handled = false;

   public PacketReceivedEvent(Object message, Context ctx) {
      this.message = message;
      this.ctx = ctx;
   }

   public Object getMessage() {
      return this.message;
   }

   public Context getCtx() {
      return this.ctx;
   }

   public boolean isHandled() {
      return this.handled;
   }

   public void setHandled(boolean handled) {
      this.handled = handled;
   }
}
