package com.github.manasmods.tensura.network.play2client;

import java.util.function.Supplier;
import net.minecraft.core.BlockPos;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestFxSpawningPacket {
   private final ResourceLocation resourceLocation;
   private final int entityId;
   private final BlockPos pos;
   private final double xOff;
   private final double yOff;
   private final double zOff;
   private final double xRot;
   private final double yRot;
   private final double zRot;
   private final int delay;
   private final boolean multi;
   private final boolean forceDeath;

   public RequestFxSpawningPacket(ResourceLocation resourceLocation, int entityId, double xOff, double yOff, double zOff, double xRot, double yRot, double zRot, boolean multi, boolean forceDeath) {
      this.resourceLocation = resourceLocation;
      this.entityId = entityId;
      this.xOff = xOff;
      this.yOff = yOff;
      this.zOff = zOff;
      this.xRot = xRot;
      this.yRot = yRot;
      this.zRot = zRot;
      this.delay = 0;
      this.multi = multi;
      this.forceDeath = forceDeath;
      this.pos = BlockPos.f_121853_;
   }

   public RequestFxSpawningPacket(ResourceLocation resourceLocation, int entityId, double xOff, double yOff, double zOff, double xRot, double yRot, double zRot, boolean multi) {
      this.resourceLocation = resourceLocation;
      this.entityId = entityId;
      this.xOff = xOff;
      this.yOff = yOff;
      this.zOff = zOff;
      this.xRot = xRot;
      this.yRot = yRot;
      this.zRot = zRot;
      this.delay = 0;
      this.multi = multi;
      this.forceDeath = false;
      this.pos = BlockPos.f_121853_;
   }

   public RequestFxSpawningPacket(ResourceLocation resourceLocation, int entityId, double xOff, double yOff, double zOff, boolean multi, boolean forceDeath) {
      this.resourceLocation = resourceLocation;
      this.entityId = entityId;
      this.xOff = xOff;
      this.yOff = yOff;
      this.zOff = zOff;
      this.xRot = 0.0D;
      this.yRot = 0.0D;
      this.zRot = 0.0D;
      this.delay = 0;
      this.multi = multi;
      this.forceDeath = forceDeath;
      this.pos = BlockPos.f_121853_;
   }

   public RequestFxSpawningPacket(ResourceLocation resourceLocation, int entityId, double xOff, double yOff, double zOff, boolean multi) {
      this.resourceLocation = resourceLocation;
      this.entityId = entityId;
      this.xOff = xOff;
      this.yOff = yOff;
      this.zOff = zOff;
      this.xRot = 0.0D;
      this.yRot = 0.0D;
      this.zRot = 0.0D;
      this.delay = 0;
      this.multi = multi;
      this.forceDeath = false;
      this.pos = BlockPos.f_121853_;
   }

   public RequestFxSpawningPacket(ResourceLocation resourceLocation, BlockPos pos, double xOff, double yOff, double zOff, int delay, boolean multi) {
      this.resourceLocation = resourceLocation;
      this.pos = pos;
      this.xOff = xOff;
      this.yOff = yOff;
      this.zOff = zOff;
      this.xRot = 0.0D;
      this.yRot = 0.0D;
      this.zRot = 0.0D;
      this.delay = delay;
      this.multi = multi;
      this.forceDeath = false;
      this.entityId = -1;
   }

   public RequestFxSpawningPacket(FriendlyByteBuf buf) {
      this.resourceLocation = buf.m_130281_();
      this.entityId = buf.readInt();
      this.pos = buf.m_130135_();
      this.xOff = buf.readDouble();
      this.yOff = buf.readDouble();
      this.zOff = buf.readDouble();
      this.xRot = buf.readDouble();
      this.yRot = buf.readDouble();
      this.zRot = buf.readDouble();
      this.delay = buf.readInt();
      this.multi = buf.readBoolean();
      this.forceDeath = buf.readBoolean();
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.m_130085_(this.resourceLocation);
      buf.writeInt(this.entityId);
      buf.m_130064_(this.pos);
      buf.writeDouble(this.xOff);
      buf.writeDouble(this.yOff);
      buf.writeDouble(this.zOff);
      buf.writeDouble(this.xRot);
      buf.writeDouble(this.yRot);
      buf.writeDouble(this.zRot);
      buf.writeInt(this.delay);
      buf.writeBoolean(this.multi);
      buf.writeBoolean(this.forceDeath);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> {
            return () -> {
               if (this.entityId == -1) {
                  ClientAccess.summonFX(this.resourceLocation, this.pos, this.xOff, this.yOff, this.zOff, this.delay, this.multi);
               } else {
                  ClientAccess.summonFX(this.resourceLocation, this.entityId, this.xOff, this.yOff, this.zOff, this.xRot, this.yRot, this.zRot, this.multi, this.forceDeath);
               }

            };
         });
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   public RequestFxSpawningPacket(ResourceLocation resourceLocation, int entityId, BlockPos pos, double xOff, double yOff, double zOff, double xRot, double yRot, double zRot, int delay, boolean multi, boolean forceDeath) {
      this.resourceLocation = resourceLocation;
      this.entityId = entityId;
      this.pos = pos;
      this.xOff = xOff;
      this.yOff = yOff;
      this.zOff = zOff;
      this.xRot = xRot;
      this.yRot = yRot;
      this.zRot = zRot;
      this.delay = delay;
      this.multi = multi;
      this.forceDeath = forceDeath;
   }
}
