package com.github.manasmods.tensura.network.play2client;

import java.util.function.Supplier;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.world.level.ChunkPos;
import net.minecraftforge.network.NetworkEvent.Context;

public class SyncMagiculePacket {
   private final ChunkPos pos;
   private final CompoundTag tag;

   public SyncMagiculePacket(FriendlyByteBuf buf) {
      this.pos = buf.m_178383_();
      this.tag = buf.m_130260_();
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.m_178341_(this.pos);
      buf.m_130079_(this.tag);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ClientAccess.updateMagicule(this);
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   public SyncMagiculePacket(ChunkPos pos, CompoundTag tag) {
      this.pos = pos;
      this.tag = tag;
   }

   public ChunkPos getPos() {
      return this.pos;
   }

   public CompoundTag getTag() {
      return this.tag;
   }
}
