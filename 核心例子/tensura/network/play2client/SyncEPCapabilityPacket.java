package com.github.manasmods.tensura.network.play2client;

import com.github.manasmods.tensura.capability.ep.ITensuraEPCapability;
import java.util.function.Supplier;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent.Context;

public class SyncEPCapabilityPacket {
   private final CompoundTag tag;
   private final int entityId;

   public SyncEPCapabilityPacket(FriendlyByteBuf buf) {
      this.tag = buf.m_130261_();
      this.entityId = buf.readInt();
   }

   public SyncEPCapabilityPacket(ITensuraEPCapability data, int entityId) {
      this.tag = (CompoundTag)data.serializeNBT();
      this.entityId = entityId;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.m_130079_(this.tag);
      buf.writeInt(this.entityId);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> {
            return () -> {
               ClientAccess.updateEPCapability(this.entityId, this.tag);
            };
         });
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }
}
