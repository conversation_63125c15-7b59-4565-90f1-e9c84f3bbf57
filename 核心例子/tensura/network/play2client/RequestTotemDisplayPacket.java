package com.github.manasmods.tensura.network.play2client;

import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestTotemDisplayPacket {
   private final ItemStack stack;

   public RequestTotemDisplayPacket(FriendlyByteBuf buf) {
      this.stack = buf.m_130267_();
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.m_130055_(this.stack);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> {
            return () -> {
               ClientAccess.displayItemLikeTotem(this.stack);
            };
         });
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   public RequestTotemDisplayPacket(ItemStack stack) {
      this.stack = stack;
   }
}
