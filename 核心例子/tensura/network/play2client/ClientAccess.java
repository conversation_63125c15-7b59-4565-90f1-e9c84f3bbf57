package com.github.manasmods.tensura.network.play2client;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.api.entity.subclass.ILivingPartEntity;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.magicule.MagiculeChunkCapabilityImpl;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.capability.smithing.SmithingCapability;
import com.github.manasmods.tensura.client.screen.FalsifierScreen;
import com.github.manasmods.tensura.client.screen.GreatSageCraftingScreen;
import com.github.manasmods.tensura.client.screen.GreatSageRefiningScreen;
import com.github.manasmods.tensura.client.screen.HumanoidNPCScreen;
import com.github.manasmods.tensura.client.screen.ResearcherSpatialStorageScreen;
import com.github.manasmods.tensura.client.screen.SettingsScreen;
import com.github.manasmods.tensura.client.screen.SpatialStorageScreen;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.menu.GreatSageCraftingMenu;
import com.github.manasmods.tensura.menu.GreatSageRefiningMenu;
import com.github.manasmods.tensura.menu.HumanoidNPCMenu;
import com.github.manasmods.tensura.menu.ResearcherSpatialStorageMenu;
import com.github.manasmods.tensura.menu.SpatialStorageMenu;
import com.github.manasmods.tensura.menu.container.SpatialStorageContainer;
import com.lowdragmc.photon.client.fx.BlockEffect;
import com.lowdragmc.photon.client.fx.EntityEffect;
import com.lowdragmc.photon.client.fx.FX;
import com.lowdragmc.photon.client.fx.FXHelper;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;
import net.minecraft.client.Minecraft;
import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.ContainerLevelAccess;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.network.NetworkEvent.Context;

class ClientAccess {
   static final Minecraft minecraft = Minecraft.m_91087_();

   static void updatePlayerCapability(int entityId, CompoundTag tag) {
      if (minecraft.f_91073_ != null) {
         Entity entity = minecraft.f_91073_.m_6815_(entityId);
         if (entity instanceof Player) {
            Player player = (Player)entity;
            TensuraPlayerCapability.getFrom(player).ifPresent((data) -> {
               data.deserializeNBT(tag);
            });
            player.m_6210_();
         }

      }
   }

   static void updateSmithingCapability(int entityId, CompoundTag tag) {
      if (minecraft.f_91073_ != null) {
         Entity entity = minecraft.f_91073_.m_6815_(entityId);
         if (entity instanceof Player) {
            Player player = (Player)entity;
            SmithingCapability.getFrom(player).ifPresent((data) -> {
               data.deserializeNBT(tag);
            });
         }

      }
   }

   static void updateEffectsCapability(int entityId, CompoundTag tag) {
      if (minecraft.f_91073_ != null) {
         Entity entity = minecraft.f_91073_.m_6815_(entityId);
         if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            TensuraEffectsCapability.getFrom(living).ifPresent((data) -> {
               data.deserializeNBT(tag);
            });
         }

      }
   }

   static void updateEPCapability(int entityId, CompoundTag tag) {
      if (minecraft.f_91073_ != null) {
         Entity entity = minecraft.f_91073_.m_6815_(entityId);
         if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            TensuraEPCapability.getFrom(living).ifPresent((data) -> {
               data.deserializeNBT(tag);
            });
         }

      }
   }

   static void updateSkillCapability(int entityId, CompoundTag tag) {
      if (minecraft.f_91073_ != null) {
         Entity entity = minecraft.f_91073_.m_6815_(entityId);
         if (entity instanceof Player) {
            Player player = (Player)entity;
            TensuraSkillCapability.getFrom(player).ifPresent((data) -> {
               data.deserializeNBT(tag);
            });
         }

      }
   }

   public static void updateMagicule(SyncMagiculePacket packet) {
      if (minecraft.f_91073_ != null) {
         MagiculeChunkCapabilityImpl.get(minecraft.f_91073_.m_6325_(packet.getPos().f_45578_, packet.getPos().f_45579_)).deserializeNBT(packet.getTag());
      }
   }

   public static void handleClientboundMainScreenOpenPacket(ClientboundMainScreenOpenPacket message, Supplier<Context> context) {
      LocalPlayer player = Minecraft.m_91087_().f_91074_;
      if (player != null) {
         ClientLevel level = player.f_108545_;
         if (level.m_6815_(message.entityId) == player) {
            switch(message.screenID) {
            case 8:
               Minecraft.m_91087_().m_91152_(new SettingsScreen());
               break;
            case 9:
               Minecraft.m_91087_().m_91152_(new FalsifierScreen());
            }

         }
      }
   }

   public static void handleClientboundSpatialStorageOpenPacket(ClientboundSpatialStorageOpenPacket message, Supplier<Context> context) {
      LocalPlayer player = Minecraft.m_91087_().f_91074_;
      if (player != null && player.m_19879_() == message.userId) {
         ManasSkill skill = (ManasSkill)SkillAPI.getSkillRegistry().getValue(message.skill);
         if (skill != null) {
            Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(player).getSkill(skill);
            if (!optional.isEmpty()) {
               SpatialStorageContainer container = new SpatialStorageContainer(message.size, message.stackSize);
               SpatialStorageMenu menu = new SpatialStorageMenu(message.containerId, player.m_150109_(), player, container, skill);
               player.f_36096_ = menu;
               Minecraft.m_91087_().m_91152_(new SpatialStorageScreen(menu, player.m_150109_(), (Component)Objects.requireNonNull(skill.getName())));
            }
         }
      }
   }

   public static void handleClientboundResearcherStorageOpenPacket(ClientboundSpatialStorageOpenPacket message, Supplier<Context> context) {
      LocalPlayer player = Minecraft.m_91087_().f_91074_;
      if (player != null && player.m_19879_() == message.userId) {
         ManasSkill skill = (ManasSkill)SkillAPI.getSkillRegistry().getValue(message.skill);
         if (skill != null) {
            Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(player).getSkill(skill);
            if (!optional.isEmpty()) {
               SpatialStorageContainer container = new SpatialStorageContainer(message.size, message.stackSize);
               ResearcherSpatialStorageMenu menu = new ResearcherSpatialStorageMenu(message.containerId, player.m_150109_(), player, container, skill);
               player.f_36096_ = menu;
               Minecraft.m_91087_().m_91152_(new ResearcherSpatialStorageScreen(menu, player.m_150109_(), (Component)Objects.requireNonNull(skill.getName())));
            }
         }
      }
   }

   public static void handleClientboundSpatialCraftingOpenPacket(ClientboundSpatialStorageOpenPacket message, Supplier<Context> context) {
      LocalPlayer player = Minecraft.m_91087_().f_91074_;
      if (player != null && player.m_19879_() == message.userId) {
         ManasSkill skill = (ManasSkill)SkillAPI.getSkillRegistry().getValue(message.skill);
         if (skill != null) {
            Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(player).getSkill(skill);
            if (!optional.isEmpty()) {
               SpatialStorageContainer container = new SpatialStorageContainer(message.size, message.stackSize);
               GreatSageCraftingMenu menu = new GreatSageCraftingMenu(message.containerId, player.m_150109_(), player, container, skill, ContainerLevelAccess.m_39289_(player.f_19853_, player.m_20183_()));
               player.f_36096_ = menu;
               Minecraft.m_91087_().m_91152_(new GreatSageCraftingScreen(menu, player.m_150109_()));
            }
         }
      }
   }

   public static void handleClientboundSpatialRefiningOpenPacket(ClientboundSpatialStorageOpenPacket message, Supplier<Context> context) {
      LocalPlayer player = Minecraft.m_91087_().f_91074_;
      if (player != null && player.m_19879_() == message.userId) {
         ManasSkill skill = (ManasSkill)SkillAPI.getSkillRegistry().getValue(message.skill);
         if (skill != null) {
            Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(player).getSkill(skill);
            if (!optional.isEmpty()) {
               SpatialStorageContainer container = new SpatialStorageContainer(message.size, message.stackSize);
               GreatSageRefiningMenu menu = new GreatSageRefiningMenu(message.containerId, player.m_150109_(), player, container, skill, ContainerLevelAccess.m_39289_(player.f_19853_, player.m_20183_()));
               player.f_36096_ = menu;
               Minecraft.m_91087_().m_91152_(new GreatSageRefiningScreen(menu, player.m_150109_()));
            }
         }
      }
   }

   public static void handleClientboundNPCScreenOpenPacket(ClientboundNPCScreenOpenPacket message, Supplier<Context> context) {
      LocalPlayer player = Minecraft.m_91087_().f_91074_;
      if (player != null) {
         ClientLevel level = player.f_108545_;
         Entity entity = level.m_6815_(message.entityId);
         if (entity instanceof HumanoidNPCEntity) {
            HumanoidNPCEntity npc = (HumanoidNPCEntity)entity;
            if (!npc.m_21830_(player)) {
               return;
            }

            SimpleContainer simpleContainer = new SimpleContainer(message.size);
            HumanoidNPCMenu menu = new HumanoidNPCMenu(message.containerId, player.m_150109_(), simpleContainer, npc, message.EP);
            player.f_36096_ = menu;
            Minecraft.m_91087_().m_91152_(new HumanoidNPCScreen(menu, player.m_150109_(), npc, message.EP));
         }

      }
   }

   static void displayItemLikeTotem(ItemStack stack) {
      if (minecraft.f_91073_ != null) {
         minecraft.f_91063_.m_109113_(stack);
      }
   }

   public static void summonFX(ResourceLocation location, int entityId, double xOffset, double yOffset, double zOffset, double xRot, double yRot, double zRot, boolean multi, boolean forceDeath) {
      if (minecraft.f_91073_ != null) {
         FX fx = FXHelper.getFX(location);
         if (fx != null) {
            Entity entity = minecraft.f_91073_.m_6815_(entityId);
            EntityEffect entityEffect = new EntityEffect(fx, minecraft.f_91073_, entity);
            entityEffect.setXOffset(xOffset);
            entityEffect.setYOffset(yOffset);
            entityEffect.setZOffset(zOffset);
            entityEffect.setAllowMulti(multi);
            entityEffect.setForcedDeath(forceDeath);
            entityEffect.setRotation(xRot, yRot, zRot);
            entityEffect.start();
         }

      }
   }

   public static void summonFX(ResourceLocation location, BlockPos pos, double xOffset, double yOffset, double zOffset, int delay, boolean multi) {
      if (minecraft.f_91073_ != null) {
         FX fx = FXHelper.getFX(location);
         if (fx != null) {
            BlockEffect blockEffect = new BlockEffect(fx, minecraft.f_91073_, pos);
            blockEffect.setXOffset(xOffset);
            blockEffect.setYOffset(yOffset);
            blockEffect.setZOffset(zOffset);
            blockEffect.setAllowMulti(multi);
            blockEffect.setDelay(delay);
            blockEffect.start();
         }

      }
   }

   public static void removeClientSkill(ResourceLocation location, int entityId) {
      if (minecraft.f_91073_ != null) {
         Entity entity = minecraft.f_91073_.m_6815_(entityId);
         if (entity != null) {
            ManasSkill skill = (ManasSkill)SkillAPI.getSkillRegistry().getValue(location);
            if (skill != null) {
               SkillAPI.getSkillsFrom(entity).forgetSkill(skill);
            }
         }
      }
   }

   public static void updateDimension(int entityId) {
      if (minecraft.f_91073_ != null) {
         Entity entity = minecraft.f_91073_.m_6815_(entityId);
         if (entity != null) {
            entity.m_6210_();
         }
      }
   }

   public static void updatePartHurtAnimation(RequestPartHurtAnimation message) {
      if (minecraft.f_91073_ != null) {
         Entity part = minecraft.f_91073_.m_6815_(message.part);
         Entity parent = minecraft.f_91073_.m_6815_(message.parent);
         if (part instanceof ILivingPartEntity) {
            ILivingPartEntity multipart = (ILivingPartEntity)part;
            if (parent instanceof LivingEntity) {
               LivingEntity living = (LivingEntity)parent;
               multipart.onServerHurt(living);
            }
         }

      }
   }
}
