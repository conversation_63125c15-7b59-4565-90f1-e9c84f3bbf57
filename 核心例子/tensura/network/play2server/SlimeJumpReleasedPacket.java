package com.github.manasmods.tensura.network.play2server;

import com.github.manasmods.tensura.handler.RaceHandler;
import com.github.manasmods.tensura.handler.client.ClientRaceHandler;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.NetworkEvent.Context;

public class SlimeJumpReleasedPacket {
   public static final Map<UUID, Float> jumpChargeData = new ConcurrentHashMap();

   public SlimeJumpReleasedPacket() {
   }

   public SlimeJumpReleasedPacket(FriendlyByteBuf buf) {
   }

   public void toBytes(FriendlyByteBuf buf) {
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer player = ((Context)ctx.get()).getSender();
         if (player != null) {
            if (RaceHandler.isInFluid(player) || !player.m_6047_() || !player.m_20096_() && player.f_19789_ > 1.0F) {
               SlimeJumpChargePacket.chargingPlayers.remove(player.m_20148_());
               ClientRaceHandler.jumpChargingTicks = 0L;
               jumpChargeData.remove(player.m_20148_());
            } else if (player.m_20096_()) {
               if (SlimeJumpChargePacket.chargingPlayers.containsKey(player.m_20148_())) {
                  long startedCharging = (Long)SlimeJumpChargePacket.chargingPlayers.get(player.m_20148_());
                  long totalChargeMills = System.currentTimeMillis() - startedCharging;
                  float chargeInPercent = 0.33333334F * (float)Math.min(TimeUnit.MILLISECONDS.toSeconds(totalChargeMills), 3L);
                  jumpChargeData.put(player.m_20148_(), chargeInPercent);
                  SlimeJumpChargePacket.chargingPlayers.remove(player.m_20148_());
                  player.m_6135_();
               }
            }
         }
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }
}
