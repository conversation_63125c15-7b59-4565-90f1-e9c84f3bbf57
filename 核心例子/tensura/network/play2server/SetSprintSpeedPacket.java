package com.github.manasmods.tensura.network.play2server;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.util.Mth;
import net.minecraftforge.network.NetworkEvent.Context;

public class SetSprintSpeedPacket {
   private final double value;

   public SetSprintSpeedPacket(FriendlyByteBuf buf) {
      this.value = buf.readDouble();
   }

   public SetSprintSpeedPacket(double value) {
      this.value = value;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeDouble(this.value);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer serverPlayer = ((Context)ctx.get()).getSender();
         if (serverPlayer != null) {
            Race race = TensuraPlayerCapability.getRace(serverPlayer);
            if (race != null) {
               double newSpeed = Mth.m_14008_(this.value, race.getMovementSpeed(), race.getSprintSpeed());
               TensuraPlayerCapability.setSprintSpeed(serverPlayer, newSpeed);
            }
         }
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }
}
