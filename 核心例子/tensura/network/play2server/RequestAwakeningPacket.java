package com.github.manasmods.tensura.network.play2server;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestAwakeningPacket {
   private final boolean trueHero;

   public RequestAwakeningPacket(FriendlyByteBuf buf) {
      this.trueHero = buf.readBoolean();
   }

   public RequestAwakeningPacket(boolean trueHero) {
      this.trueHero = trueHero;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeBoolean(this.trueHero);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer player = ((Context)ctx.get()).getSender();
         if (player != null) {
            if (!RaceHelper.shouldNamingStopAwakening(player)) {
               TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                  if (!cap.isTrueDemonLord()) {
                     if (!cap.isTrueHero()) {
                        if (this.trueHero) {
                           if (!cap.isHeroEgg()) {
                              return;
                           }

                           if (cap.getRace() != null && cap.getRace().isMajin()) {
                              return;
                           }

                           if (!RaceHelper.fightingBossForHero(player)) {
                              return;
                           }

                           cap.setTrueHero(true);
                           TensuraPlayerCapability.sync(player);
                           RaceHelper.awakening(player, true);
                        } else {
                           if (!cap.isDemonLordSeed()) {
                              return;
                           }

                           int requirement = player.m_9236_().m_6106_().m_5470_().m_46215_(TensuraGameRules.DEMON_LORD_AWAKEN);
                           if (cap.getSoulPoints() < requirement * 1000) {
                              return;
                           }

                           cap.setSoulPoints(cap.getSoulPoints() - requirement * 1000);
                           cap.setTrueDemonLord(true);
                           TensuraPlayerCapability.sync(player);
                           RaceHelper.awakening(player, false);
                        }

                     }
                  }
               });
            }
         }
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }
}
