package com.github.manasmods.tensura.network.play2server.skill;

import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.menu.BattlewillSelectionMenu;
import com.github.manasmods.tensura.menu.MagicSelectionMenu;
import com.github.manasmods.tensura.menu.SkillMenu;
import com.github.manasmods.tensura.menu.SkillSelectionMenu;
import java.util.function.Supplier;
import net.minecraft.ChatFormatting;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestAbilityPresetPacket {
   private final String presetName;
   private final boolean active;
   private final int preset;

   public RequestAbilityPresetPacket(FriendlyByteBuf buf) {
      this.presetName = buf.m_130277_();
      this.preset = buf.readInt();
      this.active = buf.readBoolean();
   }

   public RequestAbilityPresetPacket(String presetName, int slot) {
      this.presetName = presetName;
      this.preset = slot;
      this.active = false;
   }

   public RequestAbilityPresetPacket(int slot, boolean active) {
      this.presetName = "None";
      this.preset = slot;
      this.active = active;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.m_130070_(this.presetName);
      buf.writeInt(this.preset);
      buf.writeBoolean(this.active);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer player = ((Context)ctx.get()).getSender();
         if (player != null && this.matchesAny(player.f_36096_)) {
            TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
               if (this.active) {
                  if (cap.getActivePreset() == this.preset) {
                     return;
                  }

                  cap.setActivePreset(this.preset);
                  TensuraSkillCapability.sync(player);
                  player.m_5661_(Component.m_237110_("tensura.skill.preset.changed", new Object[]{cap.getPresetName(this.preset)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), false);
               } else {
                  if (cap.getPresetName(this.preset).equals(this.presetName)) {
                     return;
                  }

                  cap.setPresetName(this.preset, this.presetName);
                  TensuraSkillCapability.sync(player);
               }

            });
         }

      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   private boolean matchesAny(AbstractContainerMenu playerMenu) {
      return !(playerMenu instanceof SkillSelectionMenu) && !(playerMenu instanceof BattlewillSelectionMenu) && !(playerMenu instanceof MagicSelectionMenu) ? playerMenu instanceof SkillMenu : this.active;
   }
}
