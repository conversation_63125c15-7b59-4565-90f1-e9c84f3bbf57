package com.github.manasmods.tensura.network.play2server.skill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.extra.SpatialMotionSkill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.entity.magic.misc.WarpPortalEntity;
import com.github.manasmods.tensura.menu.SpatialMenu;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import java.util.function.Supplier;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestSpatialActionPacket {
   private final RequestSpatialActionPacket.Action action;
   private double xPos;
   private double yPos;
   private double zPos;
   private final int savedWarpUsed;

   public RequestSpatialActionPacket(FriendlyByteBuf buf) {
      this.xPos = buf.readDouble();
      this.yPos = buf.readDouble();
      this.zPos = buf.readDouble();
      this.action = (RequestSpatialActionPacket.Action)buf.m_130066_(RequestSpatialActionPacket.Action.class);
      this.savedWarpUsed = buf.readInt();
   }

   public RequestSpatialActionPacket(double x, double y, double z, RequestSpatialActionPacket.Action action, int useSavedWarp) {
      this.xPos = x;
      this.yPos = y;
      this.zPos = z;
      this.action = action;
      this.savedWarpUsed = useSavedWarp;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeDouble(this.xPos);
      buf.writeDouble(this.yPos);
      buf.writeDouble(this.zPos);
      buf.m_130068_(this.action);
      buf.writeInt(this.savedWarpUsed);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer player = ((Context)ctx.get()).getSender();
         if (player != null) {
            int savedWarp = this.savedWarpUsed - 1;
            if (savedWarp != -1) {
               TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
                  this.xPos = cap.getWarpX(savedWarp);
                  this.yPos = cap.getWarpY(savedWarp);
                  this.zPos = cap.getWarpZ(savedWarp);
               });
            }

            if (player.m_9236_().m_6857_().m_61937_(new BlockPos(this.xPos, this.yPos, this.zPos))) {
               switch(this.action) {
               case WARP:
                  this.warp(player);
                  break;
               case PORTAL:
                  this.summonPortal(player);
               }
            } else {
               player.m_5661_(Component.m_237115_("tensura.skill.teleport.out_border").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
            }
         }

      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   private void warp(ServerPlayer player) {
      if (player.f_36096_ instanceof SpatialMenu) {
         double cost = SkillUtils.hasSkill(player, (ManasSkill)UniqueSkills.TRAVELER.get()) ? 5.0D : 10.0D;
         if (SkillHelper.outOfMagicule(player, cost * Math.sqrt(player.m_20275_(this.xPos, this.yPos, this.zPos)))) {
            return;
         }

         player.f_19839_ = 10;
         if (SkillUtils.canInstantWarp(player)) {
            SpatialMotionSkill.warp(player, this.xPos, this.yPos, this.zPos);
            return;
         }

         player.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.WARPING.get(), 200, 0, false, false, false));
         TensuraEffectsCapability.getFrom(player).ifPresent((cap) -> {
            cap.setWarpPos(this.xPos, this.yPos, this.zPos);
         });
         TensuraEffectsCapability.sync(player);
      }

   }

   private void summonPortal(ServerPlayer player) {
      if (player.f_36096_ instanceof SpatialMenu) {
         double cost = SkillUtils.hasSkill(player, (ManasSkill)UniqueSkills.TRAVELER.get()) ? 25.0D : 50.0D;
         if (SkillHelper.outOfMagicule(player, cost * Math.sqrt(player.m_20275_(this.xPos, this.yPos, this.zPos)))) {
            return;
         }

         Level level = player.m_9236_();
         BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, player, Fluid.NONE, 3.0D);
         BlockPos pos = result.m_82425_().m_7494_();
         level.m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_11888_, SoundSource.PLAYERS, 1.0F, 1.0F);
         WarpPortalEntity portal = new WarpPortalEntity(level);
         portal.m_6034_((double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_());
         portal.setOwnerUUID(player.m_20148_());
         Direction direction = Direction.m_122382_(player)[0].m_122424_();
         if (direction == Direction.UP) {
            direction = Direction.DOWN;
         }

         portal.setFacingDirection(direction);
         if (SkillUtils.canInstantWarp(player)) {
            portal.setInstant(true);
         }

         portal.setDestination(new BlockPos(this.xPos, this.yPos, this.zPos));
         level.m_7967_(portal);
      }

   }

   public static enum Action {
      WARP,
      PORTAL,
      NONE;

      // $FF: synthetic method
      private static RequestSpatialActionPacket.Action[] $values() {
         return new RequestSpatialActionPacket.Action[]{WARP, PORTAL, NONE};
      }
   }
}
