package com.github.manasmods.tensura.network.play2server.skill;

import com.github.manasmods.tensura.menu.ResearcherEnchantmentMenu;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestResearcherEnchantmentPacket {
   private final int id;
   private final int level;
   private final boolean enchant;

   public RequestResearcherEnchantmentPacket(FriendlyByteBuf buf) {
      this.id = buf.readInt();
      this.level = buf.readInt();
      this.enchant = buf.readBoolean();
   }

   public RequestResearcherEnchantmentPacket(int id) {
      this.id = id;
      this.level = 0;
      this.enchant = false;
   }

   public RequestResearcherEnchantmentPacket(int id, int level) {
      this.id = id;
      this.level = level;
      this.enchant = false;
   }

   public RequestResearcherEnchantmentPacket() {
      this.id = -9;
      this.level = 0;
      this.enchant = true;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeInt(this.id);
      buf.writeInt(this.level);
      buf.writeBoolean(this.enchant);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer sender = ((Context)ctx.get()).getSender();
         if (sender != null) {
            AbstractContainerMenu patt1485$temp = sender.f_36096_;
            if (patt1485$temp instanceof ResearcherEnchantmentMenu) {
               ResearcherEnchantmentMenu menu = (ResearcherEnchantmentMenu)patt1485$temp;
               if (this.enchant) {
                  menu.handleFinalizeEnchantment();
               } else {
                  menu.editSelection(this.id, this.level);
               }

               return;
            }
         }

      });
      ((Context)ctx.get()).setPacketHandled(true);
   }
}
