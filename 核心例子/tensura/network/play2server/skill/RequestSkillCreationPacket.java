package com.github.manasmods.tensura.network.play2server.skill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.unique.AntiSkill;
import com.github.manasmods.tensura.ability.skill.unique.CreatorSkill;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import java.util.Optional;
import java.util.function.Supplier;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestSkillCreationPacket {
   private final ResourceLocation skill;

   public RequestSkillCreationPacket(FriendlyByteBuf buf) {
      this.skill = buf.m_130281_();
   }

   public RequestSkillCreationPacket(ResourceLocation skill) {
      this.skill = skill;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.m_130085_(this.skill);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer player = ((Context)ctx.get()).getSender();
         if (player != null) {
            this.createSkill(player);
         }

      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   private void createSkill(ServerPlayer player) {
      SkillStorage storage = SkillAPI.getSkillsFrom(player);
      Optional<ManasSkillInstance> optionalCreator = storage.getSkill((ManasSkill)UniqueSkills.CREATOR.get());
      if (!optionalCreator.isEmpty()) {
         ManasSkillInstance creator = (ManasSkillInstance)optionalCreator.get();
         if (!creator.onCoolDown()) {
            ((CreatorSkill)UniqueSkills.CREATOR.get()).addMasteryPoint(creator, player, 5 + SkillUtils.getBonusMasteryPoint(creator, player, 5));
            creator.setCoolDown(1200);
            CompoundTag tag = creator.getOrCreateTag();
            ResourceLocation createdLocation = new ResourceLocation(tag.m_128461_("created_skill"));
            ManasSkill oldSkill;
            Optional optional;
            if (createdLocation.equals(this.skill)) {
               oldSkill = (ManasSkill)SkillAPI.getSkillRegistry().getValue(this.skill);
               if (oldSkill == null) {
                  return;
               }

               optional = storage.getSkill(oldSkill);
               if (optional.isPresent() && ((ManasSkillInstance)optional.get()).isTemporarySkill()) {
                  if (creator.isMastered(player) && oldSkill.equals(UniqueSkills.ANTI_SKILL.get())) {
                     ((AntiSkill)UniqueSkills.ANTI_SKILL.get()).addMasteryPoint((ManasSkillInstance)optional.get(), player, oldSkill.getMaxMastery());
                  }

                  ((ManasSkillInstance)optional.get()).setRemoveTime(1200);
                  player.m_9236_().m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  creator.markDirty();
                  storage.syncChanges();
                  return;
               }
            }

            oldSkill = (ManasSkill)SkillAPI.getSkillRegistry().getValue(createdLocation);
            if (oldSkill != null) {
               optional = storage.getSkill(oldSkill);
               if (optional.isPresent() && ((ManasSkillInstance)optional.get()).isTemporarySkill()) {
                  storage.forgetSkill(oldSkill);
               }
            }

            ManasSkill manasSkill = (ManasSkill)SkillAPI.getSkillRegistry().getValue(this.skill);
            if (manasSkill != null) {
               tag.m_128359_("created_skill", String.valueOf(manasSkill.getRegistryName()));
               TensuraSkillInstance skillInstance = new TensuraSkillInstance(manasSkill);
               if (creator.isMastered(player) && manasSkill.equals(UniqueSkills.ANTI_SKILL.get())) {
                  ((AntiSkill)UniqueSkills.ANTI_SKILL.get()).addMasteryPoint(skillInstance, player, manasSkill.getMaxMastery());
               }

               skillInstance.setRemoveTime(1200);
               skillInstance.getOrCreateTag().m_128379_("CreatorSkill", true);
               if (storage.learnSkill(skillInstance)) {
                  player.m_5661_(Component.m_237110_("tensura.skill.temporary.success_drain", new Object[]{manasSkill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), false);
                  player.m_9236_().m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
                     if (cap.getSkillInSlot(0) == null) {
                        cap.setInstanceInSlot(skillInstance, 0);
                     } else if (cap.getSkillInSlot(1) == null) {
                        cap.setInstanceInSlot(skillInstance, 1);
                     } else if (cap.getSkillInSlot(2) == null) {
                        cap.setInstanceInSlot(skillInstance, 2);
                     }

                     TensuraSkillCapability.sync(player);
                  });
               }

               creator.markDirty();
               storage.syncChanges();
               player.m_6915_();
            }
         }
      }
   }
}
