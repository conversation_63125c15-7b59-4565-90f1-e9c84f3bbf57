package com.github.manasmods.tensura.network.play2server;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.Magic;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.menu.BattlewillSelectionMenu;
import com.github.manasmods.tensura.menu.EvolutionMenu;
import com.github.manasmods.tensura.menu.MagicMenu;
import com.github.manasmods.tensura.menu.MagicSelectionMenu;
import com.github.manasmods.tensura.menu.MainMenu;
import com.github.manasmods.tensura.menu.SkillMenu;
import com.github.manasmods.tensura.menu.SkillSelectionMenu;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.ClientboundMainScreenOpenPacket;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;
import javax.annotation.Nullable;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraftforge.network.NetworkHooks;
import net.minecraftforge.network.PacketDistributor;
import net.minecraftforge.network.NetworkEvent.Context;

public class GUISwitchPacket {
   private final List<Class<? extends AbstractContainerMenu>> allowedMenus = new ArrayList(List.of(MainMenu.class, SkillMenu.class, SkillSelectionMenu.class, MagicMenu.class, MagicSelectionMenu.class, BattlewillSelectionMenu.class, EvolutionMenu.class));
   private final int switchID;
   private final Skill.SkillType skillType;
   private final Magic.MagicType magicType;
   private final boolean ignoreMenu;
   public static final int MAIN = 0;
   public static final int SKILL = 4;
   public static final int MAGIC = 5;
   public static final int ART = 6;
   public static final int EVOLUTIONS = 7;
   public static final int SETTINGS = 8;
   public static final int FALSIFIER = 9;

   public GUISwitchPacket(FriendlyByteBuf buf) {
      this.switchID = buf.readInt();
      this.ignoreMenu = buf.readBoolean();
      this.skillType = (Skill.SkillType)buf.m_130066_(Skill.SkillType.class);
      this.magicType = (Magic.MagicType)buf.m_130066_(Magic.MagicType.class);
   }

   public GUISwitchPacket(int switchID) {
      this.switchID = switchID;
      this.skillType = Skill.SkillType.UNIQUE;
      this.magicType = Magic.MagicType.SPIRITUAL;
      this.ignoreMenu = false;
   }

   public GUISwitchPacket(Skill.SkillType type) {
      this.skillType = type;
      this.magicType = Magic.MagicType.SPIRITUAL;
      this.switchID = -1;
      this.ignoreMenu = false;
   }

   public GUISwitchPacket(Magic.MagicType type) {
      this.skillType = Skill.SkillType.UNIQUE;
      this.magicType = type;
      this.switchID = -1;
      this.ignoreMenu = false;
   }

   public GUISwitchPacket(int switchID, boolean ignoreMenu) {
      this.switchID = switchID;
      this.skillType = Skill.SkillType.UNIQUE;
      this.magicType = Magic.MagicType.SPIRITUAL;
      this.ignoreMenu = ignoreMenu;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeInt(this.switchID);
      buf.writeBoolean(this.ignoreMenu);
      buf.m_130068_(this.skillType);
      buf.m_130068_(this.magicType);
   }

   public void handle(Supplier<Context> context) {
      ((Context)context.get()).enqueueWork(() -> {
         ServerPlayer sender = ((Context)context.get()).getSender();
         if (sender != null) {
            if (this.switchID == -1) {
               if (sender.f_36096_ instanceof SkillMenu) {
                  NetworkHooks.openScreen(sender, new SimpleMenuProvider(SkillSelectionMenu::new, this.skillType.getName()), (buf) -> {
                     buf.m_130068_(this.skillType);
                  });
               }

               if (sender.f_36096_ instanceof MagicMenu) {
                  NetworkHooks.openScreen(sender, new SimpleMenuProvider(MagicSelectionMenu::new, this.magicType.getName()), (buf) -> {
                     buf.m_130068_(this.magicType);
                  });
               }
            } else if (this.matchesAny(sender.f_36096_)) {
               this.switchTabs(sender);
            }

         }
      });
      ((Context)context.get()).setPacketHandled(true);
   }

   private void switchTabs(ServerPlayer player) {
      switch(this.switchID) {
      case 0:
         NetworkHooks.openScreen(player, new SimpleMenuProvider(MainMenu::new, Component.m_237119_()), (buf) -> {
            buf.writeInt(player.m_9236_().m_46469_().m_46215_(TensuraGameRules.DEMON_LORD_AWAKEN));
         });
         break;
      case 1:
      case 2:
      case 3:
      default:
         SkillHelper.comingSoonMessage(player);
         break;
      case 4:
         NetworkHooks.openScreen(player, new SimpleMenuProvider(SkillMenu::new, Component.m_237119_()));
         break;
      case 5:
         NetworkHooks.openScreen(player, new SimpleMenuProvider(MagicMenu::new, Component.m_237119_()));
         break;
      case 6:
         NetworkHooks.openScreen(player, new SimpleMenuProvider(BattlewillSelectionMenu::new, Component.m_237119_()));
         break;
      case 7:
         NetworkHooks.openScreen(player, new SimpleMenuProvider(EvolutionMenu::new, Component.m_237119_()));
         break;
      case 8:
      case 9:
         TensuraNetwork.INSTANCE.send(PacketDistributor.PLAYER.with(() -> {
            return player;
         }), new ClientboundMainScreenOpenPacket(this.switchID, player.m_19879_()));
      }

   }

   private boolean matchesAny(@Nullable AbstractContainerMenu playerMenu) {
      if (this.ignoreMenu) {
         return true;
      } else {
         return playerMenu == null ? false : this.allowedMenus.contains(playerMenu.getClass());
      }
   }
}
