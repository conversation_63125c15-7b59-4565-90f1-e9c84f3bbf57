package com.github.manasmods.tensura.network.play2server;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.NetworkEvent.Context;
import net.minecraftforge.registries.IForgeRegistry;

public class SetTrackedEvolutionPacket {
   private final ResourceLocation race;

   public SetTrackedEvolutionPacket(FriendlyByteBuf buf) {
      this.race = buf.m_130281_();
   }

   public SetTrackedEvolutionPacket(Race race) {
      this.race = race.getRegistryName();
   }

   public SetTrackedEvolutionPacket() {
      this.race = new ResourceLocation("tensura:none");
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.m_130085_(this.race);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer serverPlayer = ((Context)ctx.get()).getSender();
         if (serverPlayer != null) {
            Race pRace = this.race == null ? null : (Race)((IForgeRegistry)TensuraRaces.RACE_REGISTRY.get()).getValue(this.race);
            TensuraPlayerCapability.setTrackedRace(serverPlayer, pRace);
            TensuraPlayerCapability.sync(serverPlayer);
         }

      });
      ((Context)ctx.get()).setPacketHandled(true);
   }
}
