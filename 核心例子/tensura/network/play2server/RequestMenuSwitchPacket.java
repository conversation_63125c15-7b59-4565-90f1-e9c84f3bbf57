package com.github.manasmods.tensura.network.play2server;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.unique.GreatSageSkill;
import com.github.manasmods.tensura.block.SmithingBenchBlock;
import com.github.manasmods.tensura.core.AccessorItemCombinerMenu;
import com.github.manasmods.tensura.menu.DegenerateCraftingMenu;
import com.github.manasmods.tensura.menu.DegenerateEnchantmentMenu;
import com.github.manasmods.tensura.menu.ResearcherEnchantmentMenu;
import com.github.manasmods.tensura.menu.ResearcherSpatialStorageMenu;
import com.github.manasmods.tensura.menu.SmithingBenchMenu;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.AnvilMenu;
import net.minecraft.world.inventory.ContainerLevelAccess;
import net.minecraft.world.inventory.SmithingMenu;
import net.minecraftforge.network.NetworkHooks;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestMenuSwitchPacket {
   private final RequestMenuSwitchPacket.SwitchType switchType;

   public RequestMenuSwitchPacket(FriendlyByteBuf buf) {
      this.switchType = (RequestMenuSwitchPacket.SwitchType)buf.m_130066_(RequestMenuSwitchPacket.SwitchType.class);
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.m_130068_(this.switchType);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         this.switchType.handler.accept((Context)ctx.get());
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   public RequestMenuSwitchPacket(RequestMenuSwitchPacket.SwitchType switchType) {
      this.switchType = switchType;
   }

   public static enum SwitchType {
      SMITHING_TO_VANILLA_SMITHING((context) -> {
         ServerPlayer sender = context.getSender();
         if (sender != null) {
            AbstractContainerMenu patt1936$temp = sender.f_36096_;
            if (patt1936$temp instanceof SmithingBenchMenu) {
               SmithingBenchMenu smithingBenchMenu = (SmithingBenchMenu)patt1936$temp;
               NetworkHooks.openScreen(sender, new SimpleMenuProvider((windowId, inventory, player) -> {
                  return new SmithingMenu(windowId, inventory, smithingBenchMenu.getAccess());
               }, SmithingBenchBlock.TITLE));
            }
         }
      }),
      SMITHING_TO_ANVIL((context) -> {
         ServerPlayer sender = context.getSender();
         if (sender != null) {
            AbstractContainerMenu patt2403$temp = sender.f_36096_;
            if (patt2403$temp instanceof SmithingBenchMenu) {
               SmithingBenchMenu smithingBenchMenu = (SmithingBenchMenu)patt2403$temp;
               NetworkHooks.openScreen(sender, new SimpleMenuProvider((windowId, inventory, player) -> {
                  return new AnvilMenu(windowId, inventory, smithingBenchMenu.getAccess());
               }, SmithingBenchBlock.TITLE));
            }
         }
      }),
      VANILLA_SMITHING_TO_SMITHING((context) -> {
         ServerPlayer sender = context.getSender();
         if (sender != null) {
            AbstractContainerMenu patt2878$temp = sender.f_36096_;
            if (patt2878$temp instanceof SmithingMenu) {
               SmithingMenu smithingMenu = (SmithingMenu)patt2878$temp;
               NetworkHooks.openScreen(sender, new SimpleMenuProvider((pContainerId, pPlayerInventory, pPlayer) -> {
                  return new SmithingBenchMenu(pContainerId, pPlayerInventory, ((AccessorItemCombinerMenu)smithingMenu).getAccess());
               }, SmithingBenchBlock.TITLE));
            }
         }
      }),
      VANILLA_SMITHING_TO_ANVIL((context) -> {
         ServerPlayer sender = context.getSender();
         if (sender != null) {
            AbstractContainerMenu patt3395$temp = sender.f_36096_;
            if (patt3395$temp instanceof SmithingMenu) {
               SmithingMenu smithingMenu = (SmithingMenu)patt3395$temp;
               NetworkHooks.openScreen(sender, new SimpleMenuProvider((pContainerId, pPlayerInventory, pPlayer) -> {
                  return new AnvilMenu(pContainerId, pPlayerInventory, ((AccessorItemCombinerMenu)smithingMenu).getAccess());
               }, SmithingBenchBlock.TITLE));
            }
         }
      }),
      ANVIL_TO_SMITHING((context) -> {
         ServerPlayer sender = context.getSender();
         if (sender != null) {
            AbstractContainerMenu patt3896$temp = sender.f_36096_;
            if (patt3896$temp instanceof AnvilMenu) {
               AnvilMenu anvilMenu = (AnvilMenu)patt3896$temp;
               NetworkHooks.openScreen(sender, new SimpleMenuProvider((pContainerId, pPlayerInventory, pPlayer) -> {
                  return new SmithingBenchMenu(pContainerId, pPlayerInventory, ((AccessorItemCombinerMenu)anvilMenu).getAccess());
               }, SmithingBenchBlock.TITLE));
            }
         }
      }),
      ANVIL_TO_VANILLA_SMITHING((context) -> {
         ServerPlayer sender = context.getSender();
         if (sender != null) {
            AbstractContainerMenu patt4404$temp = sender.f_36096_;
            if (patt4404$temp instanceof AnvilMenu) {
               AnvilMenu anvilMenu = (AnvilMenu)patt4404$temp;
               NetworkHooks.openScreen(sender, new SimpleMenuProvider((pContainerId, pPlayerInventory, pPlayer) -> {
                  return new SmithingMenu(pContainerId, pPlayerInventory, ((AccessorItemCombinerMenu)anvilMenu).getAccess());
               }, SmithingBenchBlock.TITLE));
            }
         }
      }),
      DEGENERATE_TAB_1_TO_TAB_2((context) -> {
         ServerPlayer sender = context.getSender();
         if (sender != null) {
            AbstractContainerMenu patt4907$temp = sender.f_36096_;
            if (patt4907$temp instanceof DegenerateCraftingMenu) {
               DegenerateCraftingMenu degenerateMenuCrafting = (DegenerateCraftingMenu)patt4907$temp;
               NetworkHooks.openScreen(sender, new SimpleMenuProvider((pContainerId, pPlayerInventory, pPlayer) -> {
                  return new DegenerateEnchantmentMenu(pContainerId, pPlayerInventory, degenerateMenuCrafting.getAccess());
               }, Component.m_237119_()));
            }
         }
      }),
      DEGENERATE_TAB_2_TO_TAB_1((context) -> {
         ServerPlayer sender = context.getSender();
         if (sender != null) {
            AbstractContainerMenu patt5426$temp = sender.f_36096_;
            if (patt5426$temp instanceof DegenerateEnchantmentMenu) {
               DegenerateEnchantmentMenu degenerateMenuAnvil = (DegenerateEnchantmentMenu)patt5426$temp;
               NetworkHooks.openScreen(sender, new SimpleMenuProvider((pContainerId, pPlayerInventory, pPlayer) -> {
                  return new DegenerateCraftingMenu(pContainerId, pPlayerInventory, degenerateMenuAnvil.getAccess());
               }, Component.m_237119_()));
            }
         }
      }),
      GREAT_SAGE_TAB_1_TO_TAB_2((context) -> {
         ServerPlayer sender = context.getSender();
         if (sender != null) {
            Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(sender).getSkill((ManasSkill)UniqueSkills.GREAT_SAGE.get());
            if (!optional.isEmpty()) {
               GreatSageSkill.openGreatSageGUI(sender, (ManasSkillInstance)optional.get(), 2);
            }
         }
      }),
      GREAT_SAGE_TAB_2_TO_TAB_1((context) -> {
         ServerPlayer sender = context.getSender();
         if (sender != null) {
            Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(sender).getSkill((ManasSkill)UniqueSkills.GREAT_SAGE.get());
            if (!optional.isEmpty()) {
               GreatSageSkill.openGreatSageGUI(sender, (ManasSkillInstance)optional.get(), 1);
            }
         }
      }),
      RESEARCHER_TAB_1_TO_TAB_2((context) -> {
         ServerPlayer sender = context.getSender();
         if (sender != null) {
            AbstractContainerMenu patt6741$temp = sender.f_36096_;
            if (patt6741$temp instanceof ResearcherSpatialStorageMenu) {
               ResearcherSpatialStorageMenu menu = (ResearcherSpatialStorageMenu)patt6741$temp;
               sender.f_36096_.m_6877_(sender);
               NetworkHooks.openScreen(sender, new SimpleMenuProvider((pContainerId, pPlayerInventory, pPlayer) -> {
                  return new ResearcherEnchantmentMenu(pContainerId, pPlayerInventory, ContainerLevelAccess.m_39289_(sender.f_19853_, sender.m_20183_()), menu.getSkill());
               }, Component.m_237119_()), (buf) -> {
                  buf.m_130085_(SkillUtils.getSkillId(menu.getSkill()));
               });
            }
         }
      }),
      RESEARCHER_TAB_2_TO_TAB_1((context) -> {
         ServerPlayer sender = context.getSender();
         if (sender != null) {
            AbstractContainerMenu patt7471$temp = sender.f_36096_;
            if (patt7471$temp instanceof ResearcherEnchantmentMenu) {
               ResearcherEnchantmentMenu menu = (ResearcherEnchantmentMenu)patt7471$temp;
               Optional skillInstance = SkillAPI.getSkillsFrom(sender).getSkill(menu.getSkill());
               if (!skillInstance.isEmpty()) {
                  ((ManasSkillInstance)skillInstance.get()).onPressed(sender);
               }
            }
         }
      });

      private final Consumer<Context> handler;

      private SwitchType(Consumer<Context> handler) {
         this.handler = handler;
      }

      // $FF: synthetic method
      private static RequestMenuSwitchPacket.SwitchType[] $values() {
         return new RequestMenuSwitchPacket.SwitchType[]{SMITHING_TO_VANILLA_SMITHING, SMITHING_TO_ANVIL, VANILLA_SMITHING_TO_SMITHING, VANILLA_SMITHING_TO_ANVIL, ANVIL_TO_SMITHING, ANVIL_TO_VANILLA_SMITHING, DEGENERATE_TAB_1_TO_TAB_2, DEGENERATE_TAB_2_TO_TAB_1, GREAT_SAGE_TAB_1_TO_TAB_2, GREAT_SAGE_TAB_2_TO_TAB_1, RESEARCHER_TAB_1_TO_TAB_2, RESEARCHER_TAB_2_TO_TAB_1};
      }
   }
}
