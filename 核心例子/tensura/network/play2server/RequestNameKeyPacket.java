package com.github.manasmods.tensura.network.play2server;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.subclass.IRanking;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.menu.NamingMenu;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.UUID;
import java.util.function.Supplier;
import net.minecraft.ChatFormatting;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.network.NetworkHooks;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestNameKeyPacket {
   public RequestNameKeyPacket(FriendlyByteBuf buf) {
   }

   public RequestNameKeyPacket() {
   }

   public void toBytes(FriendlyByteBuf buf) {
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer player = ((Context)ctx.get()).getSender();
         if (player != null) {
            if (player.m_36341_()) {
               TensuraEPCapability.getFrom(player).ifPresent((cap) -> {
                  boolean isNameable = cap.isNameable();
                  cap.setNameable(!isNameable);
                  player.m_5661_(Component.m_237110_("tensura.naming.nameable_status", new Object[]{!isNameable}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  TensuraEPCapability.sync(player);
               });
            } else {
               LivingEntity sub = SkillHelper.getTargetingEntity(player, 6.0D, false);
               if (sub != null && canName(player, sub)) {
                  NetworkHooks.openScreen(player, new SimpleMenuProvider(NamingMenu::new, Component.m_237119_()), (buf) -> {
                     buf.writeInt(sub.m_19879_());
                  });
               }
            }
         }

      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   public static boolean canName(Player player, LivingEntity sub) {
      if (cannotBeNamed(sub)) {
         player.m_5661_(Component.m_237115_("tensura.naming.cannot_name").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
         return false;
      } else if (SkillHelper.isSubordinate(sub, player)) {
         player.m_5661_(Component.m_237115_("tensura.naming.name_owner").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
         return false;
      } else if (TensuraEPCapability.getName(sub) != null) {
         player.m_5661_(Component.m_237115_("tensura.naming.already_named").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
         return false;
      } else {
         UUID owner = TensuraEPCapability.getPermanentOwner(sub);
         if (owner != null && !owner.equals(player.m_20148_())) {
            player.m_5661_(Component.m_237115_("tensura.naming.had_owner").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
            return false;
         } else if (noSubmit(sub, player)) {
            player.m_5661_(Component.m_237115_("tensura.naming.not_submit").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
            return false;
         } else if (!player.m_7500_() && (sub.m_21023_((MobEffect)TensuraMobEffects.INSANITY.get()) || sub.m_21023_((MobEffect)TensuraMobEffects.RAMPAGE.get()))) {
            player.m_5661_(Component.m_237115_("tensura.naming.insane").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
            return false;
         } else if (!player.m_7500_() && TensuraEPCapability.getEP(sub) >= TensuraPlayerCapability.getBaseEP(player)) {
            player.m_5661_(Component.m_237115_("tensura.naming.lack_EP").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
            return false;
         } else {
            return true;
         }
      }
   }

   private static boolean cannotBeNamed(LivingEntity sub) {
      if (!sub.m_6084_()) {
         return true;
      } else if (sub instanceof Player) {
         if (!TensuraEPCapability.isNameable(sub)) {
            return true;
         } else {
            return !sub.f_19853_.m_46469_().m_46207_(TensuraGameRules.PLAYER_NAME);
         }
      } else if (!sub.m_6095_().m_204039_(TensuraTags.EntityTypes.NAMEABLE)) {
         return true;
      } else {
         boolean var10000;
         if (sub instanceof IRanking) {
            IRanking ranking = (IRanking)sub;
            if (!ranking.canBeNamed()) {
               var10000 = true;
               return var10000;
            }
         }

         var10000 = false;
         return var10000;
      }
   }

   private static boolean noSubmit(LivingEntity sub, Player player) {
      if (player.m_7500_()) {
         return false;
      } else if (SkillHelper.isSubordinate(player, sub)) {
         return false;
      } else if (TensuraEffectsCapability.getEffectSource(sub, (MobEffect)TensuraMobEffects.FEAR.get()) == player) {
         return false;
      } else if ((double)sub.m_21223_() <= (double)sub.m_21233_() * (Double)TensuraConfig.INSTANCE.namingConfig.lowHPToName.get() / 100.0D) {
         return false;
      } else {
         return TensuraEPCapability.getEP(sub) > TensuraEPCapability.getEP(player) * (Double)TensuraConfig.INSTANCE.namingConfig.maximumEPToName.get() / 100.0D;
      }
   }
}
