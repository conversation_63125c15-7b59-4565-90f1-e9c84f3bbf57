package com.github.manasmods.tensura.network;

import com.github.manasmods.tensura.network.play2client.ClientboundMainScreenOpenPacket;
import com.github.manasmods.tensura.network.play2client.ClientboundNPCScreenOpenPacket;
import com.github.manasmods.tensura.network.play2client.ClientboundSpatialStorageOpenPacket;
import com.github.manasmods.tensura.network.play2client.RequestClientSkillRemovePacket;
import com.github.manasmods.tensura.network.play2client.RequestDimensionUpdatePacket;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.network.play2client.RequestPartHurtAnimation;
import com.github.manasmods.tensura.network.play2client.RequestTotemDisplayPacket;
import com.github.manasmods.tensura.network.play2client.SyncEPCapabilityPacket;
import com.github.manasmods.tensura.network.play2client.SyncEffectsCapabilityPacket;
import com.github.manasmods.tensura.network.play2client.SyncElementCombinationPacket;
import com.github.manasmods.tensura.network.play2client.SyncEntityEPPacket;
import com.github.manasmods.tensura.network.play2client.SyncGearEPPacket;
import com.github.manasmods.tensura.network.play2client.SyncKilnMoltenColoringPacket;
import com.github.manasmods.tensura.network.play2client.SyncMagiculePacket;
import com.github.manasmods.tensura.network.play2client.SyncOtherworlderSpawningPacket;
import com.github.manasmods.tensura.network.play2client.SyncPlayerCapabilityPacket;
import com.github.manasmods.tensura.network.play2client.SyncSkillCapabilityPacket;
import com.github.manasmods.tensura.network.play2client.SyncSmithingCapabilityPacket;
import com.github.manasmods.tensura.network.play2server.GUISwitchPacket;
import com.github.manasmods.tensura.network.play2server.InsanityActionPacket;
import com.github.manasmods.tensura.network.play2server.RequestAwakeningPacket;
import com.github.manasmods.tensura.network.play2server.RequestContainerButtonClickPacket;
import com.github.manasmods.tensura.network.play2server.RequestDisenchantPacket;
import com.github.manasmods.tensura.network.play2server.RequestKilnActionPacket;
import com.github.manasmods.tensura.network.play2server.RequestMenuSwitchPacket;
import com.github.manasmods.tensura.network.play2server.RequestNameKeyPacket;
import com.github.manasmods.tensura.network.play2server.RequestNamingGUIPacket;
import com.github.manasmods.tensura.network.play2server.RequestRaceAndMountPacket;
import com.github.manasmods.tensura.network.play2server.SetSprintSpeedPacket;
import com.github.manasmods.tensura.network.play2server.SetTrackedEvolutionPacket;
import com.github.manasmods.tensura.network.play2server.SlimeJumpChargePacket;
import com.github.manasmods.tensura.network.play2server.SlimeJumpReleasedPacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestAbilityPresetPacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestChangeModeButtonPacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestFalsifierItemPacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestPresetSlotChangePacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestResearcherEnchantmentPacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestSkillCreationPacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestSkillNumberKeyPressPacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestSpatialActionPacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestWarpSavePacket;
import java.util.Iterator;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.function.Supplier;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.fml.ModList;
import net.minecraftforge.network.NetworkDirection;
import net.minecraftforge.network.NetworkRegistry;
import net.minecraftforge.network.simple.SimpleChannel;
import net.minecraftforge.server.ServerLifecycleHooks;

public class TensuraNetwork {
   private static final String PROTOCOL_VERSION = ModList.get().getModFileById("tensura").versionString().replaceAll("\\.", "");
   public static final SimpleChannel INSTANCE;

   public static void register() {
      int i = 0;
      int i = i + 1;
      INSTANCE.registerMessage(i, SyncGearEPPacket.class, SyncGearEPPacket::toBytes, SyncGearEPPacket::new, SyncGearEPPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, SyncEntityEPPacket.class, SyncEntityEPPacket::toBytes, SyncEntityEPPacket::new, SyncEntityEPPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, SyncElementCombinationPacket.class, SyncElementCombinationPacket::toBytes, SyncElementCombinationPacket::new, SyncElementCombinationPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, SyncKilnMoltenColoringPacket.class, SyncKilnMoltenColoringPacket::toBytes, SyncKilnMoltenColoringPacket::new, SyncKilnMoltenColoringPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, SyncPlayerCapabilityPacket.class, SyncPlayerCapabilityPacket::toBytes, SyncPlayerCapabilityPacket::new, SyncPlayerCapabilityPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, SyncSmithingCapabilityPacket.class, SyncSmithingCapabilityPacket::toBytes, SyncSmithingCapabilityPacket::new, SyncSmithingCapabilityPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, SyncEffectsCapabilityPacket.class, SyncEffectsCapabilityPacket::toBytes, SyncEffectsCapabilityPacket::new, SyncEffectsCapabilityPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, SyncEPCapabilityPacket.class, SyncEPCapabilityPacket::toBytes, SyncEPCapabilityPacket::new, SyncEPCapabilityPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, SyncSkillCapabilityPacket.class, SyncSkillCapabilityPacket::toBytes, SyncSkillCapabilityPacket::new, SyncSkillCapabilityPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, SyncMagiculePacket.class, SyncMagiculePacket::toBytes, SyncMagiculePacket::new, SyncMagiculePacket::handle);
      ++i;
      INSTANCE.registerMessage(i, SyncOtherworlderSpawningPacket.class, SyncOtherworlderSpawningPacket::toBytes, SyncOtherworlderSpawningPacket::new, SyncOtherworlderSpawningPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestClientSkillRemovePacket.class, RequestClientSkillRemovePacket::toBytes, RequestClientSkillRemovePacket::new, RequestClientSkillRemovePacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestDimensionUpdatePacket.class, RequestDimensionUpdatePacket::toBytes, RequestDimensionUpdatePacket::new, RequestDimensionUpdatePacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestTotemDisplayPacket.class, RequestTotemDisplayPacket::toBytes, RequestTotemDisplayPacket::new, RequestTotemDisplayPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestFxSpawningPacket.class, RequestFxSpawningPacket::toBytes, RequestFxSpawningPacket::new, RequestFxSpawningPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestPartHurtAnimation.class, RequestPartHurtAnimation::toBytes, RequestPartHurtAnimation::new, RequestPartHurtAnimation::handle);
      ++i;
      INSTANCE.registerMessage(i, ClientboundNPCScreenOpenPacket.class, ClientboundNPCScreenOpenPacket::toBytes, ClientboundNPCScreenOpenPacket::new, ClientboundNPCScreenOpenPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, ClientboundMainScreenOpenPacket.class, ClientboundMainScreenOpenPacket::toBytes, ClientboundMainScreenOpenPacket::new, ClientboundMainScreenOpenPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, ClientboundSpatialStorageOpenPacket.class, ClientboundSpatialStorageOpenPacket::toBytes, ClientboundSpatialStorageOpenPacket::new, ClientboundSpatialStorageOpenPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestKilnActionPacket.class, RequestKilnActionPacket::toBytes, RequestKilnActionPacket::new, RequestKilnActionPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestMenuSwitchPacket.class, RequestMenuSwitchPacket::toBytes, RequestMenuSwitchPacket::new, RequestMenuSwitchPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, GUISwitchPacket.class, GUISwitchPacket::toBytes, GUISwitchPacket::new, GUISwitchPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, SlimeJumpChargePacket.class, SlimeJumpChargePacket::toBytes, SlimeJumpChargePacket::new, SlimeJumpChargePacket::handle);
      ++i;
      INSTANCE.registerMessage(i, SlimeJumpReleasedPacket.class, SlimeJumpReleasedPacket::toBytes, SlimeJumpReleasedPacket::new, SlimeJumpReleasedPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestChangeModeButtonPacket.class, RequestChangeModeButtonPacket::toBytes, RequestChangeModeButtonPacket::new, RequestChangeModeButtonPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestSkillNumberKeyPressPacket.class, RequestSkillNumberKeyPressPacket::toBytes, RequestSkillNumberKeyPressPacket::new, RequestSkillNumberKeyPressPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestAbilityPresetPacket.class, RequestAbilityPresetPacket::toBytes, RequestAbilityPresetPacket::new, RequestAbilityPresetPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestPresetSlotChangePacket.class, RequestPresetSlotChangePacket::toBytes, RequestPresetSlotChangePacket::new, RequestPresetSlotChangePacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestRaceAndMountPacket.class, RequestRaceAndMountPacket::toBytes, RequestRaceAndMountPacket::new, RequestRaceAndMountPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestNameKeyPacket.class, RequestNameKeyPacket::toBytes, RequestNameKeyPacket::new, RequestNameKeyPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestNamingGUIPacket.class, RequestNamingGUIPacket::toBytes, RequestNamingGUIPacket::new, RequestNamingGUIPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestContainerButtonClickPacket.class, RequestContainerButtonClickPacket::write, RequestContainerButtonClickPacket::new, RequestContainerButtonClickPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestSpatialActionPacket.class, RequestSpatialActionPacket::toBytes, RequestSpatialActionPacket::new, RequestSpatialActionPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestWarpSavePacket.class, RequestWarpSavePacket::toBytes, RequestWarpSavePacket::new, RequestWarpSavePacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestSkillCreationPacket.class, RequestSkillCreationPacket::toBytes, RequestSkillCreationPacket::new, RequestSkillCreationPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestFalsifierItemPacket.class, RequestFalsifierItemPacket::toBytes, RequestFalsifierItemPacket::new, RequestFalsifierItemPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestAwakeningPacket.class, RequestAwakeningPacket::toBytes, RequestAwakeningPacket::new, RequestAwakeningPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestResearcherEnchantmentPacket.class, RequestResearcherEnchantmentPacket::toBytes, RequestResearcherEnchantmentPacket::new, RequestResearcherEnchantmentPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, RequestDisenchantPacket.class, RequestDisenchantPacket::toBytes, RequestDisenchantPacket::new, RequestDisenchantPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, SetTrackedEvolutionPacket.class, SetTrackedEvolutionPacket::toBytes, SetTrackedEvolutionPacket::new, SetTrackedEvolutionPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, InsanityActionPacket.class, InsanityActionPacket::toBytes, InsanityActionPacket::new, InsanityActionPacket::handle);
      ++i;
      INSTANCE.registerMessage(i, SetSprintSpeedPacket.class, SetSprintSpeedPacket::toBytes, SetSprintSpeedPacket::new, SetSprintSpeedPacket::handle);
   }

   public static <T> void toServer(T message) {
      INSTANCE.sendToServer(message);
   }

   public static <T> void toAll(T message) {
      Iterator var1 = ServerLifecycleHooks.getCurrentServer().m_6846_().m_11314_().iterator();

      while(var1.hasNext()) {
         ServerPlayer player = (ServerPlayer)var1.next();
         INSTANCE.sendTo(message, player.f_8906_.f_9742_, NetworkDirection.PLAY_TO_CLIENT);
      }

   }

   static {
      ResourceLocation var10000 = new ResourceLocation("tensura", "main");
      Supplier var10001 = () -> {
         return PROTOCOL_VERSION;
      };
      String var10002 = PROTOCOL_VERSION;
      Objects.requireNonNull(var10002);
      Predicate var0 = var10002::equals;
      String var10003 = PROTOCOL_VERSION;
      Objects.requireNonNull(var10003);
      INSTANCE = NetworkRegistry.newSimpleChannel(var10000, var10001, var0, var10003::equals);
   }
}
