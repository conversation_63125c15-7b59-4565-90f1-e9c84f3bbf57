package com.github.manasmods.tensura.menu;

import com.github.manasmods.tensura.block.entity.KilnBlockEntity;
import com.github.manasmods.tensura.menu.slot.TensuraFuelSlot;
import com.github.manasmods.tensura.menu.slot.TensuraMixingSlot;
import com.github.manasmods.tensura.registry.blocks.TensuraBlockEntities;
import com.github.manasmods.tensura.registry.menu.TensuraMenuTypes;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ContainerLevelAccess;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraftforge.common.capabilities.ForgeCapabilities;
import net.minecraftforge.items.SlotItemHandler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class KilnMenu extends AbstractContainerMenu {
   private static final Logger log = LogManager.getLogger(KilnMenu.class);
   private static final int HOTBAR_SLOT_COUNT = 9;
   private static final int PLAYER_INVENTORY_ROW_COUNT = 3;
   private static final int PLAYER_INVENTORY_COLUMN_COUNT = 9;
   private static final int PLAYER_INVENTORY_SLOT_COUNT = 27;
   private static final int VANILLA_SLOT_COUNT = 36;
   private static final int VANILLA_FIRST_SLOT_INDEX = 0;
   private static final int TE_INVENTORY_FIRST_SLOT_INDEX = 36;
   private static final int TE_INVENTORY_SLOT_COUNT = 3;
   public final KilnBlockEntity blockEntity;
   private final Level level;
   private int fuelSlotIndex;
   private int meltingSlotIndex;
   private int mixingSlotIndex;

   public KilnMenu(int id, Inventory inv, FriendlyByteBuf extraData) {
      this(id, inv, (KilnBlockEntity)inv.f_35978_.f_19853_.m_7702_(extraData.m_130135_()));
   }

   public KilnMenu(int id, Inventory inv, KilnBlockEntity entity) {
      super((MenuType)TensuraMenuTypes.KILN.get(), id);
      m_38869_(inv, 3);
      this.blockEntity = entity;
      this.level = inv.f_35978_.f_19853_;
      this.addPlayerInventory(inv);
      this.addPlayerHotbar(inv);
      this.blockEntity.getCapability(ForgeCapabilities.ITEM_HANDLER).ifPresent((handler) -> {
         this.fuelSlotIndex = this.m_38897_(new TensuraFuelSlot(handler, 0, 203, 98)).f_40219_;
         this.meltingSlotIndex = this.m_38897_(new SlotItemHandler(handler, 1, 203, 52)).f_40219_;
         this.mixingSlotIndex = this.m_38897_(new TensuraMixingSlot(handler, 2, 80, 36, this)).f_40219_;
      });
   }

   public boolean isSmelting() {
      return this.blockEntity.getMeltingProgress() > 0;
   }

   public boolean hasFuel() {
      return this.blockEntity.getFuelTime() > 0;
   }

   public int getMoltenProgress() {
      int progress = this.blockEntity.getMoltenAmount();
      int progressArrowSize = 74;
      return progress != 0 ? progress * progressArrowSize / 144 : 0;
   }

   public int getMagisteelProgress() {
      int progress = this.blockEntity.getMagicMaterialAmount();
      int progressArrowSize = 74;
      return progress != 0 ? progress * progressArrowSize / 144 : 0;
   }

   public int getScaledProgress() {
      int progress = this.blockEntity.getMeltingProgress();
      int maxProgress = 100;
      int progressArrowSize = 25;
      return progress != 0 ? progress * progressArrowSize / maxProgress : 0;
   }

   public int getScaledFuelProgress() {
      int fuelProgress = this.blockEntity.getFuelTime();
      int maxFuelProgress = this.blockEntity.getMaxFuelTime();
      int fuelProgressSize = 13;
      return maxFuelProgress != 0 ? (int)((float)fuelProgress / (float)maxFuelProgress * (float)fuelProgressSize) : 0;
   }

   public ItemStack m_7648_(Player playerIn, int index) {
      Slot sourceSlot = (Slot)this.f_38839_.get(index);
      if (sourceSlot == null) {
         return ItemStack.f_41583_;
      } else if (!sourceSlot.m_6657_()) {
         return ItemStack.f_41583_;
      } else {
         ItemStack sourceStack = sourceSlot.m_7993_();
         ItemStack copyOfSourceStack = sourceStack.m_41777_();
         if (index < 36) {
            Slot fuelSlot = (Slot)this.f_38839_.get(this.fuelSlotIndex);
            if (fuelSlot.m_5857_(sourceStack) && !this.m_38903_(sourceStack, this.fuelSlotIndex, this.fuelSlotIndex + 1, false)) {
               return ItemStack.f_41583_;
            } else {
               Slot meltingSlot = (Slot)this.f_38839_.get(this.meltingSlotIndex);
               return meltingSlot.m_5857_(sourceStack) && !this.m_38903_(sourceStack, this.meltingSlotIndex, this.meltingSlotIndex + 1, false) ? ItemStack.f_41583_ : TensuraMenuHelper.quickMoveStack(playerIn, sourceStack, sourceSlot, copyOfSourceStack);
            }
         } else if (index < 39) {
            return !this.m_38903_(sourceStack, 0, 36, false) ? ItemStack.f_41583_ : TensuraMenuHelper.quickMoveStack(playerIn, sourceStack, sourceSlot, copyOfSourceStack);
         } else {
            log.error("Invalid slotIndex {} for QuickCraft in BlockEntity at {}", index, this.blockEntity.m_58899_());
            return ItemStack.f_41583_;
         }
      }
   }

   public boolean m_6875_(Player player) {
      return m_38889_(ContainerLevelAccess.m_39289_(this.level, this.blockEntity.m_58899_()), player, (Block)TensuraBlockEntities.Blocks.KILN.get());
   }

   private void addPlayerInventory(Inventory playerInventory) {
      for(int i = 0; i < 3; ++i) {
         for(int l = 0; l < 9; ++l) {
            this.m_38897_(new Slot(playerInventory, l + i * 9 + 9, 8 + l * 18, 86 + i * 18));
         }
      }

   }

   private void addPlayerHotbar(Inventory playerInventory) {
      for(int i = 0; i < 9; ++i) {
         this.m_38897_(new Slot(playerInventory, i, 8 + i * 18, 144));
      }

   }

   public boolean m_5882_(ItemStack pStack, Slot pSlot) {
      return pSlot.f_40219_ == this.mixingSlotIndex ? false : super.m_5882_(pStack, pSlot);
   }
}
