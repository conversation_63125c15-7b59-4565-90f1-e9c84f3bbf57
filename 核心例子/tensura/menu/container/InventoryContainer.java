package com.github.manasmods.tensura.menu.container;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import net.minecraft.world.Container;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;

public class InventoryContainer implements Container {
   private final Inventory inventory;
   private final List<Consumer<InventoryContainer>> changeListeners = new ArrayList();

   public int m_6643_() {
      return this.inventory.m_6643_();
   }

   public boolean m_7983_() {
      return this.inventory.m_7983_();
   }

   public ItemStack m_8020_(int pSlot) {
      return this.inventory.m_8020_(pSlot);
   }

   public ItemStack m_7407_(int pSlot, int pAmount) {
      return this.inventory.m_7407_(pSlot, pAmount);
   }

   public ItemStack m_8016_(int pSlot) {
      return this.inventory.m_8016_(pSlot);
   }

   public void m_6836_(int pSlot, ItemStack pStack) {
      this.inventory.m_6836_(pSlot, pStack);
   }

   public void m_6596_() {
      this.inventory.m_6596_();
      this.changeListeners.forEach((listener) -> {
         listener.accept(this);
      });
   }

   public void registerListener(Consumer<InventoryContainer> listener) {
      this.changeListeners.add(listener);
   }

   public boolean m_6542_(Player pPlayer) {
      return this.inventory.m_6542_(pPlayer);
   }

   public void m_6211_() {
      this.inventory.m_6211_();
   }

   public InventoryContainer(Inventory inventory) {
      this.inventory = inventory;
   }

   public Inventory getInventory() {
      return this.inventory;
   }
}
