package com.github.manasmods.tensura.core;

import com.github.manasmods.tensura.item.custom.HipokuteFlowerItem;
import net.minecraft.core.BlockPos;
import net.minecraft.stats.Stats;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.FlowerPotBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.BlockHitResult;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin({FlowerPotBlock.class})
public abstract class MixinFlowerPotBlock {
   @Inject(
      method = {"use"},
      at = {@At("HEAD")},
      cancellable = true
   )
   private void use(BlockState pState, Level pLevel, BlockPos pPos, Player pPlayer, InteractionHand pHand, BlockHitResult pHit, CallbackInfoReturnable<InteractionResult> cir) {
      ItemStack stack = pPlayer.m_21120_(pHand);
      Item var10 = stack.m_41720_();
      if (var10 instanceof HipokuteFlowerItem) {
         HipokuteFlowerItem item = (HipokuteFlowerItem)var10;
         if (((FlowerPotBlock)this).m_153267_()) {
            pLevel.m_7731_(pPos, ((Block)item.pottedFlower.get()).m_49966_(), 3);
            pPlayer.m_36220_(Stats.f_12961_);
            if (!pPlayer.m_150110_().f_35937_) {
               stack.m_41774_(1);
            }

            pLevel.m_142346_(pPlayer, GameEvent.f_157792_, pPos);
            cir.setReturnValue(InteractionResult.m_19078_(pLevel.f_46443_));
         }
      }

   }
}
