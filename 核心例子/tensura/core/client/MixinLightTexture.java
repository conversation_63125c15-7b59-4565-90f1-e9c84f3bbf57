package com.github.manasmods.tensura.core.client;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.merfolk.MerfolkRace;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import com.llamalad7.mixinextras.sugar.Local;
import com.llamalad7.mixinextras.sugar.ref.LocalBooleanRef;
import com.llamalad7.mixinextras.sugar.ref.LocalFloatRef;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.LightTexture;
import net.minecraft.tags.FluidTags;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.At.Shift;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin({LightTexture.class})
public class MixinLightTexture {
   @Inject(
      method = {"updateLightTexture"},
      at = {@At(
   value = "INVOKE",
   target = "Lcom/mojang/math/Vector3f;lerp(Lcom/mojang/math/Vector3f;F)V",
   ordinal = 0,
   shift = Shift.AFTER
)}
   )
   private void adjustVision(float pPartialTicks, CallbackInfo ci, @Local(ordinal = 7) LocalFloatRef bright, @Local(ordinal = 5) LocalFloatRef blindness) {
      Player player = Minecraft.m_91087_().f_91074_;
      if (player != null) {
         bright.set(this.mod$nightVision(player, bright.get()));
         blindness.set(this.mod$blindness(player, blindness.get()));
      }
   }

   @Inject(
      method = {"updateLightTexture"},
      at = {@At(
   value = "INVOKE_ASSIGN",
   target = "Lnet/minecraft/client/renderer/DimensionSpecialEffects;forceBrightLightmap()Z",
   shift = Shift.AFTER
)}
   )
   private void forceBrightLightmap(float pPartialTicks, CallbackInfo ci, @Local LocalBooleanRef blindness) {
      Player player = Minecraft.m_91087_().f_91074_;
      if (player != null) {
         if (this.mod$blindness(player, 0.0F) == 1.0F) {
            blindness.set(false);
         }

      }
   }

   @Unique
   private float mod$nightVision(Player player, float original) {
      if (player.m_21023_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get())) {
         return 1.0F;
      } else if (player.m_6844_(EquipmentSlot.HEAD).m_150930_((Item)TensuraArmorItems.ANTI_MAGIC_MASK.get())) {
         return 1.0F;
      } else if (player.m_6844_(EquipmentSlot.HEAD).m_150930_((Item)TensuraArmorItems.TEARY_PIERROT_MASK.get())) {
         return 1.0F;
      } else {
         Race race = TensuraPlayerCapability.getRace(player);
         return race instanceof MerfolkRace && player.m_204029_(FluidTags.f_13131_) ? 1.0F : original;
      }
   }

   @Unique
   private float mod$blindness(Player player, float original) {
      if (player.m_21023_((MobEffect)TensuraMobEffects.TRUE_BLINDNESS.get())) {
         return 1.0F;
      } else {
         return player.m_21023_((MobEffect)TensuraMobEffects.SLEEP_MODE.get()) ? 1.0F : original;
      }
   }
}
