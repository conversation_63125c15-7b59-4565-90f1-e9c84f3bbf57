package com.github.manasmods.tensura.core;

import net.minecraft.world.level.levelgen.structure.templatesystem.StructureProcessor;
import net.minecraft.world.level.levelgen.structure.templatesystem.StructureProcessorType;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.gen.Invoker;

@Mixin({StructureProcessor.class})
public interface AccessorStructureProcessor {
   @Invoker("getType")
   StructureProcessorType<?> callGetType();
}
