package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.MobType;
import net.minecraft.world.item.DiggerItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.SwordItem;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;

public class SturdyEnchantment extends EngravingEnchantment {
   public SturdyEnchantment() {
      super(Rarity.VERY_RARE, EnchantmentCategory.BREAKABLE, EquipmentSlot.MAINHAND);
   }

   public float getDamageBonus(int level, MobType mobType, ItemStack enchantedItem) {
      float damage = 10.0F * (float)level;
      Item var6 = enchantedItem.m_41720_();
      if (var6 instanceof SwordItem) {
         SwordItem sword = (SwordItem)var6;
         damage = sword.m_43299_() * (float)level;
      }

      var6 = enchantedItem.m_41720_();
      if (var6 instanceof DiggerItem) {
         DiggerItem digger = (DiggerItem)var6;
         damage = digger.m_41008_() * (float)level;
      }

      return damage * 0.3F;
   }

   public int m_7205_(int pLevel, DamageSource pSource) {
      if (!pSource.m_19378_() && !pSource.m_238340_() && !pSource.m_19379_()) {
         if (pSource instanceof TensuraDamageSource) {
            TensuraDamageSource source = (TensuraDamageSource)pSource;
            if (source.getIgnoreBarrier() > 0.0F) {
               return 0;
            }
         }

         return pLevel * 2;
      } else {
         return 0;
      }
   }
}
