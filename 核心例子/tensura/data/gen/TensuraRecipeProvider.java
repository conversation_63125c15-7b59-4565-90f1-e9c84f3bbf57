package com.github.manasmods.tensura.data.gen;

import com.github.manasmods.manascore.api.data.gen.RecipeProvider;
import com.github.manasmods.tensura.block.SimpleBlock;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.data.recipe.GreatSageRefiningRecipe;
import com.github.manasmods.tensura.data.recipe.KilnMeltingRecipe;
import com.github.manasmods.tensura.data.recipe.KilnMixingRecipe;
import com.github.manasmods.tensura.data.recipe.SmithingBenchRecipe;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.items.TensuraPotions;
import com.github.manasmods.tensura.registry.items.TensuraSmithingSchematicItems;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.google.common.collect.ImmutableList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Supplier;
import net.minecraft.advancements.CriterionTriggerInstance;
import net.minecraft.advancements.critereon.ItemPredicate;
import net.minecraft.advancements.critereon.ItemPredicate.Builder;
import net.minecraft.data.recipes.FinishedRecipe;
import net.minecraft.data.recipes.ShapedRecipeBuilder;
import net.minecraft.data.recipes.ShapelessRecipeBuilder;
import net.minecraft.data.recipes.SimpleCookingRecipeBuilder;
import net.minecraft.data.recipes.SingleItemRecipeBuilder;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.tags.ItemTags;
import net.minecraft.tags.TagKey;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.alchemy.Potion;
import net.minecraft.world.item.alchemy.PotionBrewing;
import net.minecraft.world.item.alchemy.Potions;
import net.minecraft.world.item.crafting.Ingredient;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.ComposterBlock;
import net.minecraft.world.level.block.RotatedPillarBlock;
import net.minecraft.world.level.block.SlabBlock;
import net.minecraft.world.level.block.StairBlock;
import net.minecraftforge.data.event.GatherDataEvent;
import net.minecraftforge.registries.RegistryObject;
import org.jetbrains.annotations.Nullable;

public class TensuraRecipeProvider extends RecipeProvider {
   public TensuraRecipeProvider(GatherDataEvent gatherDataEvent) {
      super(gatherDataEvent);
   }

   protected void generate(Consumer<FinishedRecipe> consumer) {
      this.stoneCutter(consumer);
      this.smeltingRecipes(consumer);
      this.gear(consumer);
      this.blocksAndItems(consumer);
      this.kilnMelting(consumer);
      this.kilnMixing(consumer);
      this.smithingRecipes(consumer);
      this.smithingUpgrades(consumer);
      this.greatSageRefining(consumer);
   }

   public static void brewingRecipe() {
      PotionBrewing.m_43513_(Potions.f_43602_, (Item)TensuraConsumableItems.CHILLED_SLIME.get(), (Potion)TensuraPotions.CHILL.get());
      PotionBrewing.m_43513_((Potion)TensuraPotions.CHILL.get(), Items.f_42451_, (Potion)TensuraPotions.LONG_CHILL.get());
      PotionBrewing.m_43513_((Potion)TensuraPotions.CHILL.get(), Items.f_42525_, (Potion)TensuraPotions.STRONG_CHILL.get());
      PotionBrewing.m_43513_(Potions.f_43602_, (Item)TensuraConsumableItems.RAW_SERPENT_MEAT.get(), (Potion)TensuraPotions.CORROSION.get());
      PotionBrewing.m_43513_((Potion)TensuraPotions.CORROSION.get(), Items.f_42451_, (Potion)TensuraPotions.LONG_CORROSION.get());
      PotionBrewing.m_43513_((Potion)TensuraPotions.CORROSION.get(), Items.f_42525_, (Potion)TensuraPotions.STRONG_CORROSION.get());
      PotionBrewing.m_43513_(Potions.f_43602_, (Item)TensuraMobDropItems.SPIDER_FANG.get(), (Potion)TensuraPotions.FATAL_POISON.get());
      PotionBrewing.m_43513_((Potion)TensuraPotions.FATAL_POISON.get(), Items.f_42451_, (Potion)TensuraPotions.LONG_FATAL_POISON.get());
      PotionBrewing.m_43513_((Potion)TensuraPotions.FATAL_POISON.get(), Items.f_42525_, (Potion)TensuraPotions.STRONG_FATAL_POISON.get());
      PotionBrewing.m_43513_(Potions.f_43602_, (Item)TensuraConsumableItems.GIANT_ANT_LEG.get(), (Potion)TensuraPotions.FRAGILITY.get());
      PotionBrewing.m_43513_((Potion)TensuraPotions.FRAGILITY.get(), Items.f_42451_, (Potion)TensuraPotions.LONG_FRAGILITY.get());
      PotionBrewing.m_43513_((Potion)TensuraPotions.FRAGILITY.get(), Items.f_42525_, (Potion)TensuraPotions.STRONG_FRAGILITY.get());
      PotionBrewing.m_43513_(Potions.f_43602_, (Item)TensuraMobDropItems.INVISIBLE_FEATHER.get(), (Potion)TensuraPotions.NIGHT_OWL.get());
      PotionBrewing.m_43513_((Potion)TensuraPotions.NIGHT_OWL.get(), Items.f_42451_, (Potion)TensuraPotions.LONG_NIGHT_OWL.get());
      PotionBrewing.m_43513_(Potions.f_43602_, (Item)TensuraMobDropItems.DRAGON_PEACOCK_FEATHER.get(), (Potion)TensuraPotions.GLOWING.get());
      PotionBrewing.m_43513_((Potion)TensuraPotions.GLOWING.get(), Items.f_42451_, (Potion)TensuraPotions.LONG_GLOWING.get());
      PotionBrewing.m_43513_(Potions.f_43602_, (Item)TensuraMobDropItems.CENTIPEDE_STINGER.get(), (Potion)TensuraPotions.PARALYSIS.get());
      PotionBrewing.m_43513_((Potion)TensuraPotions.PARALYSIS.get(), Items.f_42451_, (Potion)TensuraPotions.LONG_PARALYSIS.get());
      PotionBrewing.m_43513_((Potion)TensuraPotions.PARALYSIS.get(), Items.f_42525_, (Potion)TensuraPotions.STRONG_PARALYSIS.get());
      PotionBrewing.m_43500_((Item)TensuraConsumableItems.WATER_MAGIC_BOTTLE.get());
      PotionBrewing.m_43500_((Item)TensuraConsumableItems.VACUUMED_WATER_MAGIC_BOTTLE.get());
      PotionBrewing.m_43502_((Item)TensuraConsumableItems.WATER_MAGIC_BOTTLE.get(), (Item)TensuraMaterialItems.HIPOKUTE_GRASS.get(), (Item)TensuraConsumableItems.LOW_POTION.get());
      PotionBrewing.m_43502_((Item)TensuraConsumableItems.VACUUMED_WATER_MAGIC_BOTTLE.get(), (Item)TensuraMaterialItems.HIPOKUTE_GRASS.get(), (Item)TensuraConsumableItems.HIGH_POTION.get());
      PotionBrewing.m_43502_((Item)TensuraConsumableItems.WATER_MAGIC_BOTTLE.get(), (Item)TensuraMaterialItems.HIPOKUTE_FLOWER.get(), (Item)TensuraConsumableItems.HIGH_POTION.get());
      PotionBrewing.m_43502_((Item)TensuraConsumableItems.VACUUMED_WATER_MAGIC_BOTTLE.get(), (Item)TensuraMaterialItems.HIPOKUTE_FLOWER.get(), (Item)TensuraConsumableItems.FULL_POTION.get());
   }

   public static void composterRecipe() {
      ComposterBlock.f_51914_.put((ItemLike)TensuraBlocks.Items.PALM_LEAVES.get(), 0.3F);
      ComposterBlock.f_51914_.put((ItemLike)TensuraBlocks.Items.SAKURA_LEAVES.get(), 0.3F);
      ComposterBlock.f_51914_.put((ItemLike)TensuraBlocks.Items.PALM_SAPLING.get(), 0.3F);
      ComposterBlock.f_51914_.put((ItemLike)TensuraBlocks.Items.SAKURA_SAPLING.get(), 0.3F);
      ComposterBlock.f_51914_.put((ItemLike)TensuraMaterialItems.THATCH.get(), 0.65F);
      ComposterBlock.f_51914_.put((ItemLike)TensuraMaterialItems.HIPOKUTE_SEEDS.get(), 0.7F);
      ComposterBlock.f_51914_.put((ItemLike)TensuraMaterialItems.HIPOKUTE_GRASS.get(), 0.9F);
      ComposterBlock.f_51914_.put((ItemLike)TensuraMaterialItems.HIPOKUTE_FLOWER.get(), 1.0F);
   }

   public void greatSageRefining(Consumer<FinishedRecipe> consumer) {
      GreatSageRefiningRecipe.Builder.of((Item)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get(), 2).addInput((Item)TensuraBlocks.Items.MAGIC_ORE_BLOCK.get()).build(consumer);
      GreatSageRefiningRecipe.Builder.of((Item)TensuraMaterialItems.PURE_MAGISTEEL_NUGGET.get(), 2).addInput((Item)TensuraMaterialItems.MAGIC_ORE.get()).build(consumer);
      GreatSageRefiningRecipe.Builder.of((Item)TensuraMaterialItems.PURE_MAGISTEEL_NUGGET.get(), 2).addInput((Item)TensuraBlocks.Items.MAGIC_ORE.get()).build(consumer);
      GreatSageRefiningRecipe.Builder.of((Item)TensuraMaterialItems.PURE_MAGISTEEL_NUGGET.get(), 2).addInput((Item)TensuraBlocks.Items.DEEPSLATE_MAGIC_ORE.get()).build(consumer);
      List<Item> bottles = List.of(Items.f_42590_, Items.f_42612_, Items.f_42589_, (Item)TensuraConsumableItems.MAGIC_BOTTLE.get(), (Item)TensuraConsumableItems.WATER_MAGIC_BOTTLE.get(), (Item)TensuraConsumableItems.VACUUMED_WATER_MAGIC_BOTTLE.get());
      Iterator var3 = bottles.iterator();

      while(var3.hasNext()) {
         Item bottle = (Item)var3.next();
         GreatSageRefiningRecipe.Builder.of((Item)TensuraConsumableItems.HIGH_POTION.get()).addInput(bottle).addIngredient((Item)TensuraMaterialItems.HIPOKUTE_GRASS.get()).build(consumer);
         GreatSageRefiningRecipe.Builder.of((Item)TensuraConsumableItems.FULL_POTION.get()).addInput(bottle).addIngredient((Item)TensuraMaterialItems.HIPOKUTE_FLOWER.get()).build(consumer);
      }

      this.potionRefiningRecipes(consumer, (Potion)TensuraPotions.CHILL.get(), (Potion)TensuraPotions.LONG_CHILL.get(), (Potion)TensuraPotions.STRONG_CHILL.get(), (Item)TensuraConsumableItems.CHILLED_SLIME.get());
      this.potionRefiningRecipes(consumer, (Potion)TensuraPotions.CORROSION.get(), (Potion)TensuraPotions.LONG_CORROSION.get(), (Potion)TensuraPotions.STRONG_CORROSION.get(), (Item)TensuraConsumableItems.RAW_SERPENT_MEAT.get());
      this.potionRefiningRecipes(consumer, (Potion)TensuraPotions.FATAL_POISON.get(), (Potion)TensuraPotions.LONG_FATAL_POISON.get(), (Potion)TensuraPotions.STRONG_FATAL_POISON.get(), (Item)TensuraMobDropItems.SPIDER_FANG.get());
      this.potionRefiningRecipes(consumer, (Potion)TensuraPotions.FRAGILITY.get(), (Potion)TensuraPotions.LONG_FRAGILITY.get(), (Potion)TensuraPotions.STRONG_FRAGILITY.get(), (Item)TensuraConsumableItems.GIANT_ANT_LEG.get());
      this.potionRefiningRecipes(consumer, (Potion)TensuraPotions.PARALYSIS.get(), (Potion)TensuraPotions.LONG_PARALYSIS.get(), (Potion)TensuraPotions.STRONG_PARALYSIS.get(), (Item)TensuraMobDropItems.CENTIPEDE_STINGER.get());
      this.potionRefiningRecipes(consumer, (Potion)TensuraPotions.GLOWING.get(), (Potion)TensuraPotions.LONG_GLOWING.get(), (Potion)null, (Item)TensuraMobDropItems.DRAGON_PEACOCK_FEATHER.get());
      this.potionRefiningRecipes(consumer, (Potion)TensuraPotions.NIGHT_OWL.get(), (Potion)TensuraPotions.LONG_NIGHT_OWL.get(), (Potion)null, (Item)TensuraMobDropItems.INVISIBLE_FEATHER.get());
      this.potionRefiningRecipes(consumer, Potions.f_43612_, Potions.f_43613_, Potions.f_43614_, Items.f_42501_);
      this.potionRefiningRecipes(consumer, Potions.f_43607_, Potions.f_43608_, Potions.f_43609_, Items.f_42648_);
      this.potionRefiningRecipes(consumer, Potions.f_43590_, Potions.f_43591_, Potions.f_43592_, Items.f_42593_);
      this.potionRefiningRecipes(consumer, Potions.f_43584_, Potions.f_43585_, Potions.f_43586_, Items.f_42591_);
      this.potionRefiningRecipes(consumer, Potions.f_43587_, Potions.f_43588_, Potions.f_43589_, Items.f_42586_);
      this.potionRefiningRecipes(consumer, Potions.f_43618_, Potions.f_43619_, Potions.f_43620_, Items.f_42354_);
      this.potionRefiningRecipes(consumer, Potions.f_43623_, (Potion)null, Potions.f_43581_, Items.f_42546_);
      this.potionRefiningRecipes(consumer, Potions.f_43582_, (Potion)null, Potions.f_43583_, Items.f_42546_, Items.f_42592_);
      this.potionRefiningRecipes(consumer, Potions.f_43582_, (Potion)null, Potions.f_43583_, Items.f_42591_, Items.f_42592_);
      this.potionRefiningRecipes(consumer, Potions.f_43610_, Potions.f_43611_, (Potion)null, Items.f_42542_);
      this.potionRefiningRecipes(consumer, Potions.f_43621_, Potions.f_43622_, (Potion)null, Items.f_42529_);
      this.potionRefiningRecipes(consumer, Potions.f_43596_, Potions.f_43597_, (Potion)null, Items.f_42714_);
      this.potionRefiningRecipes(consumer, Potions.f_43603_, Potions.f_43604_, (Potion)null, Items.f_42677_);
      this.potionRefiningRecipes(consumer, Potions.f_43605_, Potions.f_43606_, (Potion)null, Items.f_42677_, Items.f_42592_);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43599_, Items.f_42736_).addInput(Potions.f_43599_).addIngredient(Items.f_42403_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43599_, Items.f_42739_).addInput(Potions.f_43599_).addIngredient(Items.f_42403_).addIngredient(Items.f_42735_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43599_, Items.f_42739_).addInput(Potions.f_43599_, Items.f_42736_).addIngredient(Items.f_42735_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43602_).addInput(Potions.f_43599_).addIngredient(Items.f_42588_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43602_, Items.f_42736_).addInput(Potions.f_43599_).addIngredient(Items.f_42588_).addIngredient(Items.f_42403_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43602_, Items.f_42739_).addInput(Potions.f_43599_).addIngredient(Items.f_42588_).addIngredient(Items.f_42403_).addIngredient(Items.f_42735_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43602_, Items.f_42736_).addInput(Potions.f_43599_, Items.f_42736_).addIngredient(Items.f_42588_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43602_, Items.f_42739_).addInput(Potions.f_43599_, Items.f_42736_).addIngredient(Items.f_42588_).addIngredient(Items.f_42735_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43602_, Items.f_42739_).addInput(Potions.f_43599_, Items.f_42739_).addIngredient(Items.f_42588_).addIngredient(Items.f_42735_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43593_).addInput(Potions.f_43599_).addIngredient(Items.f_42592_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43593_, Items.f_42736_).addInput(Potions.f_43599_).addIngredient(Items.f_42592_).addIngredient(Items.f_42403_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43593_, Items.f_42739_).addInput(Potions.f_43599_).addIngredient(Items.f_42592_).addIngredient(Items.f_42403_).addIngredient(Items.f_42735_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43593_, Items.f_42736_).addInput(Potions.f_43593_).addIngredient(Items.f_42403_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43593_, Items.f_42739_).addInput(Potions.f_43593_).addIngredient(Items.f_42403_).addIngredient(Items.f_42735_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43593_, Items.f_42739_).addInput(Potions.f_43593_, Items.f_42736_).addIngredient(Items.f_42735_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43594_).addInput(Potions.f_43599_).addIngredient(Items.f_42592_).addIngredient(Items.f_42451_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43594_, Items.f_42736_).addInput(Potions.f_43599_).addIngredient(Items.f_42592_).addIngredient(Items.f_42451_).addIngredient(Items.f_42403_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43594_, Items.f_42739_).addInput(Potions.f_43599_).addIngredient(Items.f_42592_).addIngredient(Items.f_42451_).addIngredient(Items.f_42403_).addIngredient(Items.f_42735_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43594_, Items.f_42736_).addInput(Potions.f_43594_).addIngredient(Items.f_42451_).addIngredient(Items.f_42403_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43594_, Items.f_42739_).addInput(Potions.f_43594_).addIngredient(Items.f_42451_).addIngredient(Items.f_42403_).addIngredient(Items.f_42735_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43594_, Items.f_42739_).addInput(Potions.f_43594_, Items.f_42736_).addIngredient(Items.f_42451_).addIngredient(Items.f_42735_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43594_).addInput(Potions.f_43593_).addIngredient(Items.f_42451_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43594_, Items.f_42736_).addInput(Potions.f_43593_).addIngredient(Items.f_42451_).addIngredient(Items.f_42403_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43594_, Items.f_42739_).addInput(Potions.f_43593_).addIngredient(Items.f_42451_).addIngredient(Items.f_42403_).addIngredient(Items.f_42735_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43594_, Items.f_42736_).addInput(Potions.f_43593_, Items.f_42736_).addIngredient(Items.f_42451_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43594_, Items.f_42739_).addInput(Potions.f_43593_, Items.f_42736_).addIngredient(Items.f_42451_).addIngredient(Items.f_42735_).build(consumer);
      GreatSageRefiningRecipe.Builder.of(Potions.f_43594_, Items.f_42739_).addInput(Potions.f_43593_, Items.f_42739_).addIngredient(Items.f_42451_).build(consumer);
   }

   private void smithingUpgrades(Consumer<FinishedRecipe> consumer) {
      m_125994_(consumer, (Item)TensuraToolItems.DIAMOND_SICKLE.get(), (Item)TensuraToolItems.NETHERITE_SICKLE.get());
      m_125994_(consumer, (Item)TensuraToolItems.DIAMOND_SHORT_SWORD.get(), (Item)TensuraToolItems.NETHERITE_SHORT_SWORD.get());
      m_125994_(consumer, (Item)TensuraToolItems.DIAMOND_LONG_SWORD.get(), (Item)TensuraToolItems.NETHERITE_LONG_SWORD.get());
      m_125994_(consumer, (Item)TensuraToolItems.DIAMOND_GREAT_SWORD.get(), (Item)TensuraToolItems.NETHERITE_GREAT_SWORD.get());
      m_125994_(consumer, (Item)TensuraToolItems.DIAMOND_SPEAR.get(), (Item)TensuraToolItems.NETHERITE_SPEAR.get());
      m_125994_(consumer, (Item)TensuraToolItems.DIAMOND_SCYTHE.get(), (Item)TensuraToolItems.NETHERITE_SCYTHE.get());
      m_125994_(consumer, (Item)TensuraToolItems.DIAMOND_KATANA.get(), (Item)TensuraToolItems.NETHERITE_KATANA.get());
      m_125994_(consumer, (Item)TensuraToolItems.DIAMOND_KODACHI.get(), (Item)TensuraToolItems.NETHERITE_KODACHI.get());
      m_125994_(consumer, (Item)TensuraToolItems.DIAMOND_TACHI.get(), (Item)TensuraToolItems.NETHERITE_TACHI.get());
      m_125994_(consumer, (Item)TensuraToolItems.DIAMOND_ODACHI.get(), (Item)TensuraToolItems.NETHERITE_ODACHI.get());
   }

   private void smeltingRecipes(Consumer<FinishedRecipe> consumer) {
      ImmutableList<ItemLike> SILVER_SMELTABLES = ImmutableList.of((ItemLike)TensuraBlocks.SILVER_ORE.get(), (ItemLike)TensuraBlocks.DEEPSLATE_SILVER_ORE.get(), (ItemLike)TensuraMaterialItems.RAW_SILVER.get());
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.BULLDEER_BEEF.get()}), (ItemLike)TensuraConsumableItems.BULLDEER_STEAK.get(), 0.35F, 200, 600, 100);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.GIANT_ANT_LEG.get()}), (ItemLike)TensuraConsumableItems.COOKED_GIANT_ANT_LEG.get(), 0.35F, 200, 600, 100);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.RAW_GIANT_BAT_MEAT.get()}), (ItemLike)TensuraConsumableItems.COOKED_GIANT_BAT_MEAT.get(), 0.35F, 200, 600, 100);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.KNIGHT_SPIDER_LEG.get()}), (ItemLike)TensuraConsumableItems.COOKED_KNIGHT_SPIDER_LEG.get(), 0.35F, 200, 600, 100);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.RAW_ARMOURSAURUS_MEAT.get()}), (ItemLike)TensuraConsumableItems.COOKED_ARMOURSAURUS_MEAT.get(), 0.35F, 200, 600, 100);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.RAW_BLADE_TIGER_MEAT.get()}), (ItemLike)TensuraConsumableItems.BLADE_TIGER_STEAK.get(), 0.5F, 300, 700, 200);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.RAW_CHARYBDIS_MEAT.get()}), (ItemLike)TensuraConsumableItems.COOKED_CHARYBDIS_MEAT.get(), 0.35F, 200, 600, 100);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.RAW_SERPENT_MEAT.get()}), (ItemLike)TensuraConsumableItems.COOKED_SERPENT_MEAT.get(), 0.35F, 200, 600, 100);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.RAW_SISSIE_MEAT.get()}), (ItemLike)TensuraConsumableItems.COOKED_SISSIE_MEAT.get(), 0.35F, 200, 600, 100);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.SISSIE_FIN.get()}), (ItemLike)TensuraConsumableItems.COOKED_SISSIE_FIN.get(), 0.5F, 200, 600, 100);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.RAW_SPEAR_TORO_MEAT.get()}), (ItemLike)TensuraConsumableItems.COOKED_SPEAR_TORO_MEAT.get(), 0.35F, 200, 600, 100);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.SPEAR_TORO_FIN.get()}), (ItemLike)TensuraConsumableItems.COOKED_SPEAR_TORO_FIN.get(), 0.5F, 200, 600, 100);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.RAW_MEGALODON_MEAT.get()}), (ItemLike)TensuraConsumableItems.COOKED_MEGALODON_MEAT.get(), 0.35F, 200, 600, 100);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.CHILLED_SLIME.get()}), (ItemLike)TensuraMobDropItems.SLIME_CHUNK.get(), 0.2F, 100, 300, 50);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraBlocks.Items.CHILLED_SLIME_BLOCK.get()}), (ItemLike)TensuraBlocks.Items.SLIME_CHUNK_BLOCK.get(), 0.2F, 200, 600, 100);
      this.allSmeltingRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraConsumableItems.WATER_MAGIC_BOTTLE.get()}), (ItemLike)TensuraConsumableItems.VACUUMED_WATER_MAGIC_BOTTLE.get(), 0.0F, 60, 180, 60);
      SimpleCookingRecipeBuilder.m_126272_(Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraBlocks.Items.SARASA_SANDSTONE.get()}), (ItemLike)TensuraBlocks.Items.SMOOTH_SARASA_SANDSTONE.get(), 0.1F, 200).m_126132_("has_sarasa_sandstone", m_125977_((ItemLike)TensuraBlocks.Items.SARASA_SANDSTONE.get())).m_176498_(consumer);
      m_176591_(consumer, SILVER_SMELTABLES, (ItemLike)TensuraMaterialItems.SILVER_INGOT.get(), 0.7F, 200, "silver_ingot");
      m_176625_(consumer, SILVER_SMELTABLES, (ItemLike)TensuraMaterialItems.SILVER_INGOT.get(), 0.7F, 100, "silver_ingot");
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.LOW_MAGISTEEL_HELMET.get()}), (ItemLike)TensuraMaterialItems.LOW_MAGISTEEL_NUGGET.get(), 0.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.LOW_MAGISTEEL_CHESTPLATE.get()}), (ItemLike)TensuraMaterialItems.LOW_MAGISTEEL_NUGGET.get(), 0.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.LOW_MAGISTEEL_LEGGINGS.get()}), (ItemLike)TensuraMaterialItems.LOW_MAGISTEEL_NUGGET.get(), 0.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.LOW_MAGISTEEL_BOOTS.get()}), (ItemLike)TensuraMaterialItems.LOW_MAGISTEEL_NUGGET.get(), 0.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.HIGH_MAGISTEEL_HELMET.get()}), (ItemLike)TensuraMaterialItems.HIGH_MAGISTEEL_NUGGET.get(), 1.0F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.HIGH_MAGISTEEL_CHESTPLATE.get()}), (ItemLike)TensuraMaterialItems.HIGH_MAGISTEEL_NUGGET.get(), 1.0F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.HIGH_MAGISTEEL_LEGGINGS.get()}), (ItemLike)TensuraMaterialItems.HIGH_MAGISTEEL_NUGGET.get(), 1.0F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.HIGH_MAGISTEEL_BOOTS.get()}), (ItemLike)TensuraMaterialItems.HIGH_MAGISTEEL_NUGGET.get(), 1.0F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.MITHRIL_HELMET.get()}), (ItemLike)TensuraMaterialItems.MITHRIL_NUGGET.get(), 1.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.MITHRIL_CHESTPLATE.get()}), (ItemLike)TensuraMaterialItems.MITHRIL_NUGGET.get(), 1.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.MITHRIL_LEGGINGS.get()}), (ItemLike)TensuraMaterialItems.MITHRIL_NUGGET.get(), 1.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.MITHRIL_BOOTS.get()}), (ItemLike)TensuraMaterialItems.MITHRIL_NUGGET.get(), 1.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.ORICHALCUM_HELMET.get()}), (ItemLike)TensuraMaterialItems.ORICHALCUM_NUGGET.get(), 1.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.ORICHALCUM_CHESTPLATE.get()}), (ItemLike)TensuraMaterialItems.ORICHALCUM_NUGGET.get(), 1.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.ORICHALCUM_LEGGINGS.get()}), (ItemLike)TensuraMaterialItems.ORICHALCUM_NUGGET.get(), 1.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.ORICHALCUM_BOOTS.get()}), (ItemLike)TensuraMaterialItems.ORICHALCUM_NUGGET.get(), 1.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.PURE_MAGISTEEL_HELMET.get()}), (ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_NUGGET.get(), 2.0F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.PURE_MAGISTEEL_CHESTPLATE.get()}), (ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_NUGGET.get(), 2.0F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.PURE_MAGISTEEL_LEGGINGS.get()}), (ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_NUGGET.get(), 2.0F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.PURE_MAGISTEEL_BOOTS.get()}), (ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_NUGGET.get(), 2.0F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.ADAMANTITE_HELMET.get()}), (ItemLike)TensuraMaterialItems.ADAMANTITE_NUGGET.get(), 2.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.ADAMANTITE_CHESTPLATE.get()}), (ItemLike)TensuraMaterialItems.ADAMANTITE_NUGGET.get(), 2.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.ADAMANTITE_LEGGINGS.get()}), (ItemLike)TensuraMaterialItems.ADAMANTITE_NUGGET.get(), 2.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.ADAMANTITE_BOOTS.get()}), (ItemLike)TensuraMaterialItems.ADAMANTITE_NUGGET.get(), 2.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.HIHIIROKANE_HELMET.get()}), (ItemLike)TensuraMaterialItems.HIHIIROKANE_NUGGET.get(), 2.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.HIHIIROKANE_CHESTPLATE.get()}), (ItemLike)TensuraMaterialItems.HIHIIROKANE_NUGGET.get(), 2.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.HIHIIROKANE_LEGGINGS.get()}), (ItemLike)TensuraMaterialItems.HIHIIROKANE_NUGGET.get(), 2.5F, 200);
      this.smeltBlastRecipes(consumer, Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraArmorItems.HIHIIROKANE_BOOTS.get()}), (ItemLike)TensuraMaterialItems.HIHIIROKANE_NUGGET.get(), 2.5F, 200);
   }

   private void gear(Consumer<FinishedRecipe> consumer) {
      this.sickle(consumer, ItemTags.f_13168_, (ItemLike)TensuraToolItems.WOODEN_SICKLE.get());
      this.sickle(consumer, ItemTags.f_13165_, (ItemLike)TensuraToolItems.STONE_SICKLE.get());
      this.sickle(consumer, (ItemLike)Items.f_42416_, (ItemLike)TensuraToolItems.IRON_SICKLE.get());
      this.toolsAndSickle(consumer, TensuraTags.Items.INGOTS_SILVER, TensuraToolItems.SILVER_AXE, TensuraToolItems.SILVER_HOE, TensuraToolItems.SILVER_PICKAXE, TensuraToolItems.SILVER_SHOVEL, TensuraToolItems.SILVER_SWORD, TensuraToolItems.SILVER_SICKLE);
      this.sickle(consumer, (ItemLike)Items.f_42417_, (ItemLike)TensuraToolItems.GOLDEN_SICKLE.get());
      this.sickle(consumer, (ItemLike)Items.f_42415_, (ItemLike)TensuraToolItems.DIAMOND_SICKLE.get());
      this.toolsAndSickle(consumer, (ItemLike)TensuraMaterialItems.LOW_MAGISTEEL_INGOT.get(), TensuraToolItems.LOW_MAGISTEEL_AXE, TensuraToolItems.LOW_MAGISTEEL_HOE, TensuraToolItems.LOW_MAGISTEEL_PICKAXE, TensuraToolItems.LOW_MAGISTEEL_SHOVEL, TensuraToolItems.LOW_MAGISTEEL_SWORD, TensuraToolItems.LOW_MAGISTEEL_SICKLE);
   }

   protected void toolsAndSickle(Consumer<FinishedRecipe> consumer, TagKey<Item> tag, RegistryObject<? extends Item> axe, RegistryObject<? extends Item> hoe, RegistryObject<? extends Item> pickaxe, RegistryObject<? extends Item> shovel, RegistryObject<? extends Item> sword, RegistryObject<? extends Item> sickle) {
      this.tools(consumer, tag, (ItemLike)axe.get(), (ItemLike)hoe.get(), (ItemLike)pickaxe.get(), (ItemLike)shovel.get(), (ItemLike)sword.get());
      this.sickle(consumer, tag, (ItemLike)sickle.get());
   }

   protected void toolsAndSickle(Consumer<FinishedRecipe> consumer, ItemLike material, RegistryObject<? extends Item> axe, RegistryObject<? extends Item> hoe, RegistryObject<? extends Item> pickaxe, RegistryObject<? extends Item> shovel, RegistryObject<? extends Item> sword, RegistryObject<? extends Item> sickle) {
      this.tools(consumer, material, (ItemLike)axe.get(), (ItemLike)hoe.get(), (ItemLike)pickaxe.get(), (ItemLike)shovel.get(), (ItemLike)sword.get());
      this.sickle(consumer, material, (ItemLike)sickle.get());
   }

   private void stoneCutter(Consumer<FinishedRecipe> consumer) {
      m_176546_(consumer, (ItemLike)TensuraBlocks.WEBBED_COBBLESTONE_SLAB.get(), (ItemLike)TensuraBlocks.WEBBED_COBBLESTONE.get(), 2);
      m_176735_(consumer, (ItemLike)TensuraBlocks.WEBBED_COBBLESTONE_STAIRS.get(), (ItemLike)TensuraBlocks.WEBBED_COBBLESTONE.get());
      SingleItemRecipeBuilder.m_126313_(Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraBlocks.WEBBED_COBBLESTONE.get()}), (ItemLike)TensuraBlocks.WEBBED_COBBLESTONE_WALL.get()).m_126132_("has_webbed_cobblestone", m_125977_((ItemLike)TensuraBlocks.WEBBED_COBBLESTONE.get())).m_176500_(consumer, "webbed_cobblestone_walls_from_stone_stonecutting");
      m_176735_(consumer, (ItemLike)TensuraBlocks.WEBBED_STONE_BRICKS.get(), (ItemLike)TensuraBlocks.WEBBED_COBBLESTONE.get());
      m_176546_(consumer, (ItemLike)TensuraBlocks.WEBBED_STONE_BRICK_SLAB.get(), (ItemLike)TensuraBlocks.WEBBED_STONE_BRICKS.get(), 2);
      m_176735_(consumer, (ItemLike)TensuraBlocks.WEBBED_STONE_BRICK_STAIRS.get(), (ItemLike)TensuraBlocks.WEBBED_STONE_BRICKS.get());
      SingleItemRecipeBuilder.m_126313_(Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraBlocks.WEBBED_STONE_BRICKS.get()}), (ItemLike)TensuraBlocks.WEBBED_STONE_BRICK_WALL.get()).m_126132_("has_webbed_bricks", m_125977_((ItemLike)TensuraBlocks.WEBBED_STONE_BRICKS.get())).m_176500_(consumer, "webbed_brick_walls_from_stone_stonecutting");
      m_176546_(consumer, (ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_SLAB.get(), (ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BLOCK.get(), 2);
      m_176735_(consumer, (ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_STAIRS.get(), (ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BLOCK.get());
      m_176735_(consumer, (ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get(), (ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BLOCK.get());
      m_176546_(consumer, (ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB.get(), (ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get(), 2);
      m_176735_(consumer, (ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS.get(), (ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      SingleItemRecipeBuilder.m_126313_(Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get()}), (ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICK_WALL.get()).m_126132_("has_magic_crystal_bricks", m_125977_((ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get())).m_176500_(consumer, "low_magic_crystal_brick_walls_from_stone_stonecutting");
      m_176735_(consumer, (ItemLike)TensuraBlocks.CHISELED_LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get(), (ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      m_176546_(consumer, (ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_SLAB.get(), (ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK.get(), 2);
      m_176735_(consumer, (ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_STAIRS.get(), (ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK.get());
      m_176735_(consumer, (ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get(), (ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK.get());
      m_176546_(consumer, (ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB.get(), (ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get(), 2);
      m_176735_(consumer, (ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS.get(), (ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      SingleItemRecipeBuilder.m_126313_(Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get()}), (ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_WALL.get()).m_126132_("has_magic_crystal_bricks", m_125977_((ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get())).m_176500_(consumer, "medium_magic_crystal_brick_walls_from_stone_stonecutting");
      m_176735_(consumer, (ItemLike)TensuraBlocks.CHISELED_MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get(), (ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      m_176546_(consumer, (ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_SLAB.get(), (ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK.get(), 2);
      m_176735_(consumer, (ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_STAIRS.get(), (ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK.get());
      m_176735_(consumer, (ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get(), (ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK.get());
      m_176546_(consumer, (ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB.get(), (ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get(), 2);
      m_176735_(consumer, (ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS.get(), (ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      SingleItemRecipeBuilder.m_126313_(Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get()}), (ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_WALL.get()).m_126132_("has_magic_crystal_bricks", m_125977_((ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get())).m_176500_(consumer, "high_magic_crystal_brick_walls_from_stone_stonecutting");
      m_176735_(consumer, (ItemLike)TensuraBlocks.CHISELED_HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get(), (ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      m_176546_(consumer, (ItemLike)TensuraBlocks.SARASA_SANDSTONE_SLAB.get(), (ItemLike)TensuraBlocks.SARASA_SANDSTONE.get(), 2);
      m_176735_(consumer, (ItemLike)TensuraBlocks.SARASA_SANDSTONE_STAIRS.get(), (ItemLike)TensuraBlocks.SARASA_SANDSTONE.get());
      SingleItemRecipeBuilder.m_126313_(Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraBlocks.SARASA_SANDSTONE.get()}), (ItemLike)TensuraBlocks.SARASA_SANDSTONE_WALL.get()).m_126132_("has_sarasa_sandstone", m_125977_((ItemLike)TensuraBlocks.SARASA_SANDSTONE.get())).m_176500_(consumer, "sarasa_sandstone_walls_from_stone_stonecutting");
      m_176735_(consumer, (ItemLike)TensuraBlocks.CUT_SARASA_SANDSTONE.get(), (ItemLike)TensuraBlocks.SARASA_SANDSTONE.get());
      m_176546_(consumer, (ItemLike)TensuraBlocks.CUT_SARASA_SANDSTONE_SLAB.get(), (ItemLike)TensuraBlocks.SARASA_SANDSTONE.get(), 2);
      m_176735_(consumer, (ItemLike)TensuraBlocks.CHISELED_SARASA_SANDSTONE.get(), (ItemLike)TensuraBlocks.SARASA_SANDSTONE.get());
      m_176546_(consumer, (ItemLike)TensuraBlocks.CUT_SARASA_SANDSTONE_SLAB.get(), (ItemLike)TensuraBlocks.CUT_SARASA_SANDSTONE.get(), 2);
      m_176546_(consumer, (ItemLike)TensuraBlocks.SMOOTH_SARASA_SANDSTONE_SLAB.get(), (ItemLike)TensuraBlocks.SMOOTH_SARASA_SANDSTONE.get(), 2);
      m_176735_(consumer, (ItemLike)TensuraBlocks.SMOOTH_SARASA_SANDSTONE_STAIRS.get(), (ItemLike)TensuraBlocks.SMOOTH_SARASA_SANDSTONE.get());
   }

   private void blocksAndItems(Consumer<FinishedRecipe> consumer) {
      this.planksFromLogs(consumer, TensuraBlocks.PALM_PLANKS, TensuraTags.Items.PALM_LOGS);
      this.planksFromLogs(consumer, TensuraBlocks.SAKURA_PLANKS, TensuraTags.Items.SAKURA_LOGS);
      m_126002_(consumer, (ItemLike)TensuraBlocks.PALM_WOOD.get(), (ItemLike)TensuraBlocks.PALM_LOG.get());
      m_126002_(consumer, (ItemLike)TensuraBlocks.SAKURA_WOOD.get(), (ItemLike)TensuraBlocks.SAKURA_LOG.get());
      m_126002_(consumer, (ItemLike)TensuraBlocks.STRIPPED_PALM_WOOD.get(), (ItemLike)TensuraBlocks.STRIPPED_PALM_LOG.get());
      m_126002_(consumer, (ItemLike)TensuraBlocks.STRIPPED_SAKURA_WOOD.get(), (ItemLike)TensuraBlocks.STRIPPED_SAKURA_LOG.get());
      this.stairs(consumer, false, TensuraBlocks.PALM_STAIRS, TensuraBlocks.PALM_PLANKS);
      this.stairs(consumer, false, TensuraBlocks.SAKURA_STAIRS, TensuraBlocks.SAKURA_PLANKS);
      this.slab(consumer, TensuraBlocks.PALM_SLAB, TensuraBlocks.PALM_PLANKS);
      this.slab(consumer, TensuraBlocks.SAKURA_SLAB, TensuraBlocks.SAKURA_PLANKS);
      this.door(consumer, (ItemLike)TensuraBlocks.PALM_PLANKS.get(), (ItemLike)TensuraBlocks.PALM_DOOR.get());
      this.door(consumer, (ItemLike)TensuraBlocks.SAKURA_PLANKS.get(), (ItemLike)TensuraBlocks.SAKURA_DOOR.get());
      this.trapdoor(consumer, (ItemLike)TensuraBlocks.PALM_PLANKS.get(), (ItemLike)TensuraBlocks.PALM_TRAPDOOR.get());
      this.trapdoor(consumer, (ItemLike)TensuraBlocks.SAKURA_PLANKS.get(), (ItemLike)TensuraBlocks.SAKURA_TRAPDOOR.get());
      this.button(consumer, (ItemLike)TensuraBlocks.PALM_PLANKS.get(), (ItemLike)TensuraBlocks.PALM_BUTTON.get());
      this.button(consumer, (ItemLike)TensuraBlocks.SAKURA_PLANKS.get(), (ItemLike)TensuraBlocks.SAKURA_BUTTON.get());
      m_176690_(consumer, (ItemLike)TensuraBlocks.PALM_PRESSURE_PLATE.get(), (ItemLike)TensuraBlocks.PALM_PLANKS.get());
      m_176690_(consumer, (ItemLike)TensuraBlocks.SAKURA_PRESSURE_PLATE.get(), (ItemLike)TensuraBlocks.SAKURA_PLANKS.get());
      this.fence(consumer, (ItemLike)TensuraBlocks.PALM_PLANKS.get(), (ItemLike)TensuraBlocks.PALM_FENCE.get());
      this.fence(consumer, (ItemLike)TensuraBlocks.SAKURA_PLANKS.get(), (ItemLike)TensuraBlocks.SAKURA_FENCE.get());
      this.fenceGate(consumer, (ItemLike)TensuraBlocks.PALM_PLANKS.get(), (ItemLike)TensuraBlocks.PALM_FENCE_GATE.get());
      this.fenceGate(consumer, (ItemLike)TensuraBlocks.SAKURA_PLANKS.get(), (ItemLike)TensuraBlocks.SAKURA_FENCE_GATE.get());
      this.sign(consumer, (ItemLike)TensuraBlocks.PALM_PLANKS.get(), (ItemLike)TensuraBlocks.Items.PALM_SIGN.get());
      this.sign(consumer, (ItemLike)TensuraBlocks.SAKURA_PLANKS.get(), (ItemLike)TensuraBlocks.Items.SAKURA_SIGN.get());
      m_126021_(consumer, (ItemLike)TensuraMaterialItems.PALM_BOAT.get(), (ItemLike)TensuraBlocks.PALM_PLANKS.get());
      m_126021_(consumer, (ItemLike)TensuraMaterialItems.SAKURA_BOAT.get(), (ItemLike)TensuraBlocks.SAKURA_PLANKS.get());
      this.chestboat(consumer, (ItemLike)TensuraMaterialItems.PALM_BOAT.get(), (ItemLike)TensuraMaterialItems.PALM_CHEST_BOAT.get());
      this.chestboat(consumer, (ItemLike)TensuraMaterialItems.SAKURA_BOAT.get(), (ItemLike)TensuraMaterialItems.SAKURA_CHEST_BOAT.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMobDropItems.LOW_QUALITY_MAGIC_CRYSTAL.get(), (ItemLike)TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BLOCK.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMobDropItems.MEDIUM_QUALITY_MAGIC_CRYSTAL.get(), (ItemLike)TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMobDropItems.HIGH_QUALITY_MAGIC_CRYSTAL.get(), (ItemLike)TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK.get());
      this.nineStorage(consumer, TensuraMobDropItems.LOW_QUALITY_MAGIC_CRYSTAL, TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BLOCK);
      this.nineStorage(consumer, TensuraMobDropItems.MEDIUM_QUALITY_MAGIC_CRYSTAL, TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK);
      this.nineStorage(consumer, TensuraMobDropItems.HIGH_QUALITY_MAGIC_CRYSTAL, TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK);
      this.stairs(consumer, false, TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_STAIRS, TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BLOCK);
      this.stairs(consumer, false, TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_STAIRS, TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK);
      this.stairs(consumer, false, TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_STAIRS, TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK);
      this.slab(consumer, TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_SLAB, new Supplier[]{TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BLOCK});
      this.slab(consumer, TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_SLAB, new Supplier[]{TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK});
      this.slab(consumer, TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_SLAB, new Supplier[]{TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK});
      this.bricks(consumer, (ItemLike)TensuraBlocks.Items.LOW_QUALITY_MAGIC_CRYSTAL_BLOCK.get(), (ItemLike)TensuraBlocks.Items.LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      this.bricks(consumer, (ItemLike)TensuraBlocks.Items.MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK.get(), (ItemLike)TensuraBlocks.Items.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      this.bricks(consumer, (ItemLike)TensuraBlocks.Items.HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK.get(), (ItemLike)TensuraBlocks.Items.HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      this.stairs(consumer, false, TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS, TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICKS);
      this.stairs(consumer, false, TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS, TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS);
      this.stairs(consumer, false, TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS, TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS);
      this.slab(consumer, TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB, new Supplier[]{TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICKS});
      this.slab(consumer, TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB, new Supplier[]{TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS});
      this.slab(consumer, TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB, new Supplier[]{TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS});
      m_176612_(consumer, (ItemLike)TensuraBlocks.Items.LOW_QUALITY_MAGIC_CRYSTAL_BRICK_WALL.get(), (ItemLike)TensuraBlocks.Items.LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      m_176612_(consumer, (ItemLike)TensuraBlocks.Items.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_WALL.get(), (ItemLike)TensuraBlocks.Items.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      m_176612_(consumer, (ItemLike)TensuraBlocks.Items.HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_WALL.get(), (ItemLike)TensuraBlocks.Items.HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      this.chiseledBricks(consumer, (ItemLike)TensuraBlocks.Items.LOW_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB.get(), (ItemLike)TensuraBlocks.Items.CHISELED_LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      this.chiseledBricks(consumer, (ItemLike)TensuraBlocks.Items.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB.get(), (ItemLike)TensuraBlocks.Items.CHISELED_MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      this.chiseledBricks(consumer, (ItemLike)TensuraBlocks.Items.HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB.get(), (ItemLike)TensuraBlocks.Items.CHISELED_HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get());
      this.stairs(consumer, false, TensuraBlocks.WEB_STAIRS, TensuraBlocks.WEB_BLOCK);
      this.slab(consumer, TensuraBlocks.WEB_SLAB, TensuraBlocks.WEB_BLOCK);
      this.stairs(consumer, false, TensuraBlocks.WEBBED_COBBLESTONE_STAIRS, TensuraBlocks.WEBBED_COBBLESTONE);
      this.slab(consumer, TensuraBlocks.WEBBED_COBBLESTONE_SLAB, TensuraBlocks.WEBBED_COBBLESTONE);
      m_176612_(consumer, (ItemLike)TensuraBlocks.Items.WEBBED_COBBLESTONE_WALL.get(), (ItemLike)TensuraBlocks.Items.WEBBED_COBBLESTONE.get());
      this.bricks(consumer, (ItemLike)TensuraBlocks.Items.WEBBED_STONE_BRICKS.get(), (ItemLike)TensuraBlocks.Items.WEBBED_COBBLESTONE.get());
      this.stairs(consumer, false, TensuraBlocks.WEBBED_STONE_BRICK_STAIRS, TensuraBlocks.WEBBED_STONE_BRICKS);
      this.slab(consumer, TensuraBlocks.WEBBED_STONE_BRICK_SLAB, TensuraBlocks.WEBBED_STONE_BRICKS);
      m_176612_(consumer, (ItemLike)TensuraBlocks.Items.WEBBED_STONE_BRICK_WALL.get(), (ItemLike)TensuraBlocks.Items.WEBBED_STONE_BRICKS.get());
      this.bricks(consumer, (ItemLike)TensuraBlocks.Items.SARASA_SANDSTONE.get(), (ItemLike)TensuraBlocks.Items.SARASA_SAND.get());
      this.stairs(consumer, false, TensuraBlocks.SARASA_SANDSTONE_STAIRS, TensuraBlocks.SARASA_SANDSTONE);
      this.slab(consumer, TensuraBlocks.SARASA_SANDSTONE_SLAB, TensuraBlocks.SARASA_SANDSTONE);
      m_176612_(consumer, (ItemLike)TensuraBlocks.SARASA_SANDSTONE_WALL.get(), (ItemLike)TensuraBlocks.SARASA_SANDSTONE.get());
      this.bricks(consumer, (ItemLike)TensuraBlocks.Items.CUT_SARASA_SANDSTONE.get(), (ItemLike)TensuraBlocks.Items.SARASA_SANDSTONE.get());
      this.slab(consumer, TensuraBlocks.CUT_SARASA_SANDSTONE_SLAB, TensuraBlocks.CUT_SARASA_SANDSTONE);
      this.stairs(consumer, false, TensuraBlocks.SMOOTH_SARASA_SANDSTONE_STAIRS, TensuraBlocks.SMOOTH_SARASA_SANDSTONE);
      this.slab(consumer, TensuraBlocks.SMOOTH_SARASA_SANDSTONE_SLAB, new Supplier[]{TensuraBlocks.SMOOTH_SARASA_SANDSTONE});
      this.chiseledBricks(consumer, (ItemLike)TensuraBlocks.Items.SARASA_SANDSTONE_SLAB.get(), (ItemLike)TensuraBlocks.Items.CHISELED_SARASA_SANDSTONE.get());
      this.nineStorage(consumer, TensuraMaterialItems.RAW_SILVER, TensuraBlocks.RAW_SILVER_BLOCK);
      this.nineStorage(consumer, TensuraMaterialItems.SILVER_NUGGET, TensuraMaterialItems.SILVER_INGOT);
      this.nineStorage(consumer, TensuraMaterialItems.SILVER_INGOT, TensuraBlocks.SILVER_BLOCK);
      this.nineStorage(consumer, TensuraMaterialItems.MAGIC_ORE, TensuraBlocks.MAGIC_ORE_BLOCK);
      this.nineStorage(consumer, TensuraMaterialItems.LOW_MAGISTEEL_NUGGET, TensuraMaterialItems.LOW_MAGISTEEL_INGOT);
      this.nineStorage(consumer, TensuraMaterialItems.LOW_MAGISTEEL_INGOT, TensuraBlocks.LOW_MAGISTEEL_BLOCK);
      this.nineStorage(consumer, TensuraMaterialItems.HIGH_MAGISTEEL_NUGGET, TensuraMaterialItems.HIGH_MAGISTEEL_INGOT);
      this.nineStorage(consumer, TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, TensuraBlocks.HIGH_MAGISTEEL_BLOCK);
      this.nineStorage(consumer, TensuraMaterialItems.MITHRIL_NUGGET, TensuraMaterialItems.MITHRIL_INGOT);
      this.nineStorage(consumer, TensuraMaterialItems.MITHRIL_INGOT, TensuraBlocks.MITHRIL_BLOCK);
      this.nineStorage(consumer, TensuraMaterialItems.ORICHALCUM_NUGGET, TensuraMaterialItems.ORICHALCUM_INGOT);
      this.nineStorage(consumer, TensuraMaterialItems.ORICHALCUM_INGOT, TensuraBlocks.ORICHALCUM_BLOCK);
      this.nineStorage(consumer, TensuraMaterialItems.PURE_MAGISTEEL_NUGGET, TensuraMaterialItems.PURE_MAGISTEEL_INGOT);
      this.nineStorage(consumer, TensuraMaterialItems.PURE_MAGISTEEL_INGOT, TensuraBlocks.PURE_MAGISTEEL_BLOCK);
      this.nineStorage(consumer, TensuraMaterialItems.ADAMANTITE_NUGGET, TensuraMaterialItems.ADAMANTITE_INGOT);
      this.nineStorage(consumer, TensuraMaterialItems.ADAMANTITE_INGOT, TensuraBlocks.ADAMANTITE_BLOCK);
      this.nineStorage(consumer, TensuraMaterialItems.HIHIIROKANE_NUGGET, TensuraMaterialItems.HIHIIROKANE_INGOT);
      this.nineStorage(consumer, TensuraMaterialItems.HIHIIROKANE_INGOT, TensuraBlocks.HIHIIROKANE_BLOCK);
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.RAW_SILVER.get(), (ItemLike)TensuraBlocks.RAW_SILVER_BLOCK.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.SILVER_NUGGET.get(), (ItemLike)TensuraMaterialItems.SILVER_INGOT.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.SILVER_INGOT.get(), (ItemLike)TensuraBlocks.SILVER_BLOCK.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.MAGIC_ORE.get(), (ItemLike)TensuraBlocks.MAGIC_ORE_BLOCK.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.LOW_MAGISTEEL_NUGGET.get(), (ItemLike)TensuraMaterialItems.LOW_MAGISTEEL_INGOT.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.LOW_MAGISTEEL_INGOT.get(), (ItemLike)TensuraBlocks.LOW_MAGISTEEL_BLOCK.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.HIGH_MAGISTEEL_NUGGET.get(), (ItemLike)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT.get(), (ItemLike)TensuraBlocks.HIGH_MAGISTEEL_BLOCK.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.MITHRIL_NUGGET.get(), (ItemLike)TensuraMaterialItems.MITHRIL_INGOT.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.MITHRIL_INGOT.get(), (ItemLike)TensuraBlocks.MITHRIL_BLOCK.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.ORICHALCUM_NUGGET.get(), (ItemLike)TensuraMaterialItems.ORICHALCUM_INGOT.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.ORICHALCUM_INGOT.get(), (ItemLike)TensuraBlocks.ORICHALCUM_BLOCK.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_NUGGET.get(), (ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get(), (ItemLike)TensuraBlocks.PURE_MAGISTEEL_BLOCK.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.ADAMANTITE_NUGGET.get(), (ItemLike)TensuraMaterialItems.ADAMANTITE_INGOT.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.ADAMANTITE_INGOT.get(), (ItemLike)TensuraBlocks.ADAMANTITE_BLOCK.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.HIHIIROKANE_NUGGET.get(), (ItemLike)TensuraMaterialItems.HIHIIROKANE_INGOT.get());
      this.nineStorageReverse(consumer, (ItemLike)TensuraMaterialItems.HIHIIROKANE_INGOT.get(), (ItemLike)TensuraBlocks.HIHIIROKANE_BLOCK.get());
      this.magicBottles(consumer, (ItemLike)TensuraConsumableItems.MAGIC_BOTTLE.get(), 3, Items.f_41904_, (ItemLike)TensuraMobDropItems.LOW_QUALITY_MAGIC_CRYSTAL.get());
      this.magicBottles(consumer, (ItemLike)TensuraConsumableItems.MAGIC_BOTTLE.get(), 6, Items.f_41904_, (ItemLike)TensuraMobDropItems.MEDIUM_QUALITY_MAGIC_CRYSTAL.get());
      this.magicBottles(consumer, (ItemLike)TensuraConsumableItems.MAGIC_BOTTLE.get(), 9, Items.f_41904_, (ItemLike)TensuraMobDropItems.HIGH_QUALITY_MAGIC_CRYSTAL.get());
      this.elementalCores(consumer, (ItemLike)TensuraMaterialItems.ELEMENT_CORE_EMPTY.get(), (ItemLike)TensuraMobDropItems.MEDIUM_QUALITY_MAGIC_CRYSTAL.get(), (ItemLike)TensuraMaterialItems.MAGIC_STONE.get());
      this.elementalCores(consumer, (ItemLike)TensuraMaterialItems.ELEMENT_CORE_EARTH.get(), (ItemLike)TensuraMaterialItems.EARTH_ELEMENTAL_SHARD.get(), (ItemLike)TensuraMaterialItems.ELEMENT_CORE_EMPTY.get());
      this.elementalCores(consumer, (ItemLike)TensuraMaterialItems.ELEMENT_CORE_FIRE.get(), (ItemLike)TensuraMaterialItems.FIRE_ELEMENTAL_SHARD.get(), (ItemLike)TensuraMaterialItems.ELEMENT_CORE_EMPTY.get());
      this.elementalCores(consumer, (ItemLike)TensuraMaterialItems.ELEMENT_CORE_SPACE.get(), (ItemLike)TensuraMaterialItems.SPACE_ELEMENTAL_SHARD.get(), (ItemLike)TensuraMaterialItems.ELEMENT_CORE_EMPTY.get());
      this.elementalCores(consumer, (ItemLike)TensuraMaterialItems.ELEMENT_CORE_WATER.get(), (ItemLike)TensuraMaterialItems.WATER_ELEMENTAL_SHARD.get(), (ItemLike)TensuraMaterialItems.ELEMENT_CORE_EMPTY.get());
      this.elementalCores(consumer, (ItemLike)TensuraMaterialItems.ELEMENT_CORE_WIND.get(), (ItemLike)TensuraMaterialItems.WIND_ELEMENTAL_SHARD.get(), (ItemLike)TensuraMaterialItems.ELEMENT_CORE_EMPTY.get());
      ShapelessRecipeBuilder.m_126189_(Items.f_42656_).m_126209_((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_D.get()).m_126209_(Items.f_42516_).m_126209_(Items.f_42401_).m_126132_("has_monster_leather_d", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_(new ItemLike[]{(ItemLike)TensuraMobDropItems.MONSTER_LEATHER_D.get()}).m_45077_()})).m_176500_(consumer, "name_tag_from_monster_leather");
      ShapedRecipeBuilder.m_126116_(Items.f_42450_).m_126127_('L', (ItemLike)TensuraMobDropItems.MONSTER_LEATHER_D.get()).m_126127_('I', Items.f_42416_).m_126127_('S', Items.f_42401_).m_126130_("LLL").m_126130_("I I").m_126130_("S S").m_126132_("has_monster_leather_d", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_(new ItemLike[]{(ItemLike)TensuraMobDropItems.MONSTER_LEATHER_D.get()}).m_45077_()})).m_176500_(consumer, "saddle_crafting");
      this.nineStorage(consumer, TensuraMobDropItems.SLIME_CHUNK, TensuraBlocks.SLIME_CHUNK_BLOCK);
      this.nineStorageReverse(consumer, (ItemLike)TensuraMobDropItems.SLIME_CHUNK.get(), (ItemLike)TensuraBlocks.SLIME_CHUNK_BLOCK.get());
      this.nineStorage(consumer, TensuraConsumableItems.CHILLED_SLIME, TensuraBlocks.CHILLED_SLIME_BLOCK);
      this.nineStorageReverse(consumer, (ItemLike)TensuraConsumableItems.CHILLED_SLIME.get(), (ItemLike)TensuraBlocks.CHILLED_SLIME_BLOCK.get());
      ShapelessRecipeBuilder.m_126189_(Items.f_42518_).m_126211_((ItemLike)TensuraMobDropItems.SLIME_CHUNK.get(), 4).m_126132_("has_slime_chunks", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_(new ItemLike[]{(ItemLike)TensuraMobDropItems.SLIME_CHUNK.get()}).m_45077_()})).m_176500_(consumer, "slime_ball_from_chunks");
      this.enchantedApple(consumer, (ItemLike)TensuraConsumableItems.CHILLED_SLIME.get(), Items.f_42452_, (ItemLike)TensuraMobDropItems.SLIME_CHUNK.get());
      this.enchantedApple(consumer, (ItemLike)TensuraBlocks.Items.CHILLED_SLIME_BLOCK.get(), Items.f_41981_, (ItemLike)TensuraBlocks.Items.SLIME_CHUNK_BLOCK.get());
      this.enchantedApple(consumer, Items.f_42437_, TensuraTags.Items.INGOTS_ORICHALCUM, Items.f_42436_, "orichalcum_ingot");
      this.enchantedApple(consumer, (ItemLike)TensuraConsumableItems.SILVER_APPLE.get(), TensuraTags.Items.INGOTS_SILVER, Items.f_42410_, "silver_ingot");
      this.enchantedApple(consumer, (ItemLike)TensuraConsumableItems.ENCHANTED_SILVER_APPLE.get(), TensuraTags.Items.INGOTS_MITHRIL, (ItemLike)TensuraConsumableItems.SILVER_APPLE.get(), "mithril_ingot");
      this.raceResetScroll(consumer, (ItemLike)TensuraMaterialItems.RACE_RESET_SCROLL.get(), Items.f_42516_, TensuraTags.Items.INGOTS_MITHRIL, (ItemLike)TensuraMaterialItems.MAGIC_STONE.get(), (ItemLike)TensuraMobDropItems.MONSTER_LEATHER_A.get());
      this.skillResetScroll(consumer, (ItemLike)TensuraMaterialItems.SKILL_RESET_SCROLL.get(), Items.f_42516_, (ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get(), (TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, (ItemLike)TensuraMaterialItems.MAGIC_STONE.get(), (ItemLike)TensuraMobDropItems.MONSTER_LEATHER_A.get());
      this.skillResetScroll(consumer, (ItemLike)TensuraMaterialItems.CHARACTER_RESET_SCROLL.get(), Items.f_42516_, (ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get(), (ItemLike)((ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get()), (ItemLike)TensuraMaterialItems.MAGIC_STONE.get(), (ItemLike)TensuraMobDropItems.MONSTER_LEATHER_A.get());
      this.nineStorage(consumer, TensuraMaterialItems.THATCH, TensuraBlocks.THATCH_BLOCK);
      this.stairs(consumer, false, TensuraBlocks.THATCH_STAIRS, TensuraBlocks.THATCH_BLOCK);
      this.slab(consumer, TensuraBlocks.THATCH_SLAB, TensuraBlocks.THATCH_BLOCK);
      this.magicEngine(consumer, (ItemLike)TensuraBlocks.STONE_BRICK_MAGIC_ENGINE.get(), (ItemLike)TensuraMobDropItems.HIGH_QUALITY_MAGIC_CRYSTAL.get(), (ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get(), Items.f_42018_);
      this.magicEngine(consumer, (ItemLike)TensuraBlocks.BRICK_MAGIC_ENGINE.get(), (ItemLike)TensuraMobDropItems.HIGH_QUALITY_MAGIC_CRYSTAL.get(), (ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get(), Items.f_41995_);
      ShapelessRecipeBuilder.m_126189_((ItemLike)TensuraBlocks.SMITHING_BENCH.get()).m_126209_(Items.f_42146_).m_126209_(Items.f_42775_).m_126209_(Items.f_41960_).m_126132_("has_anvil", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_(new ItemLike[]{Items.f_42146_}).m_45077_()})).m_126132_("has_smithing_table", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_(new ItemLike[]{Items.f_42775_}).m_45077_()})).m_126132_("has_crafting_table", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_(new ItemLike[]{Items.f_41960_}).m_45077_()})).m_176498_(consumer);
      ShapelessRecipeBuilder.m_126191_((ItemLike)TensuraBlocks.Items.UNLIT_TORCH.get(), 1).m_126209_(Items.f_42000_).m_126132_("has_torch", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_(new ItemLike[]{Items.f_42000_}).m_45077_()})).m_176498_(consumer);
      ShapelessRecipeBuilder.m_126191_((ItemLike)TensuraBlocks.Items.UNLIT_LANTERN.get(), 1).m_126209_(Items.f_42778_).m_126132_("has_lantern", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_(new ItemLike[]{Items.f_42778_}).m_45077_()})).m_176498_(consumer);
      ShapedRecipeBuilder.m_126118_((ItemLike)TensuraBlocks.Items.KILN.get(), 1).m_126145_("sign").m_126127_('#', Items.f_41999_).m_126127_('N', Items.f_42418_).m_126127_('C', Items.f_42544_).m_126127_('B', Items.f_42770_).m_126130_("#N#").m_126130_("CBC").m_126130_("###").m_126132_("has_obsidian", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_(new ItemLike[]{Items.f_41999_}).m_45077_()})).m_126132_("has_netherite_ingot", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_(new ItemLike[]{Items.f_42418_}).m_45077_()})).m_126132_("has_blast_furnace", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_(new ItemLike[]{Items.f_42770_}).m_45077_()})).m_126132_("has_cauldron", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_(new ItemLike[]{Items.f_42544_}).m_45077_()})).m_176498_(consumer);
      ShapedRecipeBuilder.m_126118_((ItemLike)TensuraBlocks.THATCH_BED.get(), 1).m_126145_("beds").m_126127_('#', (ItemLike)TensuraBlocks.THATCH_BLOCK.get()).m_126127_('I', (ItemLike)TensuraMaterialItems.THATCH.get()).m_126130_("   ").m_126130_("  I").m_126130_("###").m_126132_("has_thatch_block", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_(new ItemLike[]{(ItemLike)TensuraBlocks.THATCH_BLOCK.get()}).m_45077_()})).m_126132_("has_thatch_item", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_(new ItemLike[]{(ItemLike)TensuraMaterialItems.THATCH.get()}).m_45077_()})).m_176498_(consumer);
      ShapelessRecipeBuilder.m_126189_((ItemLike)TensuraConsumableItems.DUBIOUS_FOOD.get()).m_206419_(TensuraTags.Items.DUBIOUS_POISON_INGREDIENT).m_206419_(TensuraTags.Items.DUBIOUS_MAGIC_INGREDIENT).m_206419_(TensuraTags.Items.DUBIOUS_RAW_INGREDIENT).m_206419_(TensuraTags.Items.DUBIOUS_EFFECT_INGREDIENT).m_206419_(TensuraTags.Items.DUBIOUS_CRYSTAL_INGREDIENT).m_206419_(TensuraTags.Items.DUBIOUS_BREWING_INGREDIENT).m_206419_(TensuraTags.Items.DUBIOUS_MUSHROOM_INGREDIENT).m_206419_(TensuraTags.Items.RAW_MONSTER_CONSUMABLES).m_206419_(ItemTags.f_13145_).m_126132_("has_dubious_ingredient", m_126011_(new ItemPredicate[]{Builder.m_45068_().m_204145_(TensuraTags.Items.DUBIOUS_EFFECT_INGREDIENT).m_45077_()})).m_176498_(consumer);
   }

   private void nineStorage(Consumer<FinishedRecipe> consumer, RegistryObject<Item> material, RegistryObject<? extends ItemLike> block) {
      this.nineStorage(consumer, (ItemLike)material.get(), (ItemLike)block.get());
   }

   protected void nineStorageReverse(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike result, ItemLike original) {
      ShapelessRecipeBuilder.m_126191_(result, 9).m_126209_(original).m_126145_(result.toString()).m_126132_("has_" + original, m_125977_(original)).m_176498_(finishedRecipeConsumer);
   }

   private void slab(Consumer<FinishedRecipe> consumer, RegistryObject<SlabBlock> slab, RegistryObject<? extends Block> planks) {
      m_176700_(consumer, (ItemLike)slab.get(), (ItemLike)planks.get());
   }

   private void stairs(Consumer<FinishedRecipe> consumer, Supplier<? extends StairBlock> stairs, RegistryObject<? extends Block> planks) {
      this.stairs(consumer, true, stairs, planks);
   }

   private void stairs(Consumer<FinishedRecipe> consumer, boolean b, Supplier<? extends StairBlock> stairs, RegistryObject<? extends Block> planks) {
      this.stairs(consumer, b, (Block)stairs.get(), new Block[]{(Block)planks.get()});
   }

   private void planksFromLogs(Consumer<FinishedRecipe> consumer, RegistryObject<? extends ItemLike> palmPlanks, TagKey<Item> palmLogs) {
      m_206412_(consumer, (ItemLike)palmPlanks.get(), palmLogs);
   }

   private void smithingRecipes(Consumer<FinishedRecipe> consumer) {
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.KANABO).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraToolItems.GOBLIN_CLUB, 1).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 1).addIngredient((Item)Items.f_42417_, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SHORT_BOW).requiresSchematic((Supplier)TensuraSmithingSchematicItems.BASIC_BOWS).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).addIngredient((Item)Items.f_42401_, 2).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42411_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.BASIC_BOWS).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).addIngredient((Item)Items.f_42401_, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LONG_BOW).requiresSchematic((Supplier)TensuraSmithingSchematicItems.BASIC_BOWS).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 4).addIngredient((Item)Items.f_42401_, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.WAR_BOW).requiresSchematic((Supplier)TensuraSmithingSchematicItems.BASIC_BOWS).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 5).addIngredient((Item)Items.f_42401_, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SHORT_SPIDER_BOW).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPIDER_BOWS).addIngredient((Supplier)TensuraConsumableItems.KNIGHT_SPIDER_LEG, 2).addIngredient((TagKey)TensuraTags.Items.STRONG_THREAD, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SPIDER_BOW).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPIDER_BOWS).addIngredient((Supplier)TensuraConsumableItems.KNIGHT_SPIDER_LEG, 3).addIngredient((TagKey)TensuraTags.Items.STRONG_THREAD, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LONG_SPIDER_BOW).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPIDER_BOWS).addIngredient((Supplier)TensuraConsumableItems.KNIGHT_SPIDER_LEG, 4).addIngredient((TagKey)TensuraTags.Items.STRONG_THREAD, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.WAR_SPIDER_BOW).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPIDER_BOWS).addIngredient((Supplier)TensuraConsumableItems.KNIGHT_SPIDER_LEG, 5).addIngredient((TagKey)TensuraTags.Items.STRONG_THREAD, 5).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42412_, 6).requiresSchematic((Supplier)TensuraSmithingSchematicItems.BASIC_BOWS).addIngredient((Item)Items.f_42484_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).addIngredient((Item)Items.f_42402_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Item)TensuraToolItems.INVISIBLE_ARROW.get(), 6).requiresSchematic((Supplier)TensuraSmithingSchematicItems.BASIC_BOWS).addIngredient((Item)Items.f_42484_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).addIngredient((Supplier)TensuraMobDropItems.INVISIBLE_FEATHER, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Item)TensuraToolItems.SPEARED_FIN_ARROW.get(), 6).requiresSchematic((Supplier)TensuraSmithingSchematicItems.BASIC_BOWS).addIngredient((Item)Items.f_42484_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).addIngredient((Supplier)TensuraConsumableItems.SPEAR_TORO_FIN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ICE_BLADE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 3).addIngredient((Supplier)TensuraMaterialItems.ELEMENT_CORE_WATER, 2).addIngredient((Item)Items.f_42545_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.TEMPEST_SCALE_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.CHARYBDIS_SCALEMAIL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((Supplier)TensuraMobDropItems.CHARYBDIS_SCALE, 3).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.TEMPEST_SCALE_KNIFE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.CHARYBDIS_SCALEMAIL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).addIngredient((Supplier)TensuraMobDropItems.CHARYBDIS_SCALE, 2).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.TEMPEST_SCALE_SHIELD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.CHARYBDIS_SCALEMAIL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHIELD).addIngredient((Supplier)TensuraMobDropItems.CHARYBDIS_SCALE, 6).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SISSIE_TOOTH_PICKAXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).addIngredient((Item)((Item)TensuraMobDropItems.SISSIE_TOOTH.get()), 2).addIngredient((Item)((Item)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT.get()), 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.CENTIPEDE_DAGGER).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HUNTING_KNIFE).addIngredient((Item)((Item)TensuraMobDropItems.CENTIPEDE_STINGER.get()), 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).addIngredient((Item)Items.f_42401_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SPIDER_DAGGER).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HUNTING_KNIFE).addIngredient((Item)((Item)TensuraMobDropItems.SPIDER_FANG.get()), 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).addIngredient((Item)Items.f_42401_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.BEAST_HORN_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Item)((Item)TensuraMobDropItems.BEAST_HORN.get()), 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.UNICORN_HORN_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Item)((Item)TensuraMobDropItems.UNICORN_HORN.get()), 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.BLADE_TIGER_SCYTHE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Item)((Item)TensuraMobDropItems.BLADE_TIGER_TAIL.get()), 1).addIngredient((Item)Items.f_42417_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.WOODEN_SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).addIngredient((TagKey)ItemTags.f_13168_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.WOODEN_LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((TagKey)ItemTags.f_13168_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.WOODEN_GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).addIngredient((TagKey)ItemTags.f_13168_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.WOODEN_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((TagKey)ItemTags.f_13168_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.WOODEN_SCYTHE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((TagKey)ItemTags.f_13168_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.WOODEN_KATANA).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)ItemTags.f_13168_, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.WOODEN_KODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)ItemTags.f_13168_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.WOODEN_TACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)ItemTags.f_13168_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.WOODEN_ODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)ItemTags.f_13168_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42407_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LEATHER_GEAR).addIngredient((Item)Items.f_42454_, 5).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42408_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LEATHER_GEAR).addIngredient((Item)Items.f_42454_, 8).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42462_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LEATHER_GEAR).addIngredient((Item)Items.f_42454_, 7).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42463_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LEATHER_GEAR).addIngredient((Item)Items.f_42454_, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.STONE_SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).addIngredient((TagKey)ItemTags.f_13165_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.STONE_LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((TagKey)ItemTags.f_13165_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.STONE_GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).addIngredient((TagKey)ItemTags.f_13165_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.STONE_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((TagKey)ItemTags.f_13165_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.STONE_SCYTHE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((TagKey)ItemTags.f_13165_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.STONE_KATANA).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)ItemTags.f_13165_, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.STONE_KODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)ItemTags.f_13165_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.STONE_TACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)ItemTags.f_13165_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.STONE_ODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)ItemTags.f_13165_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42450_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_D, 3).addIngredient((Item)Items.f_42416_, 2).addIngredient((Item)Items.f_42401_, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Item)TensuraMaterialItems.MONSTER_SADDLE.get()).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_C, 5).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_NUGGET, 8).addIngredient((Supplier)TensuraMobDropItems.STEEL_THREAD, 2).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42654_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_D, 2).addIngredient((Item)Items.f_42454_, 5).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42651_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_D, 2).addIngredient((Item)Items.f_42416_, 5).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42652_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_C, 2).addIngredient((Item)Items.f_42417_, 5).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42653_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_B, 2).addIngredient((Item)Items.f_42415_, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.WINGED_SHOES).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.WINGED_SHOES).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_C, 4).addIngredient((Supplier)TensuraMobDropItems.DRAGON_PEACOCK_FEATHER, 4).addIngredient((Supplier)TensuraMaterialItems.MAGIC_STONE, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.BAT_GLIDER).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.GIANT_BAT_WING, 2).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_C, 2).addIngredient((Supplier)TensuraMaterialItems.MAGIC_STONE, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_D_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_D, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_D_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_D, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_D_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_D, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_D_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_D, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_C_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_C, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_C_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_C, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_C_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_C, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_C_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_C, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_B_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_B, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_B_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_B, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_B_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_B, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_B_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_B, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_A_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_A, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_A_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_A, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_A_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_A, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_A_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_A, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_SPECIAL_A, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_SPECIAL_A, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_SPECIAL_A, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_SPECIAL_A, 4).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42430_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).addIngredient((Item)Items.f_42417_, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.GOLDEN_SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).addIngredient((Item)Items.f_42417_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.GOLDEN_LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((Item)Items.f_42417_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.GOLDEN_GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).addIngredient((Item)Items.f_42417_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.GOLDEN_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Item)Items.f_42417_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.GOLDEN_SCYTHE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Item)Items.f_42417_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.GOLDEN_KATANA).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Item)Items.f_42417_, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.GOLDEN_KODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Item)Items.f_42417_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.GOLDEN_TACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Item)Items.f_42417_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.GOLDEN_ODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Item)Items.f_42417_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42432_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).addIngredient((Item)Items.f_42417_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42433_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).addIngredient((Item)Items.f_42417_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42431_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).addIngredient((Item)Items.f_42417_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42434_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).addIngredient((Item)Items.f_42417_, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.GOLDEN_SICKLE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).addIngredient((Item)Items.f_42417_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42476_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).addIngredient((Item)Items.f_42417_, 5).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42477_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).addIngredient((Item)Items.f_42417_, 8).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42478_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).addIngredient((Item)Items.f_42417_, 7).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42479_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).addIngredient((Item)Items.f_42417_, 4).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42436_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GOLD_GEAR).addIngredient((Item)Items.f_42410_, 1).addIngredient((Item)Items.f_42417_, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_SCYTHE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_KATANA).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_KODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_TACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_ODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_PICKAXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_AXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_SHOVEL).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_HOE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SILVER_SICKLE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.SILVER_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.SILVER_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.SILVER_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.SILVER_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraConsumableItems.SILVER_APPLE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SILVER_GEAR).addIngredient((Item)Items.f_42410_, 1).addIngredient((TagKey)TensuraTags.Items.INGOTS_SILVER, 8).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42383_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).addIngredient((Item)Items.f_42416_, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.IRON_SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).addIngredient((Item)Items.f_42416_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.IRON_LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((Item)Items.f_42416_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.IRON_GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).addIngredient((Item)Items.f_42416_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.IRON_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Item)Items.f_42416_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.IRON_SCYTHE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Item)Items.f_42416_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.IRON_KATANA).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Item)Items.f_42416_, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.IRON_KODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Item)Items.f_42416_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.IRON_TACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Item)Items.f_42416_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.IRON_ODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Item)Items.f_42416_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42385_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).addIngredient((Item)Items.f_42416_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42386_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).addIngredient((Item)Items.f_42416_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42384_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).addIngredient((Item)Items.f_42416_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42387_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).addIngredient((Item)Items.f_42416_, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.IRON_SICKLE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).addIngredient((Item)Items.f_42416_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42468_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).addIngredient((Item)Items.f_42416_, 5).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42469_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).addIngredient((Item)Items.f_42416_, 8).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42470_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).addIngredient((Item)Items.f_42416_, 7).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42471_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.IRON_GEAR).addIngredient((Item)Items.f_42416_, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ANT_CROSSBOW).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ANT_CARAPACE_GEAR).addIngredient((Supplier)TensuraConsumableItems.GIANT_ANT_LEG, 3).addIngredient((Supplier)TensuraMobDropItems.STEEL_THREAD, 2).addIngredient((Item)Items.f_42109_, 1).addIngredient((Item)Items.f_42416_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ANT_CARAPACE_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ANT_CARAPACE_GEAR).addIngredient((Supplier)TensuraMobDropItems.GIANT_ANT_CARAPACE, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ANT_CARAPACE_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ANT_CARAPACE_GEAR).addIngredient((Supplier)TensuraMobDropItems.GIANT_ANT_CARAPACE, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ANT_CARAPACE_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ANT_CARAPACE_GEAR).addIngredient((Supplier)TensuraMobDropItems.GIANT_ANT_CARAPACE, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ANT_CARAPACE_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ANT_CARAPACE_GEAR).addIngredient((Supplier)TensuraMobDropItems.GIANT_ANT_CARAPACE, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.SERPENT_SCALEMAIL_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SERPENT_SCALEMAIL_GEAR).addIngredient((Supplier)TensuraMobDropItems.SERPENT_SCALE, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.SERPENT_SCALEMAIL_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SERPENT_SCALEMAIL_GEAR).addIngredient((Supplier)TensuraMobDropItems.SERPENT_SCALE, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.SERPENT_SCALEMAIL_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SERPENT_SCALEMAIL_GEAR).addIngredient((Supplier)TensuraMobDropItems.SERPENT_SCALE, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.SERPENT_SCALEMAIL_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SERPENT_SCALEMAIL_GEAR).addIngredient((Supplier)TensuraMobDropItems.SERPENT_SCALE, 4).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42388_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).addIngredient((Item)Items.f_42415_, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.DIAMOND_SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).addIngredient((Item)Items.f_42415_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.DIAMOND_LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((Item)Items.f_42415_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.DIAMOND_GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).addIngredient((Item)Items.f_42415_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.DIAMOND_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Item)Items.f_42415_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.DIAMOND_SCYTHE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Item)Items.f_42415_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.DIAMOND_KATANA).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Item)Items.f_42415_, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.DIAMOND_KODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Item)Items.f_42415_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.DIAMOND_TACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Item)Items.f_42415_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.DIAMOND_ODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Item)Items.f_42415_, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42390_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).addIngredient((Item)Items.f_42415_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42391_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).addIngredient((Item)Items.f_42415_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42389_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).addIngredient((Item)Items.f_42415_, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42392_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).addIngredient((Item)Items.f_42415_, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.DIAMOND_SICKLE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).addIngredient((Item)Items.f_42415_, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42472_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).addIngredient((Item)Items.f_42415_, 5).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42473_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).addIngredient((Item)Items.f_42415_, 8).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42474_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).addIngredient((Item)Items.f_42415_, 7).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42475_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DIAMOND_GEAR).addIngredient((Item)Items.f_42415_, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.KNIGHT_SPIDER_CARAPACE_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.KNIGHT_SPIDER_CARAPACE_GEAR).addIngredient((Supplier)TensuraMobDropItems.KNIGHT_SPIDER_CARAPACE, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.KNIGHT_SPIDER_CARAPACE_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.KNIGHT_SPIDER_CARAPACE_GEAR).addIngredient((Supplier)TensuraMobDropItems.KNIGHT_SPIDER_CARAPACE, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.KNIGHT_SPIDER_CARAPACE_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.KNIGHT_SPIDER_CARAPACE_GEAR).addIngredient((Supplier)TensuraMobDropItems.KNIGHT_SPIDER_CARAPACE, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.KNIGHT_SPIDER_CARAPACE_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.KNIGHT_SPIDER_CARAPACE_GEAR).addIngredient((Supplier)TensuraMobDropItems.KNIGHT_SPIDER_CARAPACE, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraMaterialItems.MAGIC_STONE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 1).addIngredient((Supplier)TensuraMobDropItems.LOW_QUALITY_MAGIC_CRYSTAL, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraMaterialItems.ELEMENT_CORE_EMPTY).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.MAGIC_STONE, 1).addIngredient((Supplier)TensuraMobDropItems.MEDIUM_QUALITY_MAGIC_CRYSTAL, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SLIME_STAFF).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMobDropItems.SLIME_CORE, 1).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Item)TensuraMobDropItems.MEDIUM_QUALITY_MAGIC_CRYSTAL.get(), 2).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMobDropItems.HIGH_QUALITY_MAGIC_CRYSTAL, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Item)TensuraMobDropItems.LOW_QUALITY_MAGIC_CRYSTAL.get(), 2).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMobDropItems.MEDIUM_QUALITY_MAGIC_CRYSTAL, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_SCYTHE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_KATANA).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_KODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_TACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_ODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_PICKAXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_AXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_SHOVEL).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_HOE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.LOW_MAGISTEEL_SICKLE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.LOW_MAGISTEEL_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.LOW_MAGISTEEL_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.LOW_MAGISTEEL_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.LOW_MAGISTEEL_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ARMOURSAURUS_SHIELD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ARMOURSAURUS_SCALEMAIL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHIELD).addIngredient((Supplier)TensuraMobDropItems.ARMOURSAURUS_SHELL, 4).addIngredient((Supplier)TensuraMobDropItems.ARMOURSAURUS_SCALE, 3).addIngredient((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ARMOURSAURUS_SCALEMAIL_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ARMOURSAURUS_SCALEMAIL_GEAR).addIngredient((Supplier)TensuraMobDropItems.ARMOURSAURUS_SCALE, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ARMOURSAURUS_SCALEMAIL_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ARMOURSAURUS_SCALEMAIL_GEAR).addIngredient((Supplier)TensuraMobDropItems.ARMOURSAURUS_SCALE, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ARMOURSAURUS_SCALEMAIL_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ARMOURSAURUS_SCALEMAIL_GEAR).addIngredient((Supplier)TensuraMobDropItems.ARMOURSAURUS_SCALE, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ARMOURSAURUS_SCALEMAIL_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ARMOURSAURUS_SCALEMAIL_GEAR).addIngredient((Supplier)TensuraMobDropItems.ARMOURSAURUS_SCALE, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_SCYTHE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_KATANA).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_KODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_TACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_ODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_PICKAXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_AXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_SHOVEL).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_HOE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIGH_MAGISTEEL_SICKLE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.HIGH_MAGISTEEL_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.HIGH_MAGISTEEL_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.HIGH_MAGISTEEL_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.HIGH_MAGISTEEL_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.CHARYBDIS_SCALEMAIL_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.CHARYBDIS_SCALEMAIL_GEAR).addIngredient((Supplier)TensuraMobDropItems.CHARYBDIS_SCALE, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.CHARYBDIS_SCALEMAIL_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.CHARYBDIS_SCALEMAIL_GEAR).addIngredient((Supplier)TensuraMobDropItems.CHARYBDIS_SCALE, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.CHARYBDIS_SCALEMAIL_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.CHARYBDIS_SCALEMAIL_GEAR).addIngredient((Supplier)TensuraMobDropItems.CHARYBDIS_SCALE, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.CHARYBDIS_SCALEMAIL_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.CHARYBDIS_SCALEMAIL_GEAR).addIngredient((Supplier)TensuraMobDropItems.CHARYBDIS_SCALE, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_SCYTHE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_KATANA).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_KODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_TACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_ODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_PICKAXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_AXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_SHOVEL).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_HOE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.MITHRIL_SICKLE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MITHRIL_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MITHRIL_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MITHRIL_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.MITHRIL_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraConsumableItems.ENCHANTED_SILVER_APPLE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).addIngredient((Supplier)TensuraConsumableItems.SILVER_APPLE, 1).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 8).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42517_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR).addIngredient((Item)Items.f_42516_, 3).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_D, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraMaterialItems.RACE_RESET_SCROLL).requiresSchematic((Supplier)TensuraSmithingSchematicItems.MITHRIL_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_MITHRIL, 2).addIngredient((Supplier)TensuraMaterialItems.MAGIC_STONE, 1).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_A, 2).addIngredient((Item)Items.f_42516_, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraMaterialItems.SKILL_RESET_SCROLL).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 2).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 1).addIngredient((Supplier)TensuraMaterialItems.MAGIC_STONE, 1).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_A, 2).addIngredient((Item)Items.f_42516_, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraMaterialItems.CHARACTER_RESET_SCROLL).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 3).addIngredient((Supplier)TensuraMaterialItems.MAGIC_STONE, 1).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_A, 2).addIngredient((Item)Items.f_42516_, 3).build(consumer);
      SmithingBenchRecipe.Builder.of(Items.f_42437_).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).addIngredient((Item)Items.f_42436_, 1).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_SCYTHE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_KATANA).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_KODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_TACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_ODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_PICKAXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_AXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_SHOVEL).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_HOE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ORICHALCUM_SICKLE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ORICHALCUM_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ORICHALCUM_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ORICHALCUM_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ORICHALCUM_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ORICHALCUM_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ORICHALCUM, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_SCYTHE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_KATANA).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_KODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_TACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_ODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_PICKAXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_AXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_SHOVEL).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_HOE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_SICKLE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.PURE_MAGISTEEL_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.PURE_MAGISTEEL_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.PURE_MAGISTEEL_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.PURE_MAGISTEEL_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_SCYTHE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_KATANA).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_KODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_TACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_ODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_PICKAXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_AXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_SHOVEL).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_HOE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.ADAMANTITE_SICKLE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ADAMANTITE_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ADAMANTITE_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ADAMANTITE_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ADAMANTITE_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ADAMANTITE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_ADAMANTITE, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_SPEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_SCYTHE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 3).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_KATANA).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_KODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SHORT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_TACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.LONG_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_ODACHI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).requiresSchematic((Supplier)TensuraSmithingSchematicItems.GREAT_SWORD).requiresSchematic((Supplier)TensuraSmithingSchematicItems.JAPANESE_SWORD).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 4).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_PICKAXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_AXE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_SHOVEL).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 1).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_HOE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 2).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.HIHIIROKANE_SICKLE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 3).addIngredient((TagKey)net.minecraftforge.common.Tags.Items.RODS_WOODEN, 2).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.HIHIIROKANE_HELMET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.HIHIIROKANE_CHESTPLATE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.HIHIIROKANE_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.HIHIIROKANE_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR).addIngredient((TagKey)TensuraTags.Items.INGOTS_HIHIIROKANE, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ANTI_MAGIC_MASK).requiresSchematic((Supplier)TensuraSmithingSchematicItems.ANTI_MAGIC_MASK).addIngredient((Item)Items.f_42461_, 2).addIngredient((Item)Items.f_42497_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.DARK_JACKET).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DARK_SET).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_B, 8).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.DARK_LEGGINGS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DARK_SET).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_B, 7).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.DARK_BOOTS).requiresSchematic((Supplier)TensuraSmithingSchematicItems.DARK_SET).addIngredient((Supplier)TensuraMobDropItems.MONSTER_LEATHER_B, 4).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.CRAZY_PIERROT_MASK).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PIERROT_MASK).addIngredient((Item)Items.f_42461_, 2).addIngredient((Item)Items.f_42539_, 1).addIngredient((Item)Items.f_42498_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.ANGRY_PIERROT_MASK).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PIERROT_MASK).addIngredient((Item)Items.f_42461_, 2).addIngredient((Item)Items.f_42497_, 1).addIngredient((Item)Items.f_42536_, 1).addIngredient((Item)Items.f_42494_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.WONDER_PIERROT_MASK).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PIERROT_MASK).addIngredient((Item)Items.f_42461_, 2).addIngredient((Item)Items.f_42494_, 1).addIngredient((Item)Items.f_42498_, 1).addIngredient((Item)Items.f_42497_, 1).addIngredient((Item)Items.f_42489_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraArmorItems.TEARY_PIERROT_MASK).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PIERROT_MASK).addIngredient((Item)Items.f_42461_, 2).addIngredient((Item)Items.f_42494_, 1).addIngredient((Item)Items.f_42489_, 1).addIngredient((Item)Items.f_42539_, 1).addIngredient((Item)Items.f_42497_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Item)TensuraToolItems.SEVERER_BLADE.get(), 3).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPATIAL_BLADE).addIngredient((Item)Items.f_42416_, 2).addIngredient((Item)Items.f_42584_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.SPATIAL_BLADE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.SPATIAL_BLADE).addIngredient((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, 1).addIngredient((Item)Items.f_42398_, 1).addIngredient((Supplier)TensuraToolItems.SEVERER_BLADE, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Item)TensuraToolItems.STICKY_STEEL_WEB_CARTRIDGE.get()).requiresSchematic((Supplier)TensuraSmithingSchematicItems.WEB_GUN).addIngredient((Supplier)TensuraToolItems.COPPER_SHELL, 1).addIngredient((Supplier)TensuraMobDropItems.STICKY_THREAD, 4).addIngredient((Supplier)TensuraMobDropItems.STEEL_THREAD, 4).addIngredient((Item)Items.f_42403_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Item)TensuraToolItems.STICKY_WEB_CARTRIDGE.get()).requiresSchematic((Supplier)TensuraSmithingSchematicItems.WEB_GUN).addIngredient((Supplier)TensuraToolItems.COPPER_SHELL, 1).addIngredient((Supplier)TensuraMobDropItems.STICKY_THREAD, 4).addIngredient((Item)Items.f_42403_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Item)TensuraToolItems.WEB_CARTRIDGE.get()).requiresSchematic((Supplier)TensuraSmithingSchematicItems.WEB_GUN).addIngredient((Supplier)TensuraToolItems.COPPER_SHELL, 1).addIngredient((Item)Items.f_42401_, 8).addIngredient((Item)Items.f_42403_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Item)TensuraToolItems.COPPER_SHELL.get(), 4).requiresSchematic((Supplier)TensuraSmithingSchematicItems.WEB_GUN).addIngredient((Item)Items.f_151052_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.WEB_GUN).requiresSchematic((Supplier)TensuraSmithingSchematicItems.WEB_GUN).addIngredient((Item)Items.f_42416_, 2).addIngredient((Item)Items.f_42451_, 1).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.KUNAI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.KUNAI).addIngredient((Item)Items.f_42749_, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.PURE_MAGISTEEL_KUNAI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.KUNAI).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_NUGGET, 5).build(consumer);
      SmithingBenchRecipe.Builder.of((Supplier)TensuraToolItems.DRAGON_KNUCKLE).requiresSchematic((Supplier)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR).addIngredient((Supplier)TensuraMaterialItems.PURE_MAGISTEEL_INGOT, 2).addIngredient((Item)Items.f_42489_, 3).build(consumer);
   }

   private void kilnMixing(Consumer<FinishedRecipe> consumer) {
      kilnMixingLeftOres(consumer, TensuraMoltenMaterialProvider.MOLTEN_GOLD, Items.f_42587_, Items.f_42417_, Items.f_41912_);
      kilnMixingLeftOres(consumer, TensuraMoltenMaterialProvider.MOLTEN_IRON, Items.f_42749_, Items.f_42416_, Items.f_41913_);
      kilnMixingLeftOres(consumer, TensuraMoltenMaterialProvider.MOLTEN_SILVER, (Item)TensuraMaterialItems.SILVER_NUGGET.get(), (Item)TensuraMaterialItems.SILVER_INGOT.get(), ((SimpleBlock)TensuraBlocks.SILVER_BLOCK.get()).m_5456_());
      kilnMixingRight(consumer, (Item)((Item)TensuraMaterialItems.PURE_MAGISTEEL_NUGGET.get()), TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 4);
      kilnMixingRight(consumer, (Item)((Item)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get()), TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 36);
      kilnMixingRight(consumer, (Item)Items.f_42419_, TensuraMoltenMaterialProvider.MOLTEN_NETHERITE, 4);
      KilnMixingRecipe.Builder.of((Supplier)TensuraMaterialItems.LOW_MAGISTEEL_INGOT).leftInput(TensuraMoltenMaterialProvider.MOLTEN_IRON, 8).rightInput(TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 4).build(consumer);
      KilnMixingRecipe.Builder.of((Supplier)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT).leftInput(TensuraMoltenMaterialProvider.MOLTEN_IRON, 5).rightInput(TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 16).build(consumer);
      KilnMixingRecipe.Builder.of((Item)TensuraMaterialItems.MITHRIL_INGOT.get()).leftInput(TensuraMoltenMaterialProvider.MOLTEN_SILVER, 4).rightInput(TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 20).build(consumer);
      KilnMixingRecipe.Builder.of((Item)TensuraMaterialItems.ORICHALCUM_INGOT.get()).leftInput(TensuraMoltenMaterialProvider.MOLTEN_GOLD, 4).rightInput(TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 20).build(consumer);
      KilnMixingRecipe.Builder.of(Items.f_42418_).leftInput(TensuraMoltenMaterialProvider.MOLTEN_GOLD, 36).rightInput(TensuraMoltenMaterialProvider.MOLTEN_NETHERITE, 16).build(consumer);
   }

   protected static void kilnMixingLeftOres(Consumer<FinishedRecipe> consumer, ResourceLocation moltenType, Item nugget, Item ingot, Item block) {
      kilnMixingLeft(consumer, nugget.m_7968_(), moltenType, 1);
      kilnMixingLeft(consumer, ingot.m_7968_(), moltenType, 9);
      kilnMixingLeft(consumer, block.m_7968_(), moltenType, 81);
   }

   protected static void kilnMixingLeft(Consumer<FinishedRecipe> consumer, ItemStack output, ResourceLocation moltenType, int amount) {
      KilnMixingRecipe.Builder.of(output).leftInput(moltenType, amount).build(consumer);
   }

   protected static void kilnMixingRight(Consumer<FinishedRecipe> consumer, Item output, ResourceLocation moltenType, int amount) {
      kilnMixingRight(consumer, output.m_7968_(), moltenType, amount);
   }

   protected static void kilnMixingRight(Consumer<FinishedRecipe> consumer, ItemStack output, ResourceLocation moltenType, int amount) {
      KilnMixingRecipe.Builder.of(output).rightInput(moltenType, amount).build(consumer);
   }

   private void kilnMelting(Consumer<FinishedRecipe> consumer) {
      kilnMeltingOres(consumer, TensuraMoltenMaterialProvider.MOLTEN_GOLD, "gold", net.minecraftforge.common.Tags.Items.NUGGETS_GOLD, net.minecraftforge.common.Tags.Items.INGOTS_GOLD, net.minecraftforge.common.Tags.Items.RAW_MATERIALS_GOLD, net.minecraftforge.common.Tags.Items.ORES_GOLD, net.minecraftforge.common.Tags.Items.STORAGE_BLOCKS_GOLD, net.minecraftforge.common.Tags.Items.STORAGE_BLOCKS_RAW_GOLD);
      kilnMeltingGears(consumer, TensuraMoltenMaterialProvider.MOLTEN_GOLD, "gold", Items.f_42476_, Items.f_42477_, Items.f_42478_, Items.f_42479_, Items.f_42432_, Items.f_42433_, Items.f_42431_, Items.f_42434_, (ItemLike)TensuraToolItems.GOLDEN_SICKLE.get(), Items.f_42430_, (ItemLike)TensuraToolItems.GOLDEN_SHORT_SWORD.get(), (ItemLike)TensuraToolItems.GOLDEN_LONG_SWORD.get(), (ItemLike)TensuraToolItems.GOLDEN_GREAT_SWORD.get(), (ItemLike)TensuraToolItems.GOLDEN_KATANA.get(), (ItemLike)TensuraToolItems.GOLDEN_KODACHI.get(), (ItemLike)TensuraToolItems.GOLDEN_TACHI.get(), (ItemLike)TensuraToolItems.GOLDEN_ODACHI.get(), (ItemLike)TensuraToolItems.GOLDEN_SPEAR.get(), 2, 1);
      kilnMeltingOres(consumer, TensuraMoltenMaterialProvider.MOLTEN_IRON, "iron", net.minecraftforge.common.Tags.Items.NUGGETS_IRON, net.minecraftforge.common.Tags.Items.INGOTS_IRON, net.minecraftforge.common.Tags.Items.RAW_MATERIALS_IRON, net.minecraftforge.common.Tags.Items.ORES_IRON, net.minecraftforge.common.Tags.Items.STORAGE_BLOCKS_IRON, net.minecraftforge.common.Tags.Items.STORAGE_BLOCKS_RAW_IRON);
      kilnMeltingGears(consumer, TensuraMoltenMaterialProvider.MOLTEN_IRON, "iron", Items.f_42468_, Items.f_42469_, Items.f_42470_, Items.f_42471_, Items.f_42385_, Items.f_42386_, Items.f_42384_, Items.f_42387_, (ItemLike)TensuraToolItems.IRON_SICKLE.get(), Items.f_42383_, (ItemLike)TensuraToolItems.IRON_SHORT_SWORD.get(), (ItemLike)TensuraToolItems.IRON_LONG_SWORD.get(), (ItemLike)TensuraToolItems.IRON_GREAT_SWORD.get(), (ItemLike)TensuraToolItems.IRON_KATANA.get(), (ItemLike)TensuraToolItems.IRON_KODACHI.get(), (ItemLike)TensuraToolItems.IRON_TACHI.get(), (ItemLike)TensuraToolItems.IRON_ODACHI.get(), (ItemLike)TensuraToolItems.IRON_SPEAR.get(), 2, 1);
      kilnMeltingOres(consumer, TensuraMoltenMaterialProvider.MOLTEN_SILVER, "silver", TensuraTags.Items.NUGGETS_SILVER, TensuraTags.Items.INGOTS_SILVER, TensuraTags.Items.RAW_MATERIALS_SILVER, TensuraTags.Items.ORES_SILVER, TensuraTags.Items.STORAGE_BLOCKS_SILVER, TensuraTags.Items.STORAGE_BLOCKS_RAW_SILVER);
      kilnMeltingGears(consumer, TensuraMoltenMaterialProvider.MOLTEN_SILVER, "silver", (ItemLike)TensuraArmorItems.SILVER_HELMET.get(), (ItemLike)TensuraArmorItems.SILVER_CHESTPLATE.get(), (ItemLike)TensuraArmorItems.SILVER_LEGGINGS.get(), (ItemLike)TensuraArmorItems.SILVER_BOOTS.get(), (ItemLike)TensuraToolItems.SILVER_PICKAXE.get(), (ItemLike)TensuraToolItems.SILVER_AXE.get(), (ItemLike)TensuraToolItems.SILVER_SHOVEL.get(), (ItemLike)TensuraToolItems.SILVER_HOE.get(), (ItemLike)TensuraToolItems.SILVER_SICKLE.get(), (ItemLike)TensuraToolItems.SILVER_SWORD.get(), (ItemLike)TensuraToolItems.SILVER_SHORT_SWORD.get(), (ItemLike)TensuraToolItems.SILVER_LONG_SWORD.get(), (ItemLike)TensuraToolItems.SILVER_GREAT_SWORD.get(), (ItemLike)TensuraToolItems.SILVER_KATANA.get(), (ItemLike)TensuraToolItems.SILVER_KODACHI.get(), (ItemLike)TensuraToolItems.SILVER_TACHI.get(), (ItemLike)TensuraToolItems.SILVER_ODACHI.get(), (ItemLike)TensuraToolItems.SILVER_SPEAR.get(), 2, 1);
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_NETHERITE, 4, Items.f_42419_);
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_NETHERITE, 4, Items.f_42792_);
      this.kilnDoubleMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_NETHERITE, 16, TensuraMoltenMaterialProvider.MOLTEN_GOLD, 36, Items.f_42418_);
      kilnDoubleMeltingGears(consumer, TensuraMoltenMaterialProvider.MOLTEN_NETHERITE, TensuraMoltenMaterialProvider.MOLTEN_GOLD, "netherite", Items.f_42480_, Items.f_42481_, Items.f_42482_, Items.f_42483_, Items.f_42395_, Items.f_42396_, Items.f_42394_, Items.f_42397_, (ItemLike)TensuraToolItems.NETHERITE_SICKLE.get(), Items.f_42393_, (ItemLike)TensuraToolItems.NETHERITE_SHORT_SWORD.get(), (ItemLike)TensuraToolItems.NETHERITE_LONG_SWORD.get(), (ItemLike)TensuraToolItems.NETHERITE_GREAT_SWORD.get(), (ItemLike)TensuraToolItems.NETHERITE_KATANA.get(), (ItemLike)TensuraToolItems.NETHERITE_KODACHI.get(), (ItemLike)TensuraToolItems.NETHERITE_TACHI.get(), (ItemLike)TensuraToolItems.NETHERITE_ODACHI.get(), (ItemLike)TensuraToolItems.NETHERITE_SPEAR.get(), 4, 1, 8, 4);
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 1, ((SimpleBlock)TensuraBlocks.MAGIC_ORE.get()).m_5456_());
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 1, ((RotatedPillarBlock)TensuraBlocks.DEEPSLATE_MAGIC_ORE.get()).m_5456_());
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 1, (Item)TensuraMaterialItems.MAGIC_ORE.get());
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 9, ((SimpleBlock)TensuraBlocks.MAGIC_ORE_BLOCK.get()).m_5456_());
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 1, (Item)TensuraMaterialItems.HIGH_MAGISTEEL_NUGGET.get());
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 2, TensuraTags.Items.NUGGETS_MITHRIL, "mithril_nugget");
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 2, TensuraTags.Items.NUGGETS_ORICHALCUM, "orichalcum_nugget");
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 4, (Item)TensuraMaterialItems.PURE_MAGISTEEL_NUGGET.get());
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 36, (Item)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get());
      kilnMeltingGears(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, "pure_magisteel", (ItemLike)TensuraArmorItems.PURE_MAGISTEEL_HELMET.get(), (ItemLike)TensuraArmorItems.PURE_MAGISTEEL_CHESTPLATE.get(), (ItemLike)TensuraArmorItems.PURE_MAGISTEEL_LEGGINGS.get(), (ItemLike)TensuraArmorItems.PURE_MAGISTEEL_BOOTS.get(), (ItemLike)TensuraToolItems.PURE_MAGISTEEL_PICKAXE.get(), (ItemLike)TensuraToolItems.PURE_MAGISTEEL_AXE.get(), (ItemLike)TensuraToolItems.PURE_MAGISTEEL_SHOVEL.get(), (ItemLike)TensuraToolItems.PURE_MAGISTEEL_HOE.get(), (ItemLike)TensuraToolItems.PURE_MAGISTEEL_SICKLE.get(), (ItemLike)TensuraToolItems.PURE_MAGISTEEL_SWORD.get(), (ItemLike)TensuraToolItems.PURE_MAGISTEEL_SHORT_SWORD.get(), (ItemLike)TensuraToolItems.PURE_MAGISTEEL_LONG_SWORD.get(), (ItemLike)TensuraToolItems.PURE_MAGISTEEL_GREAT_SWORD.get(), (ItemLike)TensuraToolItems.PURE_MAGISTEEL_KATANA.get(), (ItemLike)TensuraToolItems.PURE_MAGISTEEL_KODACHI.get(), (ItemLike)TensuraToolItems.PURE_MAGISTEEL_TACHI.get(), (ItemLike)TensuraToolItems.PURE_MAGISTEEL_ODACHI.get(), (ItemLike)TensuraToolItems.PURE_MAGISTEEL_SPEAR.get(), 8, 4);
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 1, (Item)TensuraToolItems.KANABO.get());
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 1, (Item)TensuraToolItems.PURE_MAGISTEEL_KUNAI.get());
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 4, (Item)TensuraToolItems.DRAGON_KNUCKLE.get());
      this.kilnDoubleMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 4, TensuraMoltenMaterialProvider.MOLTEN_IRON, 8, (Item)TensuraMaterialItems.LOW_MAGISTEEL_INGOT.get());
      this.kilnDoubleMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 36, TensuraMoltenMaterialProvider.MOLTEN_IRON, 72, (Item)TensuraBlocks.Items.LOW_MAGISTEEL_BLOCK.get());
      kilnDoubleMeltingGears(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, TensuraMoltenMaterialProvider.MOLTEN_IRON, "low_magisteel", (ItemLike)TensuraArmorItems.LOW_MAGISTEEL_HELMET.get(), (ItemLike)TensuraArmorItems.LOW_MAGISTEEL_CHESTPLATE.get(), (ItemLike)TensuraArmorItems.LOW_MAGISTEEL_LEGGINGS.get(), (ItemLike)TensuraArmorItems.LOW_MAGISTEEL_BOOTS.get(), (ItemLike)TensuraToolItems.LOW_MAGISTEEL_PICKAXE.get(), (ItemLike)TensuraToolItems.LOW_MAGISTEEL_AXE.get(), (ItemLike)TensuraToolItems.LOW_MAGISTEEL_SHOVEL.get(), (ItemLike)TensuraToolItems.LOW_MAGISTEEL_HOE.get(), (ItemLike)TensuraToolItems.LOW_MAGISTEEL_SICKLE.get(), (ItemLike)TensuraToolItems.LOW_MAGISTEEL_SWORD.get(), (ItemLike)TensuraToolItems.LOW_MAGISTEEL_SHORT_SWORD.get(), (ItemLike)TensuraToolItems.LOW_MAGISTEEL_LONG_SWORD.get(), (ItemLike)TensuraToolItems.LOW_MAGISTEEL_GREAT_SWORD.get(), (ItemLike)TensuraToolItems.LOW_MAGISTEEL_KATANA.get(), (ItemLike)TensuraToolItems.LOW_MAGISTEEL_KODACHI.get(), (ItemLike)TensuraToolItems.LOW_MAGISTEEL_TACHI.get(), (ItemLike)TensuraToolItems.LOW_MAGISTEEL_ODACHI.get(), (ItemLike)TensuraToolItems.LOW_MAGISTEEL_SPEAR.get(), 1, 0, 2, 1);
      this.kilnDoubleMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 16, TensuraMoltenMaterialProvider.MOLTEN_IRON, 5, (Item)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT.get());
      kilnDoubleMeltingGears(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, TensuraMoltenMaterialProvider.MOLTEN_IRON, "high_magisteel", (ItemLike)TensuraArmorItems.HIGH_MAGISTEEL_HELMET.get(), (ItemLike)TensuraArmorItems.HIGH_MAGISTEEL_CHESTPLATE.get(), (ItemLike)TensuraArmorItems.HIGH_MAGISTEEL_LEGGINGS.get(), (ItemLike)TensuraArmorItems.HIGH_MAGISTEEL_BOOTS.get(), (ItemLike)TensuraToolItems.HIGH_MAGISTEEL_PICKAXE.get(), (ItemLike)TensuraToolItems.HIGH_MAGISTEEL_AXE.get(), (ItemLike)TensuraToolItems.HIGH_MAGISTEEL_SHOVEL.get(), (ItemLike)TensuraToolItems.HIGH_MAGISTEEL_HOE.get(), (ItemLike)TensuraToolItems.HIGH_MAGISTEEL_SICKLE.get(), (ItemLike)TensuraToolItems.HIGH_MAGISTEEL_SWORD.get(), (ItemLike)TensuraToolItems.HIGH_MAGISTEEL_SHORT_SWORD.get(), (ItemLike)TensuraToolItems.HIGH_MAGISTEEL_LONG_SWORD.get(), (ItemLike)TensuraToolItems.HIGH_MAGISTEEL_GREAT_SWORD.get(), (ItemLike)TensuraToolItems.HIGH_MAGISTEEL_KATANA.get(), (ItemLike)TensuraToolItems.HIGH_MAGISTEEL_KODACHI.get(), (ItemLike)TensuraToolItems.HIGH_MAGISTEEL_TACHI.get(), (ItemLike)TensuraToolItems.HIGH_MAGISTEEL_ODACHI.get(), (ItemLike)TensuraToolItems.HIGH_MAGISTEEL_SPEAR.get(), 4, 2, 1, 0);
      this.kilnDoubleMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 20, TensuraMoltenMaterialProvider.MOLTEN_SILVER, 4, TensuraTags.Items.INGOTS_MITHRIL, "mithril_ingot");
      kilnMeltingGears(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, "mithril", (ItemLike)TensuraArmorItems.MITHRIL_HELMET.get(), (ItemLike)TensuraArmorItems.MITHRIL_CHESTPLATE.get(), (ItemLike)TensuraArmorItems.MITHRIL_LEGGINGS.get(), (ItemLike)TensuraArmorItems.MITHRIL_BOOTS.get(), (ItemLike)TensuraToolItems.MITHRIL_PICKAXE.get(), (ItemLike)TensuraToolItems.MITHRIL_AXE.get(), (ItemLike)TensuraToolItems.MITHRIL_SHOVEL.get(), (ItemLike)TensuraToolItems.MITHRIL_HOE.get(), (ItemLike)TensuraToolItems.MITHRIL_SICKLE.get(), (ItemLike)TensuraToolItems.MITHRIL_SWORD.get(), (ItemLike)TensuraToolItems.MITHRIL_SHORT_SWORD.get(), (ItemLike)TensuraToolItems.MITHRIL_LONG_SWORD.get(), (ItemLike)TensuraToolItems.MITHRIL_GREAT_SWORD.get(), (ItemLike)TensuraToolItems.MITHRIL_KATANA.get(), (ItemLike)TensuraToolItems.MITHRIL_KODACHI.get(), (ItemLike)TensuraToolItems.MITHRIL_TACHI.get(), (ItemLike)TensuraToolItems.MITHRIL_ODACHI.get(), (ItemLike)TensuraToolItems.MITHRIL_SPEAR.get(), 4, 2);
      this.kilnDoubleMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 20, TensuraMoltenMaterialProvider.MOLTEN_GOLD, 4, TensuraTags.Items.INGOTS_ORICHALCUM, "orichalcum_ingot");
      kilnMeltingGears(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, "orichalcum", (ItemLike)TensuraArmorItems.ORICHALCUM_HELMET.get(), (ItemLike)TensuraArmorItems.ORICHALCUM_CHESTPLATE.get(), (ItemLike)TensuraArmorItems.ORICHALCUM_LEGGINGS.get(), (ItemLike)TensuraArmorItems.ORICHALCUM_BOOTS.get(), (ItemLike)TensuraToolItems.ORICHALCUM_PICKAXE.get(), (ItemLike)TensuraToolItems.ORICHALCUM_AXE.get(), (ItemLike)TensuraToolItems.ORICHALCUM_SHOVEL.get(), (ItemLike)TensuraToolItems.ORICHALCUM_HOE.get(), (ItemLike)TensuraToolItems.ORICHALCUM_SICKLE.get(), (ItemLike)TensuraToolItems.ORICHALCUM_SWORD.get(), (ItemLike)TensuraToolItems.ORICHALCUM_SHORT_SWORD.get(), (ItemLike)TensuraToolItems.ORICHALCUM_LONG_SWORD.get(), (ItemLike)TensuraToolItems.ORICHALCUM_GREAT_SWORD.get(), (ItemLike)TensuraToolItems.ORICHALCUM_KATANA.get(), (ItemLike)TensuraToolItems.ORICHALCUM_KODACHI.get(), (ItemLike)TensuraToolItems.ORICHALCUM_TACHI.get(), (ItemLike)TensuraToolItems.ORICHALCUM_ODACHI.get(), (ItemLike)TensuraToolItems.ORICHALCUM_SPEAR.get(), 4, 2);
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 8, (Item)TensuraMaterialItems.ADAMANTITE_NUGGET.get());
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 36, (Item)TensuraMaterialItems.ADAMANTITE_INGOT.get());
      kilnMeltingGears(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, "adamantite", (ItemLike)TensuraArmorItems.ADAMANTITE_HELMET.get(), (ItemLike)TensuraArmorItems.ADAMANTITE_CHESTPLATE.get(), (ItemLike)TensuraArmorItems.ADAMANTITE_LEGGINGS.get(), (ItemLike)TensuraArmorItems.ADAMANTITE_BOOTS.get(), (ItemLike)TensuraToolItems.ADAMANTITE_PICKAXE.get(), (ItemLike)TensuraToolItems.ADAMANTITE_AXE.get(), (ItemLike)TensuraToolItems.ADAMANTITE_SHOVEL.get(), (ItemLike)TensuraToolItems.ADAMANTITE_HOE.get(), (ItemLike)TensuraToolItems.ADAMANTITE_SICKLE.get(), (ItemLike)TensuraToolItems.ADAMANTITE_SWORD.get(), (ItemLike)TensuraToolItems.ADAMANTITE_SHORT_SWORD.get(), (ItemLike)TensuraToolItems.ADAMANTITE_LONG_SWORD.get(), (ItemLike)TensuraToolItems.ADAMANTITE_GREAT_SWORD.get(), (ItemLike)TensuraToolItems.ADAMANTITE_KATANA.get(), (ItemLike)TensuraToolItems.ADAMANTITE_KODACHI.get(), (ItemLike)TensuraToolItems.ADAMANTITE_TACHI.get(), (ItemLike)TensuraToolItems.ADAMANTITE_ODACHI.get(), (ItemLike)TensuraToolItems.ADAMANTITE_SPEAR.get(), 16, 8);
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 12, (Item)TensuraMaterialItems.HIHIIROKANE_NUGGET.get());
      this.kilnMelting(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, 54, (Item)TensuraMaterialItems.HIHIIROKANE_INGOT.get());
      kilnMeltingGears(consumer, TensuraMoltenMaterialProvider.MOLTEN_MAGISTEEL, "hihiirokane", (ItemLike)TensuraArmorItems.HIHIIROKANE_HELMET.get(), (ItemLike)TensuraArmorItems.HIHIIROKANE_CHESTPLATE.get(), (ItemLike)TensuraArmorItems.HIHIIROKANE_LEGGINGS.get(), (ItemLike)TensuraArmorItems.HIHIIROKANE_BOOTS.get(), (ItemLike)TensuraToolItems.HIHIIROKANE_PICKAXE.get(), (ItemLike)TensuraToolItems.HIHIIROKANE_AXE.get(), (ItemLike)TensuraToolItems.HIHIIROKANE_SHOVEL.get(), (ItemLike)TensuraToolItems.HIHIIROKANE_HOE.get(), (ItemLike)TensuraToolItems.HIHIIROKANE_SICKLE.get(), (ItemLike)TensuraToolItems.HIHIIROKANE_SWORD.get(), (ItemLike)TensuraToolItems.HIHIIROKANE_SHORT_SWORD.get(), (ItemLike)TensuraToolItems.HIHIIROKANE_LONG_SWORD.get(), (ItemLike)TensuraToolItems.HIHIIROKANE_GREAT_SWORD.get(), (ItemLike)TensuraToolItems.HIHIIROKANE_KATANA.get(), (ItemLike)TensuraToolItems.HIHIIROKANE_KODACHI.get(), (ItemLike)TensuraToolItems.HIHIIROKANE_TACHI.get(), (ItemLike)TensuraToolItems.HIHIIROKANE_ODACHI.get(), (ItemLike)TensuraToolItems.HIHIIROKANE_SPEAR.get(), 24, 12);
   }

   protected void kilnMelting(Consumer<FinishedRecipe> consumer, ResourceLocation moltenType, int amount, TagKey<Item> input, String path) {
      KilnMeltingRecipe.Builder.of(moltenType, amount).requires(Ingredient.m_204132_(input)).build(consumer, path);
   }

   protected void kilnMelting(Consumer<FinishedRecipe> consumer, ResourceLocation moltenType, int amount, Item input) {
      KilnMeltingRecipe.Builder.of(moltenType, amount).requires(Ingredient.m_43929_(new ItemLike[]{input})).build(consumer, this.rl(input).m_135815_());
   }

   protected static void kilnMeltingOres(Consumer<FinishedRecipe> consumer, ResourceLocation moltenType, String filePrefix, TagKey<Item> nugget, TagKey<Item> ingot, TagKey<Item> raw, TagKey<Item> ore, TagKey<Item> block, TagKey<Item> rawBlock) {
      KilnMeltingRecipe.Builder.of(moltenType, 1).requires(Ingredient.m_204132_(nugget)).build(consumer, filePrefix + "_nuggets");
      KilnMeltingRecipe.Builder.of(moltenType, 9).requires(Ingredient.m_204132_(ingot)).build(consumer, filePrefix + "_ingots");
      KilnMeltingRecipe.Builder.of(moltenType, 9).requires(Ingredient.m_204132_(raw)).build(consumer, filePrefix + "_raw");
      KilnMeltingRecipe.Builder.of(moltenType, 9).requires(Ingredient.m_204132_(ore)).build(consumer, filePrefix + "_ores");
      KilnMeltingRecipe.Builder.of(moltenType, 81).requires(Ingredient.m_204132_(block)).build(consumer, filePrefix + "_blocks");
      KilnMeltingRecipe.Builder.of(moltenType, 81).requires(Ingredient.m_204132_(rawBlock)).build(consumer, filePrefix + "_raw_blocks");
   }

   protected static void kilnMeltingGears(Consumer<FinishedRecipe> consumer, ResourceLocation moltenType, String filePrefix, ItemLike helmet, ItemLike chestplate, ItemLike leggings, ItemLike boots, ItemLike pickaxe, ItemLike axe, ItemLike shovel, ItemLike hoe, ItemLike sickle, ItemLike sword, ItemLike shortSword, ItemLike longSword, ItemLike greatSword, ItemLike katana, ItemLike kodachi, ItemLike tachi, ItemLike odachi, ItemLike spear, int moltenArmor, int moltenTools) {
      KilnMeltingRecipe.Builder.of(moltenType, moltenArmor).requires(Ingredient.m_43929_(new ItemLike[]{helmet})).build(consumer, filePrefix + "_helmet");
      KilnMeltingRecipe.Builder.of(moltenType, moltenArmor).requires(Ingredient.m_43929_(new ItemLike[]{chestplate})).build(consumer, filePrefix + "_chestplate");
      KilnMeltingRecipe.Builder.of(moltenType, moltenArmor).requires(Ingredient.m_43929_(new ItemLike[]{leggings})).build(consumer, filePrefix + "_leggings");
      KilnMeltingRecipe.Builder.of(moltenType, moltenArmor).requires(Ingredient.m_43929_(new ItemLike[]{boots})).build(consumer, filePrefix + "_boots");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{pickaxe})).build(consumer, filePrefix + "_pickaxe");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{axe})).build(consumer, filePrefix + "_axe");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{shovel})).build(consumer, filePrefix + "_shovel");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{hoe})).build(consumer, filePrefix + "_hoe");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{sickle})).build(consumer, filePrefix + "_sickle");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{sword})).build(consumer, filePrefix + "_sword");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{shortSword})).build(consumer, filePrefix + "_short_sword");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{longSword})).build(consumer, filePrefix + "_long_sword");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{greatSword})).build(consumer, filePrefix + "_great_sword");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{katana})).build(consumer, filePrefix + "_katana");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{kodachi})).build(consumer, filePrefix + "_kodachi");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{tachi})).build(consumer, filePrefix + "_tachi");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{odachi})).build(consumer, filePrefix + "_odachi");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{spear})).build(consumer, filePrefix + "_spear");
   }

   protected void kilnDoubleMelting(Consumer<FinishedRecipe> consumer, ResourceLocation moltenType, int amount, ResourceLocation secondaryType, int secondaryAmount, Item input) {
      KilnMeltingRecipe.Builder.of(moltenType, amount).requires(Ingredient.m_43929_(new ItemLike[]{input})).inputSecondary(secondaryType, secondaryAmount).build(consumer, this.rl(input).m_135815_());
   }

   protected void kilnDoubleMelting(Consumer<FinishedRecipe> consumer, ResourceLocation moltenType, int amount, ResourceLocation secondaryType, int secondaryAmount, TagKey<Item> input, String path) {
      KilnMeltingRecipe.Builder.of(moltenType, amount).requires(Ingredient.m_204132_(input)).inputSecondary(secondaryType, secondaryAmount).build(consumer, path);
   }

   protected static void kilnDoubleMeltingGears(Consumer<FinishedRecipe> consumer, ResourceLocation moltenType, ResourceLocation secondType, String filePrefix, ItemLike helmet, ItemLike chestplate, ItemLike leggings, ItemLike boots, ItemLike pickaxe, ItemLike axe, ItemLike shovel, ItemLike hoe, ItemLike sickle, ItemLike sword, ItemLike shortSword, ItemLike longSword, ItemLike greatSword, ItemLike katana, ItemLike kodachi, ItemLike tachi, ItemLike odachi, ItemLike spear, int moltenArmor, int moltenTools, int secondMoltenArmor, int secondMoltenTools) {
      KilnMeltingRecipe.Builder.of(moltenType, moltenArmor).requires(Ingredient.m_43929_(new ItemLike[]{helmet})).inputSecondary(secondType, secondMoltenArmor).build(consumer, filePrefix + "_helmet");
      KilnMeltingRecipe.Builder.of(moltenType, moltenArmor).requires(Ingredient.m_43929_(new ItemLike[]{chestplate})).inputSecondary(secondType, secondMoltenArmor).build(consumer, filePrefix + "_chestplate");
      KilnMeltingRecipe.Builder.of(moltenType, moltenArmor).requires(Ingredient.m_43929_(new ItemLike[]{leggings})).inputSecondary(secondType, secondMoltenArmor).build(consumer, filePrefix + "_leggings");
      KilnMeltingRecipe.Builder.of(moltenType, moltenArmor).requires(Ingredient.m_43929_(new ItemLike[]{boots})).inputSecondary(secondType, secondMoltenArmor).build(consumer, filePrefix + "_boots");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{pickaxe})).inputSecondary(secondType, secondMoltenTools).build(consumer, filePrefix + "_pickaxe");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{axe})).inputSecondary(secondType, secondMoltenTools).build(consumer, filePrefix + "_axe");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{shovel})).inputSecondary(secondType, secondMoltenTools).build(consumer, filePrefix + "_shovel");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{hoe})).inputSecondary(secondType, secondMoltenTools).build(consumer, filePrefix + "_hoe");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{sickle})).inputSecondary(secondType, secondMoltenTools).build(consumer, filePrefix + "_sickle");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{sword})).inputSecondary(secondType, secondMoltenTools).build(consumer, filePrefix + "_sword");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{shortSword})).inputSecondary(secondType, secondMoltenTools).build(consumer, filePrefix + "_short_sword");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{longSword})).inputSecondary(secondType, secondMoltenTools).build(consumer, filePrefix + "_long_sword");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{greatSword})).inputSecondary(secondType, secondMoltenTools).build(consumer, filePrefix + "_great_sword");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{katana})).inputSecondary(secondType, secondMoltenTools).build(consumer, filePrefix + "_katana");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{kodachi})).inputSecondary(secondType, secondMoltenTools).build(consumer, filePrefix + "_kodachi");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{tachi})).inputSecondary(secondType, secondMoltenTools).build(consumer, filePrefix + "_tachi");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{odachi})).inputSecondary(secondType, secondMoltenTools).build(consumer, filePrefix + "_odachi");
      KilnMeltingRecipe.Builder.of(moltenType, moltenTools).requires(Ingredient.m_43929_(new ItemLike[]{spear})).inputSecondary(secondType, secondMoltenTools).build(consumer, filePrefix + "_spear");
   }

   protected void sickle(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike material, ItemLike sickle) {
      this.sickle(finishedRecipeConsumer, Ingredient.m_43929_(new ItemLike[]{material}), sickle);
   }

   protected void sickle(Consumer<FinishedRecipe> finishedRecipeConsumer, TagKey<Item> material, ItemLike sickle) {
      this.sickle(finishedRecipeConsumer, Ingredient.m_204132_(material), sickle);
   }

   protected void sickle(Consumer<FinishedRecipe> finishedRecipeConsumer, Ingredient material, ItemLike sickle) {
      this.sickle(finishedRecipeConsumer, material, Ingredient.m_204132_(net.minecraftforge.common.Tags.Items.RODS_WOODEN), sickle);
   }

   protected void sickle(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike material, ItemLike stick, ItemLike sickle) {
      this.sickle(finishedRecipeConsumer, Ingredient.m_43929_(new ItemLike[]{material}), Ingredient.m_43929_(new ItemLike[]{stick}), sickle);
   }

   protected void sickle(Consumer<FinishedRecipe> finishedRecipeConsumer, TagKey<Item> material, TagKey<Item> stick, ItemLike sickle) {
      this.sickle(finishedRecipeConsumer, Ingredient.m_204132_(material), Ingredient.m_204132_(stick), sickle);
   }

   protected void sickle(Consumer<FinishedRecipe> finishedRecipeConsumer, Ingredient material, Ingredient stick, ItemLike sickle) {
      CriterionTriggerInstance unlockCriterion = m_126011_(new ItemPredicate[]{Builder.m_45068_().m_151445_((ItemLike[])Arrays.stream(material.m_43908_()).map(ItemStack::m_41720_).toArray((x$0) -> {
         return new ItemLike[x$0];
      })).m_45077_()});
      ShapedRecipeBuilder var10000 = ShapedRecipeBuilder.m_126116_(sickle).m_126130_("XXX").m_126130_("  S").m_126130_("  S").m_126124_('X', material).m_126124_('S', stick);
      String var10001 = this.rl(sickle).toString();
      var10000.m_126132_("has_material_for_" + var10001.replace(':', '_'), unlockCriterion).m_176498_(finishedRecipeConsumer);
   }

   protected void door(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike material, ItemLike door) {
      ShapedRecipeBuilder.m_126118_(door, 3).m_126127_('#', material).m_126130_("##").m_126130_("##").m_126130_("##").m_126132_(m_176602_(material), m_125977_(material)).m_176498_(finishedRecipeConsumer);
   }

   protected void trapdoor(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike material, ItemLike trapdoor) {
      ShapedRecipeBuilder.m_126118_(trapdoor, 2).m_126127_('#', material).m_126130_("###").m_126130_("###").m_126132_(m_176602_(material), m_125977_(material)).m_176498_(finishedRecipeConsumer);
   }

   protected void button(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike material, ItemLike button) {
      ShapelessRecipeBuilder.m_126189_(button).m_126209_(material).m_126132_(m_176602_(material), m_125977_(material)).m_176498_(finishedRecipeConsumer);
   }

   protected void fence(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike material, ItemLike fence) {
      ShapedRecipeBuilder.m_126118_(fence, 3).m_126127_('W', material).m_206416_('#', net.minecraftforge.common.Tags.Items.RODS_WOODEN).m_126130_("W#W").m_126130_("W#W").m_126132_(m_176602_(material), m_125977_(material)).m_176498_(finishedRecipeConsumer);
   }

   protected void fenceGate(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike material, ItemLike fenceGate) {
      ShapedRecipeBuilder.m_126116_(fenceGate).m_126127_('W', material).m_206416_('#', net.minecraftforge.common.Tags.Items.RODS_WOODEN).m_126130_("#W#").m_126130_("#W#").m_126132_(m_176602_(material), m_125977_(material)).m_176498_(finishedRecipeConsumer);
   }

   protected void sign(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike material, ItemLike sign) {
      ShapedRecipeBuilder.m_126118_(sign, 3).m_126145_("sign").m_126127_('#', material).m_206416_('X', net.minecraftforge.common.Tags.Items.RODS_WOODEN).m_126130_("###").m_126130_("###").m_126130_(" X ").m_126132_(m_176602_(material), m_125977_(material)).m_176498_(finishedRecipeConsumer);
   }

   protected void chestboat(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike boat, ItemLike chestBoat) {
      ShapelessRecipeBuilder.m_126189_(chestBoat).m_126209_(Blocks.f_50087_).m_126209_(boat).m_126145_("chest_boat").m_126132_("has_boat", m_206406_(ItemTags.f_13155_)).m_176498_(finishedRecipeConsumer);
   }

   protected void bricks(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike material, ItemLike bricks) {
      ShapedRecipeBuilder.m_126118_(bricks, 4).m_126127_('#', material).m_126130_("##").m_126130_("##").m_126132_(m_176602_(material), m_125977_(material)).m_176498_(finishedRecipeConsumer);
   }

   protected void chiseledBricks(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike material, ItemLike bricks) {
      ShapedRecipeBuilder.m_126118_(bricks, 1).m_126127_('#', material).m_126130_("#").m_126130_("#").m_126132_(m_176602_(material), m_125977_(material)).m_176498_(finishedRecipeConsumer);
   }

   protected void magicBottles(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike bottle, int count, ItemLike glass, ItemLike crystal) {
      String unlock = count <= 3 ? "low_crystal" : (count <= 6 ? "medium_crystal" : "high_crystal");
      ShapedRecipeBuilder.m_126118_(bottle, count).m_126127_('#', glass).m_126127_('X', crystal).m_126130_("#X#").m_126130_(" # ").m_126145_("magic_bottles").m_126132_("has_" + unlock, m_125977_(crystal)).m_176500_(finishedRecipeConsumer, "bottles_of_" + unlock);
   }

   protected void enchantedApple(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike core, ItemLike material, ItemLike apple) {
      ShapedRecipeBuilder.m_126116_(core).m_126127_('#', material).m_126127_('X', apple).m_126130_("###").m_126130_("#X#").m_126130_("###").m_126132_("has_material", m_125977_(apple)).m_176500_(finishedRecipeConsumer, apple + "_from_" + material);
   }

   protected void enchantedApple(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike core, TagKey<Item> material, ItemLike apple, String string) {
      ShapedRecipeBuilder.m_126116_(core).m_206416_('#', material).m_126127_('X', apple).m_126130_("###").m_126130_("#X#").m_126130_("###").m_126132_("has_material", m_125977_(apple)).m_176500_(finishedRecipeConsumer, apple + "_from_" + string);
   }

   protected void elementalCores(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike core, ItemLike shard, ItemLike emptyCore) {
      ShapedRecipeBuilder.m_126116_(core).m_126127_('#', shard).m_126127_('X', emptyCore).m_126130_("###").m_126130_("#X#").m_126130_("###").m_126132_("has_empty_core", m_125977_((ItemLike)TensuraMaterialItems.ELEMENT_CORE_EMPTY.get())).m_176498_(finishedRecipeConsumer);
   }

   protected void raceResetScroll(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike scroll, ItemLike paper, TagKey<Item> mithril, ItemLike stone, ItemLike leather) {
      ShapedRecipeBuilder.m_126116_(scroll).m_126127_('P', paper).m_206416_('M', mithril).m_126127_('S', stone).m_126127_('L', leather).m_126130_("PMP").m_126130_("LSL").m_126130_("PMP").m_126132_("has_mithril", m_125977_((ItemLike)TensuraMaterialItems.MITHRIL_INGOT.get())).m_176498_(finishedRecipeConsumer);
   }

   protected void skillResetScroll(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike scroll, ItemLike paper, ItemLike pure, TagKey<Item> orichalcum, ItemLike stone, ItemLike leather) {
      ShapedRecipeBuilder.m_126116_(scroll).m_126127_('P', paper).m_126127_('#', pure).m_206416_('O', orichalcum).m_126127_('S', stone).m_126127_('L', leather).m_126130_("P#P").m_126130_("OSO").m_126130_("LPL").m_126132_("has_orichalcum", m_125977_((ItemLike)TensuraMaterialItems.ORICHALCUM_INGOT.get())).m_176498_(finishedRecipeConsumer);
   }

   protected void skillResetScroll(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike scroll, ItemLike paper, ItemLike pure, ItemLike orichalcum, ItemLike stone, ItemLike leather) {
      ShapedRecipeBuilder.m_126116_(scroll).m_126127_('P', paper).m_126127_('#', pure).m_126127_('O', orichalcum).m_126127_('S', stone).m_126127_('L', leather).m_126130_("P#P").m_126130_("OSO").m_126130_("LPL").m_126132_("has_orichalcum", m_125977_((ItemLike)TensuraMaterialItems.ORICHALCUM_INGOT.get())).m_176498_(finishedRecipeConsumer);
   }

   protected void magicEngine(Consumer<FinishedRecipe> finishedRecipeConsumer, ItemLike engine, ItemLike crystal, ItemLike pure, ItemLike stone) {
      ShapedRecipeBuilder.m_126116_(engine).m_126127_('C', crystal).m_126127_('P', pure).m_126127_('S', stone).m_126130_("SCS").m_126130_("SPS").m_126130_("SSS").m_126132_("has_high_crystal", m_125977_((ItemLike)TensuraMobDropItems.HIGH_QUALITY_MAGIC_CRYSTAL.get())).m_176498_(finishedRecipeConsumer);
   }

   protected void smeltBlastRecipes(Consumer<FinishedRecipe> pFinishedRecipeConsumer, Ingredient ingredient, ItemLike result, float exp, int smeltingTicks) {
      ItemStack[] items = ingredient.m_43908_();
      ItemStack[] var7 = items;
      int var8 = items.length;

      for(int var9 = 0; var9 < var8; ++var9) {
         ItemStack itemStack = var7[var9];
         SimpleCookingRecipeBuilder var10000 = SimpleCookingRecipeBuilder.m_126272_(ingredient, result, exp, smeltingTicks).m_126132_("has_" + this.getHasName(itemStack), m_125977_(itemStack.m_41720_()));
         String var10002 = m_176656_(result);
         var10000.m_176500_(pFinishedRecipeConsumer, var10002 + "_" + m_176632_(itemStack.m_41720_()));
         var10000 = SimpleCookingRecipeBuilder.m_126267_(ingredient, result, exp, smeltingTicks / 2).m_126132_("has_" + this.getHasName(itemStack), m_125977_(itemStack.m_41720_()));
         var10002 = m_176668_(result);
         var10000.m_176500_(pFinishedRecipeConsumer, var10002 + "_" + m_176632_(itemStack.m_41720_()));
      }

   }

   protected void potionRefiningRecipes(Consumer<FinishedRecipe> consumer, Potion potion, @Nullable Potion longPotion, @Nullable Potion strongPotion, Item... toAdd) {
      if (toAdd.length < 5) {
         GreatSageRefiningRecipe.Builder.of(potion).addInput(Potions.f_43599_).addIngredient(toAdd).addIngredient(Items.f_42588_).build(consumer);
         if (toAdd.length < 4) {
            if (longPotion != null) {
               GreatSageRefiningRecipe.Builder.of(longPotion).addInput(Potions.f_43599_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42525_).build(consumer);
            }

            if (strongPotion != null) {
               GreatSageRefiningRecipe.Builder.of(strongPotion).addInput(Potions.f_43599_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42451_).build(consumer);
            }

            GreatSageRefiningRecipe.Builder.of(potion, Items.f_42736_).addInput(Potions.f_43599_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42403_).build(consumer);
            if (toAdd.length < 3) {
               if (longPotion != null) {
                  GreatSageRefiningRecipe.Builder.of(longPotion, Items.f_42736_).addInput(Potions.f_43599_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42525_).addIngredient(Items.f_42403_).build(consumer);
               }

               if (strongPotion != null) {
                  GreatSageRefiningRecipe.Builder.of(strongPotion, Items.f_42736_).addInput(Potions.f_43599_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42451_).addIngredient(Items.f_42403_).build(consumer);
               }

               GreatSageRefiningRecipe.Builder.of(potion, Items.f_42739_).addInput(Potions.f_43599_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42403_).addIngredient(Items.f_42735_).build(consumer);
               if (toAdd.length < 2) {
                  if (longPotion != null) {
                     GreatSageRefiningRecipe.Builder.of(longPotion, Items.f_42739_).addInput(Potions.f_43599_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42525_).addIngredient(Items.f_42403_).addIngredient(Items.f_42735_).build(consumer);
                  }

                  if (strongPotion != null) {
                     GreatSageRefiningRecipe.Builder.of(strongPotion, Items.f_42739_).addInput(Potions.f_43599_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42451_).addIngredient(Items.f_42403_).addIngredient(Items.f_42735_).build(consumer);
                  }
               }
            }
         }
      }

      GreatSageRefiningRecipe.Builder.of(potion).addInput(Potions.f_43602_).addIngredient(toAdd).build(consumer);
      if (toAdd.length < 5) {
         if (longPotion != null) {
            GreatSageRefiningRecipe.Builder.of(longPotion).addInput(Potions.f_43602_).addIngredient(toAdd).addIngredient(Items.f_42525_).build(consumer);
         }

         if (strongPotion != null) {
            GreatSageRefiningRecipe.Builder.of(strongPotion).addInput(Potions.f_43602_).addIngredient(toAdd).addIngredient(Items.f_42451_).build(consumer);
         }

         GreatSageRefiningRecipe.Builder.of(potion, Items.f_42736_).addInput(Potions.f_43602_).addIngredient(toAdd).addIngredient(Items.f_42403_).build(consumer);
         if (toAdd.length < 4) {
            if (longPotion != null) {
               GreatSageRefiningRecipe.Builder.of(longPotion, Items.f_42736_).addInput(Potions.f_43602_).addIngredient(toAdd).addIngredient(Items.f_42525_).addIngredient(Items.f_42403_).build(consumer);
            }

            if (strongPotion != null) {
               GreatSageRefiningRecipe.Builder.of(strongPotion, Items.f_42736_).addInput(Potions.f_43602_).addIngredient(toAdd).addIngredient(Items.f_42451_).addIngredient(Items.f_42403_).build(consumer);
            }

            GreatSageRefiningRecipe.Builder.of(potion, Items.f_42739_).addInput(Potions.f_43602_).addIngredient(toAdd).addIngredient(Items.f_42403_).addIngredient(Items.f_42735_).build(consumer);
            if (toAdd.length < 3) {
               if (longPotion != null) {
                  GreatSageRefiningRecipe.Builder.of(longPotion, Items.f_42739_).addInput(Potions.f_43602_).addIngredient(toAdd).addIngredient(Items.f_42525_).addIngredient(Items.f_42403_).addIngredient(Items.f_42735_).build(consumer);
               }

               if (strongPotion != null) {
                  GreatSageRefiningRecipe.Builder.of(strongPotion, Items.f_42739_).addInput(Potions.f_43602_).addIngredient(toAdd).addIngredient(Items.f_42451_).addIngredient(Items.f_42403_).addIngredient(Items.f_42735_).build(consumer);
               }
            }
         }
      }

      if (toAdd.length < 5) {
         GreatSageRefiningRecipe.Builder.of(potion, Items.f_42736_).addInput(Potions.f_43599_, Items.f_42736_).addIngredient(toAdd).addIngredient(Items.f_42588_).build(consumer);
         if (toAdd.length < 4) {
            if (longPotion != null) {
               GreatSageRefiningRecipe.Builder.of(longPotion, Items.f_42736_).addInput(Potions.f_43599_, Items.f_42736_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42525_).build(consumer);
            }

            if (strongPotion != null) {
               GreatSageRefiningRecipe.Builder.of(strongPotion, Items.f_42736_).addInput(Potions.f_43599_, Items.f_42736_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42451_).build(consumer);
            }

            GreatSageRefiningRecipe.Builder.of(potion, Items.f_42739_).addInput(Potions.f_43599_, Items.f_42736_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42735_).build(consumer);
            if (toAdd.length < 3) {
               if (longPotion != null) {
                  GreatSageRefiningRecipe.Builder.of(longPotion, Items.f_42739_).addInput(Potions.f_43599_, Items.f_42736_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42525_).addIngredient(Items.f_42735_).build(consumer);
               }

               if (strongPotion != null) {
                  GreatSageRefiningRecipe.Builder.of(strongPotion, Items.f_42739_).addInput(Potions.f_43599_, Items.f_42736_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42451_).addIngredient(Items.f_42735_).build(consumer);
               }
            }
         }
      }

      GreatSageRefiningRecipe.Builder.of(potion, Items.f_42736_).addInput(Potions.f_43602_, Items.f_42736_).addIngredient(toAdd).build(consumer);
      if (toAdd.length < 5) {
         if (longPotion != null) {
            GreatSageRefiningRecipe.Builder.of(longPotion, Items.f_42736_).addInput(Potions.f_43602_, Items.f_42736_).addIngredient(toAdd).addIngredient(Items.f_42525_).build(consumer);
         }

         if (strongPotion != null) {
            GreatSageRefiningRecipe.Builder.of(strongPotion, Items.f_42736_).addInput(Potions.f_43602_, Items.f_42736_).addIngredient(toAdd).addIngredient(Items.f_42451_).build(consumer);
         }

         GreatSageRefiningRecipe.Builder.of(potion, Items.f_42739_).addInput(Potions.f_43602_, Items.f_42736_).addIngredient(toAdd).addIngredient(Items.f_42735_).build(consumer);
         if (toAdd.length < 4) {
            if (longPotion != null) {
               GreatSageRefiningRecipe.Builder.of(longPotion, Items.f_42739_).addInput(Potions.f_43602_, Items.f_42736_).addIngredient(toAdd).addIngredient(Items.f_42525_).addIngredient(Items.f_42735_).build(consumer);
            }

            if (strongPotion != null) {
               GreatSageRefiningRecipe.Builder.of(strongPotion, Items.f_42739_).addInput(Potions.f_43602_, Items.f_42736_).addIngredient(toAdd).addIngredient(Items.f_42451_).addIngredient(Items.f_42735_).build(consumer);
            }
         }
      }

      if (toAdd.length < 5) {
         GreatSageRefiningRecipe.Builder.of(potion, Items.f_42739_).addInput(Potions.f_43599_, Items.f_42739_).addIngredient(toAdd).addIngredient(Items.f_42588_).build(consumer);
         if (toAdd.length < 4) {
            if (longPotion != null) {
               GreatSageRefiningRecipe.Builder.of(longPotion, Items.f_42739_).addInput(Potions.f_43599_, Items.f_42739_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42525_).build(consumer);
            }

            if (strongPotion != null) {
               GreatSageRefiningRecipe.Builder.of(strongPotion, Items.f_42739_).addInput(Potions.f_43599_, Items.f_42739_).addIngredient(toAdd).addIngredient(Items.f_42588_).addIngredient(Items.f_42451_).build(consumer);
            }
         }
      }

      GreatSageRefiningRecipe.Builder.of(potion, Items.f_42739_).addInput(Potions.f_43602_, Items.f_42739_).addIngredient(toAdd).build(consumer);
      if (toAdd.length < 5) {
         if (longPotion != null) {
            GreatSageRefiningRecipe.Builder.of(longPotion, Items.f_42739_).addInput(Potions.f_43602_, Items.f_42739_).addIngredient(toAdd).addIngredient(Items.f_42525_).build(consumer);
         }

         if (strongPotion != null) {
            GreatSageRefiningRecipe.Builder.of(strongPotion, Items.f_42739_).addInput(Potions.f_43602_, Items.f_42739_).addIngredient(toAdd).addIngredient(Items.f_42451_).build(consumer);
         }
      }

   }
}
