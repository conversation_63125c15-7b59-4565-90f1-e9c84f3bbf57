package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.network.PacketDistributor;

public class WeatherDominationSkill extends Skill {
   public WeatherDominationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (!SkillUtils.isSkillMastered(entity, (ManasSkill)ExtraSkills.WEATHER_MANIPULATION.get())) {
         return false;
      } else {
         return newEP > 400000.0D;
      }
   }

   public double learningCost() {
      return 500.0D;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 3 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 3 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.weather_manipulation.clear");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.weather_manipulation.rain");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.weather_manipulation.thunder");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 500.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance instanceof TensuraSkillInstance) {
         TensuraSkillInstance skillInstance = (TensuraSkillInstance)instance;
         Level var5 = entity.m_9236_();
         if (var5 instanceof ServerLevel) {
            ServerLevel level = (ServerLevel)var5;
            if (entity.f_19853_.m_46472_().equals(Level.f_46428_)) {
               if (!SkillHelper.outOfMagicule(entity, skillInstance)) {
                  boolean sucess = false;
                  switch(skillInstance.getMode()) {
                  case 1:
                     if (level.m_46471_() || level.m_46470_()) {
                        level.m_8606_(24000, 0, false, false);
                        sucess = true;
                     }
                     break;
                  case 2:
                     if (!level.m_46471_() || level.m_46470_()) {
                        level.m_8606_(0, 24000, true, false);
                        sucess = true;
                     }
                     break;
                  case 3:
                     if (!level.m_46470_()) {
                        level.m_8606_(0, 24000, true, true);
                        sucess = true;
                     }
                  }

                  if (sucess) {
                     this.addMasteryPoint(instance, entity);
                     instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
                     entity.m_21011_(InteractionHand.MAIN_HAND, true);
                     level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11736_, SoundSource.PLAYERS, 0.5F, 1.0F);
                     TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
                        return entity;
                     }), new RequestFxSpawningPacket(new ResourceLocation("tensura:weather_manipulation"), entity.m_19879_(), 0.0D, (double)entity.m_20192_(), 0.0D, true));
                  }

               }
            }
         }
      }
   }
}
