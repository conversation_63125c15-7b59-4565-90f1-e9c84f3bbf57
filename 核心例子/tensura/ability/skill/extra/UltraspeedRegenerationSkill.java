package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.registry.TensuraStats;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.stats.StatType;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class UltraspeedRegenerationSkill extends Skill {
   public UltraspeedRegenerationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (!SkillUtils.isSkillMastered(entity, (ManasSkill)CommonSkills.SELF_REGENERATION.get())) {
         return false;
      } else {
         if (entity instanceof ServerPlayer) {
            ServerPlayer player = (ServerPlayer)entity;
            if (player.m_8951_().m_13015_(((StatType)TensuraStats.BOSS_KILLED.get()).m_12902_((EntityType)TensuraEntityTypes.CHARYBDIS.get())) > 0) {
               return newEP > 300000.0D;
            }
         }

         return false;
      }
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public boolean canBeSlotted(ManasSkillInstance instance) {
      return instance.getMastery() < 0;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 6 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
      if (entity instanceof Player) {
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.INSTANT_REGENERATION.get(), 240, 0, false, false, false));
      } else {
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.SELF_REGENERATION.get(), 240, 20, false, false, false));
      }

   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      this.onTick(instance, entity);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      MobEffectInstance effectInstance = entity.m_21124_((MobEffect)TensuraMobEffects.INSTANT_REGENERATION.get());
      if (effectInstance != null && effectInstance.m_19564_() < 1) {
         entity.m_21195_((MobEffect)TensuraMobEffects.INSTANT_REGENERATION.get());
      }

   }
}
