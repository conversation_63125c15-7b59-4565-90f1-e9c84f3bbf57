package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.entity.magic.lightning.LightningBolt;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class LightningDominationSkill extends Skill {
   public LightningDominationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (!SkillUtils.isSkillMastered(entity, (ManasSkill)ExtraSkills.LIGHTNING_MANIPULATION.get())) {
         return false;
      } else {
         return newEP > 400000.0D;
      }
   }

   public double learningCost() {
      return 20.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 20.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity living, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (DamageSourceHelper.isLightningDamage(e.getSource())) {
            e.setAmount(e.getAmount() * 4.0F);
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      if (level.m_46470_()) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            this.addMasteryPoint(instance, entity);
            Entity target = SkillHelper.getTargetingEntity(entity, 30.0D, false, false);
            Vec3 pos;
            if (target != null) {
               pos = target.m_20182_();
            } else {
               BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 30.0D);
               pos = result.m_82450_();
            }

            LightningBolt bolt = new LightningBolt(level, entity);
            ServerPlayer var10001;
            if (entity instanceof ServerPlayer) {
               ServerPlayer serverPlayer = (ServerPlayer)entity;
               var10001 = serverPlayer;
            } else {
               var10001 = null;
            }

            bolt.m_20879_(var10001);
            bolt.setMpCost(this.magiculeCost(entity, instance));
            bolt.setTensuraDamage(15.0F);
            bolt.setAdditionalVisual(8);
            bolt.setRadius(5.0F);
            bolt.setSkill(instance);
            bolt.m_146884_(pos);
            level.m_7967_(bolt);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 0.5F, 1.0F);
         }
      }
   }
}
