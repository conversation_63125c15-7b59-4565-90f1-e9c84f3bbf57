package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.effect.template.Transformation;
import java.util.Iterator;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public abstract class MagicElementalTransformSkill extends Skill implements Transformation {
   public MagicElementalTransformSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public MagicElementalTransformSkill(Skill.SkillType type) {
      super(type);
   }

   protected abstract MagicElemental getMagicElemental();

   protected abstract ManasSkill getElementalTransform();

   protected abstract MobEffect getMagicElementalEffect();

   protected abstract void doVisualEffect(LivingEntity var1);

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (!SkillUtils.isSkillMastered(entity, this.getElementalTransform())) {
         return false;
      } else {
         return newEP > 400000.0D;
      }
   }

   public double learningCost() {
      return 10000.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 10000.0D;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      return this.canTick(instance, entity);
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return entity.m_21023_(this.getMagicElementalEffect());
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 6 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      if (this.canTick(instance, entity)) {
         entity.m_21195_(this.getMagicElementalEffect());
      }

   }

   public void onLearnSkill(ManasSkillInstance instance, LivingEntity living, UnlockSkillEvent event) {
      if (instance.getMastery() >= 0 && !instance.isTemporarySkill()) {
         Iterator var4 = SkillAPI.getSkillRegistry().getValues().iterator();

         while(var4.hasNext()) {
            ManasSkill manasSkill = (ManasSkill)var4.next();
            if (manasSkill instanceof SpiritualMagic) {
               SpiritualMagic skill = (SpiritualMagic)manasSkill;
               if (skill.getElemental() == this.getMagicElemental() && skill.getLevel().getId() <= 3 && SkillUtils.learnSkill(living, (ManasSkill)skill) && living instanceof Player) {
                  Player player = (Player)living;
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               }
            }
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!this.failedToActivate(entity, this.getMagicElementalEffect())) {
         if (entity.m_21023_(this.getMagicElementalEffect())) {
            entity.m_21195_(this.getMagicElementalEffect());
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
            instance.setCoolDown(600);
         } else if (!SkillHelper.outOfMagicule(entity, instance)) {
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(this.isMastered(instance, entity) ? 960 : 780);
            entity.m_7292_(new MobEffectInstance(this.getMagicElementalEffect(), this.isMastered(instance, entity) ? 7200 : 3600, 0, false, false, true));
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            this.doVisualEffect(entity);
         }
      }
   }
}
