package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class InfiniteRegenerationSkill extends Skill {
   public InfiniteRegenerationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (!SkillUtils.hasSkill(entity, (ManasSkill)ExtraSkills.ULTRASPEED_REGENERATION.get())) {
         return false;
      } else {
         return newEP > 2000000.0D;
      }
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public boolean canBeSlotted(ManasSkillInstance instance) {
      return instance.getMastery() < 0;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 6 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
      if (entity instanceof Player) {
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.INSTANT_REGENERATION.get(), 240, 1, false, false, false));
      } else {
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.SELF_REGENERATION.get(), 240, 30, false, false, false));
      }

   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      this.onTick(instance, entity);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_21195_((MobEffect)TensuraMobEffects.INSTANT_REGENERATION.get());
   }
}
