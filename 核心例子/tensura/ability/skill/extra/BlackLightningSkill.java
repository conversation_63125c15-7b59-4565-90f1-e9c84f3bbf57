package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.entity.magic.beam.BeamProjectile;
import com.github.manasmods.tensura.entity.magic.lightning.BlackLightningBolt;
import com.github.manasmods.tensura.entity.magic.misc.DeathStormTornado;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;

public class BlackLightningSkill extends Skill {
   public BlackLightningSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 500.0D;
   }

   public int modes() {
      return 5;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (!this.canAdvanceModes(entity)) {
         return 0;
      } else {
         int var10000;
         if (reverse) {
            switch(instance.getMode()) {
            case 1:
               var10000 = this.canEquipDeathStorm(entity) ? 5 : (this.isMastered(instance, entity) ? 4 : 1);
               break;
            case 2:
               var10000 = 1;
               break;
            case 3:
               var10000 = 2;
               break;
            case 4:
               var10000 = 3;
               break;
            case 5:
               var10000 = this.isMastered(instance, entity) ? 4 : 3;
               break;
            default:
               var10000 = 0;
            }

            return var10000;
         } else {
            switch(instance.getMode()) {
            case 1:
               var10000 = 2;
               break;
            case 2:
               var10000 = 3;
               break;
            case 3:
               var10000 = this.isMastered(instance, entity) ? 4 : (this.canEquipDeathStorm(entity) ? 5 : 1);
               break;
            case 4:
               var10000 = this.canEquipDeathStorm(entity) ? 5 : 1;
               break;
            default:
               var10000 = 1;
            }

            return var10000;
         }
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.black_lightning.default");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.black_lightning.weak");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.black_lightning.strong");
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill.mode.black_lightning.blast");
         break;
      case 5:
         var10000 = Component.m_237115_("tensura.skill.mode.black_lightning.storm");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 500.0D;
         break;
      case 2:
      case 4:
      default:
         var10000 = 100.0D;
         break;
      case 3:
         var10000 = 1000.0D;
         break;
      case 5:
         var10000 = 5000.0D;
      }

      return var10000;
   }

   public String modeLearningId(int mode) {
      return mode == 5 ? "DeathStorm" : "None";
   }

   public boolean canAdvanceModes(LivingEntity entity) {
      return SkillUtils.hasSkill(entity, (ManasSkill)ExtraSkills.MOLECULAR_MANIPULATION.get());
   }

   public boolean canEquipDeathStorm(LivingEntity entity) {
      return SkillUtils.hasSkill(entity, (ManasSkill)ExtraSkills.WIND_DOMINATION.get()) ? true : SkillUtils.hasSkill(entity, (ManasSkill)ExtraSkills.WIND_MANIPULATION.get());
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.getOrCreateTag().m_128451_("StormingTick") > 0;
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() != 4) {
         return false;
      } else {
         if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         double cost = this.magiculeCost(entity, instance);
         BeamProjectile.spawnLastingBeam((EntityType)TensuraEntityTypes.BLACK_LIGHTNING_BLAST.get(), 25.0F, 1.0F, 21, 30.0F, 2.0F, entity.m_146892_(), entity, instance, cost, cost, heldTicks);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12089_, SoundSource.PLAYERS, 0.8F, 0.5F);
         return true;
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      int mode = instance.getMode();
      switch(mode) {
      case 4:
         instance.getOrCreateTag().m_128405_("BeamID", 0);
         instance.markDirty();
         break;
      case 5:
         CompoundTag tag = instance.getOrCreateTag();
         int learnPoint = tag.m_128451_("DeathStorm");
         if (learnPoint < 100) {
            tag.m_128405_("DeathStorm", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
            if (entity instanceof Player) {
               Player player = (Player)entity;
               if (tag.m_128451_("DeathStorm") >= 100) {
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(5)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               } else {
                  instance.setCoolDown(10);
                  SkillUtils.learningFailPenalty(entity);
                  player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(5)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
               }

               player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

            instance.markDirty();
            return;
         }

         ServerLevel serverLevel;
         if (tag.m_128451_("StormingTick") > 0) {
            tag.m_128405_("StormingTick", 0);
            if (level instanceof ServerLevel) {
               serverLevel = (ServerLevel)level;
               if (serverLevel.m_46470_()) {
                  serverLevel.m_8606_(0, 24000, false, false);
               }
            }

            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12089_, SoundSource.PLAYERS, 1.0F, 1.0F);
         } else {
            if (SkillHelper.outOfMagicule(entity, instance)) {
               return;
            }

            this.addMasteryPoint(instance, entity);
            tag.m_128405_("StormingTick", 30);
            if (level instanceof ServerLevel) {
               serverLevel = (ServerLevel)level;
               if (!serverLevel.m_46470_()) {
                  serverLevel.m_8606_(0, 24000, true, true);
               }
            }

            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12089_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

         instance.markDirty();
         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         break;
      default:
         if (SkillHelper.outOfMagicule(entity, instance)) {
            return;
         }

         Entity target = SkillHelper.getTargetingEntity(entity, 60.0D, false, false);
         Vec3 pos;
         if (target != null) {
            pos = target.m_20182_();
         } else {
            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 50.0D);
            pos = result.m_82450_();
         }

         BlackLightningBolt bolt = new BlackLightningBolt(level, entity);
         if (entity instanceof ServerPlayer) {
            ServerPlayer serverPlayer = (ServerPlayer)entity;
            bolt.m_20879_(serverPlayer);
         }

         bolt.setMpCost(this.magiculeCost(entity, instance));
         int radius = 0;
         switch(mode) {
         case 1:
            radius = 3;
            bolt.setTensuraDamage(50.0F);
            bolt.setAdditionalVisual(4);
            break;
         case 2:
            radius = 2;
            bolt.setTensuraDamage(25.0F);
            bolt.setAdditionalVisual(2);
            break;
         case 3:
            radius = 5;
            bolt.setTensuraDamage(150.0F);
            bolt.setAdditionalVisual(6);
         }

         bolt.setRadius((float)radius);
         bolt.setSkill(instance);
         bolt.m_146884_(pos);
         level.m_7967_(bolt);
         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         this.addMasteryPoint(instance, entity);
      }

   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      Level level = entity.m_9236_();
      int stormTick = tag.m_128451_("StormingTick");
      if (stormTick > 0 && !level.m_46470_()) {
         tag.m_128405_("StormingTick", 0);
         instance.markDirty();
      } else {
         List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(15.0D), (targetx) -> {
            return this.deathStormTarget(entity, targetx);
         });
         if (!list.isEmpty()) {
            Iterator var7 = list.iterator();

            label45:
            while(true) {
               while(true) {
                  if (!var7.hasNext()) {
                     break label45;
                  }

                  LivingEntity target = (LivingEntity)var7.next();
                  if (!entity.m_217043_().m_188499_() && stormTick % 2 == 0 && !this.nearTornado(target)) {
                     DeathStormTornado tornado = new DeathStormTornado(level, entity);
                     tornado.setMpCost(this.magiculeCost(entity, instance) / 50.0D);
                     tornado.setNewDamage(50.0F);
                     tornado.setRadius(4.0F);
                     tornado.setSkill(instance);
                     tornado.m_146884_(target.m_20182_());
                     level.m_7967_(tornado);
                  } else {
                     BlackLightningBolt bolt = new BlackLightningBolt(level, entity);
                     if (entity instanceof ServerPlayer) {
                        ServerPlayer serverPlayer = (ServerPlayer)entity;
                        bolt.m_20879_(serverPlayer);
                     }

                     bolt.setMpCost(this.magiculeCost(entity, instance) / 50.0D);
                     bolt.setTensuraDamage(100.0F);
                     bolt.setAdditionalVisual(2);
                     bolt.setRadius(4.0F);
                     bolt.setSkill(instance);
                     bolt.m_146884_(target.m_20182_());
                     level.m_7967_(bolt);
                  }
               }
            }
         } else {
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12090_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

         tag.m_128405_("StormingTick", stormTick - 1);
         instance.markDirty();
         if (tag.m_128451_("StormingTick") <= 0) {
            if (level instanceof ServerLevel) {
               ServerLevel serverLevel = (ServerLevel)level;
               if (serverLevel.m_46470_()) {
                  serverLevel.m_8606_(0, 24000, false, false);
               }
            }

         }
      }
   }

   private boolean deathStormTarget(LivingEntity owner, LivingEntity target) {
      if (target == owner) {
         return false;
      } else if (target.m_7307_(owner)) {
         return false;
      } else if (TensuraEPCapability.isTargetNeutral(target, owner)) {
         return false;
      } else if (!target.m_6084_()) {
         return false;
      } else if (target instanceof Player) {
         Player player = (Player)target;
         return !player.m_7500_() && !player.m_5833_();
      } else {
         return target.m_5720_() == SoundSource.HOSTILE || target.m_5720_() == SoundSource.NEUTRAL;
      }
   }

   private boolean nearTornado(LivingEntity entity) {
      return !entity.m_9236_().m_45976_(DeathStormTornado.class, entity.m_20191_().m_82400_(5.0D)).isEmpty();
   }
}
