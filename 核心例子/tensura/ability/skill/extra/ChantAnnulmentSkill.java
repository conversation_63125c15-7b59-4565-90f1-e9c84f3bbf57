package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.magic.Magic;
import com.github.manasmods.tensura.ability.skill.Skill;
import java.util.Iterator;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class ChantAnnulmentSkill extends Skill {
   public ChantAnnulmentSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      int magics = 0;
      Iterator var5 = SkillAPI.getSkillsFrom(entity).getLearnedSkills().iterator();

      while(var5.hasNext()) {
         ManasSkillInstance skill = (ManasSkillInstance)var5.next();
         if (skill.getSkill() instanceof Magic && skill.isMastered(entity)) {
            ++magics;
         }
      }

      return magics >= 10;
   }

   public double learningCost() {
      return 1000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return instance.getMastery() >= 0;
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }
}
