package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.human.CloneEntity;
import com.github.manasmods.tensura.registry.TensuraStats;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Objects;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.StatType;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.entity.living.LivingDeathEvent;
import net.minecraftforge.event.entity.player.PlayerEvent.PlayerRespawnEvent;

public class BodyDoubleSkill extends Skill {
   public BodyDoubleSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 10000.0D;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         if (player.m_8951_().m_13015_(((StatType)TensuraStats.BOSS_KILLED.get()).m_12902_((EntityType)TensuraEntityTypes.IFRIT.get())) <= 0) {
            return false;
         } else {
            return TensuraEPCapability.getEP(entity) >= 100000.0D;
         }
      } else {
         return false;
      }
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.body_double.creation");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.body_double.control");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.getTag() == null ? false : instance.getTag().m_128441_("Original");
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      if (tag.m_128441_("Original")) {
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 200, 1, false, false, false));
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.ENERGY_BLOCKADE.get(), 200, 4, false, false, false));
         Level var5 = entity.m_9236_();
         if (var5 instanceof ServerLevel) {
            ServerLevel level = (ServerLevel)var5;
            UUID uuid = tag.m_128342_("Original");
            Entity body = SkillHelper.getEntityFromUUID(level, uuid, (clone) -> {
               return clone instanceof CloneEntity;
            });
            if (!(body instanceof CloneEntity) || body.m_9236_() != level || body.m_20270_(entity) > 50.0F) {
               entity.m_6469_(TensuraDamageSources.noEnergySource(entity), entity.m_21233_() * 0.1F);
               DamageSourceHelper.directSpiritualHurt(entity, (Entity)null, TensuraDamageSources.noEnergySource(entity), (float)entity.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get()) * 0.1F, 0.0F);
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  player.m_5661_(Component.m_237115_("tensura.skill.mode.body_double.main_too_far").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
                  player.m_6330_(SoundEvents.f_12511_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }

         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         if (TensuraPlayerCapability.isSpiritualForm(player)) {
            player.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
            return;
         }
      }

      Level level = entity.m_9236_();
      double var10000;
      if (instance.getMode() == 1) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            var10000 = TensuraPlayerCapability.getBaseMagicule(player);
         } else {
            var10000 = TensuraEPCapability.getEP(entity);
         }

         double MP = var10000;
         double EP = MP * 0.1D;
         if (!SkillHelper.outOfMagicule(entity, EP)) {
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(instance.isMastered(entity) ? 1 : 3);
            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(entity.f_19853_, entity, Fluid.NONE, 5.0D);
            CloneEntity clone = this.summonClone(entity, level, EP, result.m_82450_());
            CloneEntity.copyEffects(entity, clone);
            EquipmentSlot[] var10 = EquipmentSlot.values();
            int var11 = var10.length;

            for(int var12 = 0; var12 < var11; ++var12) {
               EquipmentSlot slot = var10[var12];
               clone.m_8061_(slot, entity.m_6844_(slot).m_41777_());
            }

         }
      } else {
         CloneEntity clone = (CloneEntity)SkillHelper.getTargetingEntity(CloneEntity.class, entity, 50.0D, 0.2D, false);
         if (clone != null) {
            if (clone.m_21830_(entity)) {
               if (clone.m_6084_() && clone.getSkill() == this) {
                  if (entity.m_6144_()) {
                     if (!clone.m_6107_()) {
                        clone.m_6469_(TensuraDamageSources.noEnergySource(entity), clone.m_21233_());
                        entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     }

                  } else {
                     if (entity instanceof Player) {
                        Player player = (Player)entity;
                        var10000 = TensuraPlayerCapability.getMagicule(player);
                     } else {
                        var10000 = TensuraEPCapability.getEP(entity);
                     }

                     double MP = var10000;
                     CloneEntity newClone = this.summonClone(entity, level, MP, entity.m_20182_());
                     CloneEntity.copyEffects(entity, newClone);
                     newClone.m_21153_(entity.m_21223_());
                     entity.m_21153_(clone.m_21223_());
                     if (entity instanceof ServerPlayer) {
                        ServerPlayer player = (ServerPlayer)entity;
                        TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                           cap.setMagicule(TensuraEPCapability.getEP(clone));
                           TensuraPlayerCapability.sync(player);
                        });
                        player.m_8999_((ServerLevel)level, clone.m_20182_().f_82479_, clone.m_20182_().f_82480_, clone.m_20182_().f_82481_, clone.m_146908_(), clone.m_146909_());
                        player.f_19864_ = true;
                     }

                     newClone.copyEquipments(entity);
                     CloneEntity.copyRotation(entity, newClone);
                     clone.copyEquipmentsOntoOwner(entity, true);
                     CloneEntity.copyEffects(clone, entity);
                     clone.remove();
                     CompoundTag tag = instance.getOrCreateTag();
                     UUID uuid = tag.m_128441_("Original") ? tag.m_128342_("Original") : null;
                     if (uuid != null && Objects.equals(clone.m_20148_(), uuid)) {
                        tag.m_128473_("Original");
                     } else {
                        newClone.setImmobile(true);
                        tag.m_128362_("Original", newClone.m_20148_());
                     }

                     instance.markDirty();
                  }
               }
            }
         }
      }
   }

   private CloneEntity summonClone(LivingEntity entity, Level level, double EP, Vec3 position) {
      EntityType<CloneEntity> type = entity.m_6144_() ? (EntityType)TensuraEntityTypes.CLONE_SLIM.get() : (EntityType)TensuraEntityTypes.CLONE_DEFAULT.get();
      CloneEntity clone = new CloneEntity(type, level);
      if (entity instanceof Player) {
         Player player = (Player)entity;
         clone.m_21828_(player);
      }

      clone.setSkill(this);
      clone.copyStatsAndSkills(entity, true);
      TensuraEPCapability.setLivingEP(clone, EP);
      clone.m_21153_(clone.m_21233_());
      clone.m_146884_(position);
      CloneEntity.copyRotation(entity, clone);
      level.m_7967_(clone);
      level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
      TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123765_, 1.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123765_, 2.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(clone, ParticleTypes.f_123765_, 1.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(clone, ParticleTypes.f_123765_, 2.0D);
      return clone;
   }

   public void onSubordinateDeath(ManasSkillInstance instance, LivingEntity owner, LivingDeathEvent e) {
      if (!e.isCanceled()) {
         LivingEntity var5 = e.getEntity();
         if (var5 instanceof CloneEntity) {
            CloneEntity clone = (CloneEntity)var5;
            if (clone.getSkill() == this) {
               CompoundTag tag = instance.getOrCreateTag();
               if (tag.m_128441_("Original")) {
                  UUID uuid = tag.m_128342_("Original");
                  if (Objects.equals(clone.m_20148_(), uuid)) {
                     tag.m_128473_("Original");
                     instance.markDirty();
                     owner.m_6469_(DamageSourceHelper.turnTensura(e.getSource()).setIgnoreBarrier(3.0F).m_19381_(), owner.m_21233_() * 10.0F);
                     if (owner.m_6084_() && owner instanceof ServerPlayer) {
                        ServerPlayer player = (ServerPlayer)owner;
                        player.m_8999_((ServerLevel)owner.m_9236_(), clone.m_20182_().f_82479_, clone.m_20182_().f_82480_, clone.m_20182_().f_82481_, clone.m_146908_(), clone.m_146909_());
                        player.f_19864_ = true;
                     }

                     return;
                  }
               }

               if (!clone.m_6107_()) {
                  TensuraEPCapability.getFrom(clone).ifPresent((cap) -> {
                     SkillHelper.gainMP(owner, cap.getEP(), false);
                     cap.setSkipEPDrop(true);
                     if (owner instanceof Player) {
                        Player player = (Player)owner;
                        player.m_5661_(Component.m_237110_("tensura.ep.acquire_mp", new Object[]{cap.getEP()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                        player.m_6330_(SoundEvents.f_12275_, SoundSource.PLAYERS, 0.5F, 1.0F);
                     }

                  });
               }
            }
         }
      }
   }

   public void onDeath(ManasSkillInstance instance, LivingDeathEvent event) {
      if (!event.isCanceled()) {
         LivingEntity entity = event.getEntity();
         if (!entity.m_6084_()) {
            Level var5 = entity.m_9236_();
            if (var5 instanceof ServerLevel) {
               ServerLevel level = (ServerLevel)var5;
               if (entity instanceof Player) {
                  CompoundTag tag = instance.getOrCreateTag();
                  if (tag.m_128441_("Original")) {
                     UUID uuid = tag.m_128342_("Original");
                     Entity var8 = SkillHelper.getEntityFromUUID(level, uuid, (clonex) -> {
                        return clonex instanceof CloneEntity;
                     });
                     if (var8 instanceof CloneEntity) {
                        CloneEntity body = (CloneEntity)var8;
                        tag.m_128473_("Original");
                        if (!body.m_213877_() && !(body.m_21223_() <= 0.0F)) {
                           entity.m_21153_(Math.max(body.m_21223_(), 1.0F));
                           event.setCanceled(true);
                           TensuraEffectsCapability.resetEverything(entity, false, false);
                           level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
                           CloneEntity clone = this.summonClone(entity, level, 100.0D, entity.m_20182_());
                           clone.copyEquipments(entity);
                           instance.addMasteryPoint(entity);
                           boolean keepInv = level.m_46469_().m_46207_(GameRules.f_46133_);
                           if (!keepInv) {
                              clone.m_6667_(event.getSource());
                              clone.remove();
                           }

                           SkillHelper.moveAcrossDimensionTo(entity, body);
                           body.copyEquipmentsOntoOwner(entity, true);
                           body.remove();
                           if (keepInv) {
                              clone = (CloneEntity)SkillHelper.moveAcrossDimensionTo(clone, entity);
                              if (clone != null) {
                                 clone.remove();
                              }
                           }

                        }
                     }
                  }
               }
            }
         }
      }
   }

   public void onRespawn(ManasSkillInstance instance, PlayerRespawnEvent event) {
      if (!event.isEndConquered()) {
         instance.getOrCreateTag().m_128473_("Original");
         instance.markDirty();
      }
   }
}
