package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.projectile.WebBulletProjectile;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.mojang.math.Vector3f;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;

public class StickySteelThreadSkill extends Skill {
   public StickySteelThreadSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 200.0D;
   }

   public int modes() {
      return 4;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 4 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 4 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.sticky_steel_thread.sticky");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.sticky_steel_thread.steel");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.sticky_steel_thread.slinger");
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill.mode.sticky_steel_thread.arcane_thread");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 50.0D;
         break;
      case 2:
         var10000 = 100.0D;
         break;
      default:
         var10000 = 200.0D;
      }

      return var10000;
   }

   public String modeLearningId(int mode) {
      return mode == 4 ? "ArcaneThread" : "None";
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMastery() < 0) {
         return false;
      } else {
         CompoundTag tag = instance.getOrCreateTag();
         return instance.getMode() != 4 && tag.m_128451_("ArcaneThread") <= 100;
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!SkillHelper.outOfMagicule(entity, instance)) {
         Level level = entity.m_9236_();
         CompoundTag tag = instance.getOrCreateTag();
         switch(instance.getMode()) {
         case 1:
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(instance.isMastered(entity) ? 1 : 3);
            this.shootWebBullet(entity, level, ((Item)TensuraToolItems.STICKY_WEB_CARTRIDGE.get()).m_7968_());
            break;
         case 2:
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(instance.isMastered(entity) ? 1 : 3);
            this.shootWebBullet(entity, level, ((Item)TensuraToolItems.STICKY_STEEL_WEB_CARTRIDGE.get()).m_7968_());
            break;
         case 3:
            Entity oldBullet = entity.m_9236_().m_6815_(tag.m_128451_("BulletID"));
            if (oldBullet instanceof WebBulletProjectile) {
               oldBullet.m_146870_();
            }

            if (entity.m_6144_()) {
               return;
            }

            WebBulletProjectile bullet = new WebBulletProjectile(level, entity, true, ((Item)TensuraToolItems.WEB_CARTRIDGE.get()).m_7968_());
            bullet.setSlinger(true);
            Vector3f vector3f = new Vector3f(entity.m_20252_(1.0F));
            bullet.m_6686_((double)vector3f.m_122239_(), (double)vector3f.m_122260_(), (double)vector3f.m_122269_(), 2.0F, 0.0F);
            entity.m_6674_(entity.m_7655_());
            level.m_7967_(bullet);
            tag.m_128405_("BulletID", bullet.m_19879_());
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11687_, SoundSource.PLAYERS, 1.0F, 1.0F);
            break;
         case 4:
            int learnPoint = tag.m_128451_("ArcaneThread");
            if (learnPoint < 100) {
               tag.m_128405_("ArcaneThread", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  if (tag.m_128451_("ArcaneThread") >= 100) {
                     player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(4)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  } else {
                     instance.setCoolDown(10);
                     SkillUtils.learningFailPenalty(entity);
                     player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(4)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
                  }

                  player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }

               instance.markDirty();
               return;
            }

            double distance = instance.isMastered(entity) ? 10.0D : 20.0D;
            LivingEntity target = SkillHelper.getTargetingEntity(entity, distance, false, true);
            if (target == null) {
               return;
            }

            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            TensuraParticleHelper.addServerParticlesAroundSelf(target, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_50033_.m_49966_()), 1.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(target, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_50033_.m_49966_()), 2.0D);
            if (this.getArcaneThreadSource(target, instance.isMastered(entity) ? 300 : 400) == entity) {
               this.addMasteryPoint(instance, entity);
               instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
               target.m_6469_(this.sourceWithMP(TensuraDamageSources.steelThread(entity), entity, instance), instance.isMastered(entity) ? 60.0F : 30.0F);
               target.m_21195_((MobEffect)TensuraMobEffects.WEBBED.get());
               target.m_21195_((MobEffect)TensuraMobEffects.SILENCE.get());
               TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123766_, 2.0D);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
            } else if (!target.m_21023_((MobEffect)TensuraMobEffects.WEBBED.get())) {
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12054_, SoundSource.PLAYERS, 1.0F, 1.0F);
               if (TensuraEPCapability.getEP(target) <= TensuraEPCapability.getEP(entity)) {
                  SkillHelper.checkThenAddEffectSource(target, entity, new MobEffectInstance((MobEffect)TensuraMobEffects.WEBBED.get(), 500, 0, true, true, false));
               }

               SkillHelper.checkThenAddEffectSource(target, entity, new MobEffectInstance((MobEffect)TensuraMobEffects.SILENCE.get(), 500, 0, true, true, false));
            }
         }

      }
   }

   @Nullable
   private Player getArcaneThreadSource(LivingEntity target, int duration) {
      MobEffectInstance webbed = target.m_21124_((MobEffect)TensuraMobEffects.WEBBED.get());
      if (webbed == null) {
         return null;
      } else {
         return webbed.m_19557_() < duration ? null : TensuraEffectsCapability.getEffectSource(target, (MobEffect)TensuraMobEffects.WEBBED.get());
      }
   }

   private void shootWebBullet(LivingEntity entity, Level level, ItemStack ammo) {
      WebBulletProjectile bullet = new WebBulletProjectile(level, entity, true, ammo);
      Vector3f vector3f = new Vector3f(entity.m_20252_(1.0F));
      bullet.m_6686_((double)vector3f.m_122239_(), (double)vector3f.m_122260_(), (double)vector3f.m_122269_(), 1.0F, 0.0F);
      entity.m_6674_(entity.m_7655_());
      level.m_7967_(bullet);
      entity.m_21011_(InteractionHand.MAIN_HAND, true);
      level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11687_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }
}
