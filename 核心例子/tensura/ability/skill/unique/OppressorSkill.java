package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.player.PlayerEvent.PlayerRespawnEvent;

public class OppressorSkill extends Skill {
   public OppressorSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 20000.0D;
   }

   public double learningCost() {
      return 500.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public int modes() {
      return 5;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 5 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 5 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.oppressor.repel");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.oppressor.attract");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.oppressor.oppress");
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill.mode.oppressor.bleve");
         break;
      case 5:
         var10000 = Component.m_237115_("tensura.skill.mode.oppressor.flicker");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
      case 2:
      case 5:
         var10000 = 50.0D;
         break;
      case 3:
      default:
         var10000 = 0.0D;
         break;
      case 4:
         var10000 = 1000.0D;
      }

      return var10000;
   }

   public void onLearnSkill(ManasSkillInstance instance, LivingEntity entity, UnlockSkillEvent event) {
      if (instance.getMastery() >= 0 && !instance.isTemporarySkill()) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         ManasSkill skill = (ManasSkill)ExtraSkills.GRAVITY_MANIPULATION.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         if (instance.isToggled() && this.isMastered(instance, event.getEntity())) {
            if (event.getSource().m_146707_()) {
               event.setCanceled(true);
            } else if (event.getSource().m_19360_() && !event.getSource().m_19387_()) {
               event.setCanceled(true);
            }

         }
      }
   }

   public boolean onHeld(ManasSkillInstance skillInstance, LivingEntity entity, int heldTicks) {
      if (skillInstance.getMode() != 4 && skillInstance.getMode() != 3) {
         CompoundTag tag = skillInstance.getOrCreateTag();
         Entity target;
         switch(skillInstance.getMode()) {
         case 1:
            target = SkillHelper.getTargetingEntity(entity, 10.0D, 0.5D, false, true);
            if (target != null) {
               TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123760_, 1.0D);
            }
            break;
         case 2:
            target = SkillHelper.getTargetingEntity(entity, 30.0D, 0.5D, false, true);
            if (target != null) {
               TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123760_, 1.0D);
            }
         }

         if (!tag.m_128471_("masteryPower")) {
            tag.m_128405_("scale", heldTicks);
         }

         if (tag.m_128451_("scale") > 100) {
            tag.m_128405_("scale", 100);
         }

         skillInstance.markDirty();
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.power_scale", new Object[]{(double)tag.m_128451_("scale") / 10.0D}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), true);
         }

         return true;
      } else {
         return false;
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      LivingEntity target;
      Player player;
      switch(instance.getMode()) {
      case 3:
         target = (LivingEntity)SkillHelper.getTargetingEntity(LivingEntity.class, entity, 20.0D, 0.0D, false);
         if (target == null) {
            return;
         }

         if (target instanceof Player) {
            player = (Player)target;
            if (player.m_150110_().f_35934_) {
               return;
            }
         }

         if (target.m_21023_((MobEffect)TensuraMobEffects.OPPRESSION.get())) {
            if (SkillHelper.outOfMagicule(entity, 700.0D)) {
               return;
            }

            instance.addMasteryPoint(entity);
            instance.setCoolDown(instance.isMastered(entity) ? 5 : 10);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            target.m_6469_(this.sourceWithMP(TensuraDamageSources.oppression(entity), entity, instance), instance.isMastered(entity) ? 200.0F : 100.0F);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11913_, SoundSource.PLAYERS, 1.0F, 1.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123747_, 1.0D);
         } else {
            if (SkillHelper.outOfMagicule(entity, 300.0D)) {
               return;
            }

            instance.addMasteryPoint(entity);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            DamageSourceHelper.markHurt(target, entity);
            SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.OPPRESSION.get(), 600, 0, false, false, false, true);
            TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123790_, 1.0D);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }
         break;
      case 4:
         if (SkillHelper.outOfMagicule(entity, instance)) {
            return;
         }

         target = (LivingEntity)SkillHelper.getTargetingEntity(LivingEntity.class, entity, 20.0D, 0.0D, false);
         if (target == null) {
            return;
         }

         if (target instanceof Player) {
            player = (Player)target;
            if (player.m_150110_().f_35934_) {
               return;
            }
         }

         instance.addMasteryPoint(entity);
         instance.setCoolDown(instance.isMastered(entity) ? 5 : 10);
         target.m_6469_(this.sourceWithMP(TensuraDamageSources.oppressionBleve(entity), entity, instance), 100.0F);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11913_, SoundSource.PLAYERS, 1.0F, 1.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123747_, 1.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123812_, 1.0D);
         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         target.m_20256_(target.m_20184_().m_82520_(0.0D, 2.0D, 0.0D));
         target.f_19864_ = true;
      }

   }

   public void onRelease(ManasSkillInstance skillInstance, LivingEntity entity, int heldTicks) {
      if (this.isHeld(entity)) {
         CompoundTag tag = skillInstance.getOrCreateTag();
         Player player;
         Vec3 vec3;
         Entity target;
         double scale;
         switch(skillInstance.getMode()) {
         case 1:
            if (tag.m_128451_("scale") <= 0) {
               return;
            }

            if (!SkillHelper.outOfMagicule(entity, skillInstance)) {
               target = SkillHelper.getTargetingEntity(entity, 10.0D, 0.5D, false, true);
               if (target != null) {
                  if (target instanceof Player) {
                     player = (Player)target;
                     if (player.m_150110_().f_35934_) {
                        this.resetScale(skillInstance, entity);
                        return;
                     }
                  }

                  scale = (double)tag.m_128451_("scale") / -15.0D;
                  if (scale >= 2.0D) {
                     skillInstance.addMasteryPoint(entity);
                  }

                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_175829_, 1.0D);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_175829_, 1.0D);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  vec3 = (new Vec3(entity.m_20185_() - target.m_20185_(), entity.m_20186_() - target.m_20186_(), entity.m_20189_() - target.m_20189_())).m_82490_(1.0D / (double)target.m_20270_(entity));
                  target.m_20256_(vec3.m_82541_().m_82490_(scale));
                  target.f_19864_ = true;
               }
            }

            this.resetScale(skillInstance, entity);
            break;
         case 2:
            if (tag.m_128451_("scale") <= 0) {
               return;
            }

            if (!SkillHelper.outOfMagicule(entity, skillInstance)) {
               target = SkillHelper.getTargetingEntity(entity, 30.0D, 0.5D, false, true);
               if (target != null) {
                  if (target instanceof Player) {
                     player = (Player)target;
                     if (player.m_150110_().f_35934_) {
                        this.resetScale(skillInstance, entity);
                        return;
                     }
                  }

                  scale = (double)tag.m_128451_("scale") / 15.0D;
                  if (scale >= 2.0D) {
                     skillInstance.addMasteryPoint(entity);
                  }

                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_175829_, 1.0D);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_175829_, 1.0D);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  vec3 = (new Vec3(entity.m_20185_() - target.m_20185_(), entity.m_20186_() - target.m_20186_(), entity.m_20189_() - target.m_20189_())).m_82490_(1.0D / (double)target.m_20270_(entity));
                  target.m_20256_(vec3.m_82541_().m_82490_(scale));
                  target.f_19864_ = true;
               }
            }

            this.resetScale(skillInstance, entity);
         case 3:
         case 4:
         default:
            break;
         case 5:
            if (tag.m_128451_("scale") <= 0) {
               return;
            }

            if (!SkillHelper.outOfMagicule(entity, skillInstance)) {
               float scale = (float)tag.m_128451_("scale") / 10.0F;
               if (scale >= 2.0F) {
                  skillInstance.addMasteryPoint(entity);
               }

               if (entity.m_20202_() != null) {
                  TensuraParticleHelper.addServerParticlesAroundSelf(entity.m_20202_(), ParticleTypes.f_123812_, 0.5D);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  SkillHelper.riptidePushVehicle(entity.m_20202_(), entity, scale);
                  entity.m_20202_().f_19864_ = true;
                  entity.m_20202_().f_19812_ = true;
               } else {
                  SkillHelper.riptidePush(entity, scale);
                  entity.f_19864_ = true;
                  entity.f_19812_ = true;
                  TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123812_, 1.0D);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }

            this.resetScale(skillInstance, entity);
         }

      }
   }

   public void onRespawn(ManasSkillInstance instance, PlayerRespawnEvent event) {
      if (!event.isEndConquered()) {
         instance.getOrCreateTag().m_128379_("masteryPower", false);
         instance.markDirty();
      }
   }

   public void onNumberKeyPress(ManasSkillInstance instance, Player player, int keyNumber) {
      if (instance.isMastered(player)) {
         CompoundTag tag = instance.getOrCreateTag();
         if (keyNumber == 0) {
            if (tag.m_128471_("masteryPower")) {
               tag.m_128379_("masteryPower", false);
               tag.m_128405_("scale", 10);
               instance.markDirty();
            }

         } else {
            if (!tag.m_128471_("masteryPower")) {
               tag.m_128379_("masteryPower", true);
            }

            tag.m_128405_("scale", keyNumber * 10 + 10);
            instance.markDirty();
         }
      }
   }

   private void resetScale(ManasSkillInstance instance, LivingEntity entity) {
      if (!instance.isMastered(entity)) {
         CompoundTag tag = instance.getOrCreateTag();
         tag.m_128405_("scale", 0);
         instance.markDirty();
      }
   }
}
