package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.ISpatialStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.extra.ThoughtAccelerationSkill;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.event.SkillPlunderEvent;
import com.github.manasmods.tensura.menu.GreatSageCraftingMenu;
import com.github.manasmods.tensura.menu.GreatSageRefiningMenu;
import com.github.manasmods.tensura.menu.container.SpatialStorageContainer;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.ClientboundSpatialStorageOpenPacket;
import java.util.List;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ContainerLevelAccess;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.player.PlayerContainerEvent.Open;
import net.minecraftforge.network.PacketDistributor;
import org.jetbrains.annotations.NotNull;

public class GreatSageSkill extends Skill implements ISpatialStorage {
   protected static final UUID ACCELERATION = UUID.fromString("753d7401-7c9c-49de-ad16-6b2b6e1e9342");

   public GreatSageSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 75000.0D;
   }

   public double learningCost() {
      return 10000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return true;
   }

   protected boolean canActivateInRaceLimit(ManasSkillInstance instance) {
      return instance.getMode() == 1;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.great_sage.analytical_appraisal");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.great_sage.analysis");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      ThoughtAccelerationSkill.onToggle(instance, entity, ACCELERATION, true);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      ThoughtAccelerationSkill.onToggle(instance, entity, ACCELERATION, false);
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 1) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
               int level;
               if (player.m_6047_()) {
                  level = cap.getAnalysisMode();
                  switch(level) {
                  case 1:
                     cap.setAnalysisMode(2);
                     player.m_5661_(Component.m_237115_("tensura.skill.analytical.analyzing_mode.block").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
                     break;
                  case 2:
                     cap.setAnalysisMode(0);
                     player.m_5661_(Component.m_237115_("tensura.skill.analytical.analyzing_mode.both").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
                     break;
                  default:
                     cap.setAnalysisMode(1);
                     player.m_5661_(Component.m_237115_("tensura.skill.analytical.analyzing_mode.entity").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
                  }

                  player.m_6330_(SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  TensuraSkillCapability.sync(player);
               } else {
                  level = instance.isMastered(entity) ? 18 : 8;
                  if (cap.getAnalysisLevel() != level) {
                     cap.setAnalysisLevel(level);
                     cap.setAnalysisDistance(instance.isMastered(entity) ? 30 : 20);
                     entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  } else {
                     cap.setAnalysisLevel(0);
                     entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  }

                  TensuraSkillCapability.sync(player);
               }
            });
         }
      } else {
         LivingEntity target = SkillHelper.getTargetingEntity(entity, 10.0D, false);
         if (target != null && target.m_6084_()) {
            label58: {
               if (target instanceof Player) {
                  Player player = (Player)target;
                  if (player.m_150110_().f_35934_) {
                     break label58;
                  }
               }

               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               ServerLevel level = (ServerLevel)entity.m_9236_();
               int chance = instance.isMastered(entity) ? 50 : 25;
               boolean failed = true;
               if (entity.m_217043_().m_188503_(100) <= chance) {
                  List<ManasSkillInstance> collection = SkillAPI.getSkillsFrom(target).getLearnedSkills().stream().filter(this::canCopy).toList();
                  if (!collection.isEmpty()) {
                     this.addMasteryPoint(instance, entity);
                     ManasSkill skill = ((ManasSkillInstance)collection.get(target.m_217043_().m_188503_(collection.size()))).getSkill();
                     SkillPlunderEvent event = new SkillPlunderEvent(target, entity, false, skill);
                     if (!MinecraftForge.EVENT_BUS.post(event) && SkillUtils.learnSkill(entity, event.getSkill(), instance.getRemoveTime())) {
                        instance.setCoolDown(10);
                        failed = false;
                        if (entity instanceof Player) {
                           Player player = (Player)entity;
                           player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{event.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                        }

                        level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     }
                  }
               }

               if (failed && entity instanceof Player) {
                  Player player = (Player)entity;
                  player.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  instance.setCoolDown(5);
               }

               return;
            }
         }

         this.openSpatialStorage(entity, instance);
      }
   }

   public void openSpatialStorage(LivingEntity entity, ManasSkillInstance instance) {
      openGreatSageGUI(entity, instance, entity.m_6144_() ? 2 : 1);
   }

   public static void openGreatSageGUI(LivingEntity entity, ManasSkillInstance instance, int type) {
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         player.f_36096_.m_6877_(player);
         player.m_9217_();
         player.m_6330_(SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
         ManasSkill skill = instance.getSkill();
         SpatialStorageContainer container = ((ISpatialStorage)instance.getSkill()).getSpatialStorage(instance);
         TensuraNetwork.INSTANCE.send(PacketDistributor.PLAYER.with(() -> {
            return player;
         }), new ClientboundSpatialStorageOpenPacket(player.m_19879_(), player.f_8940_, container.m_6643_(), container.m_6893_(), SkillUtils.getSkillId(skill), (byte)type));
         if (type == 2) {
            player.f_36096_ = new GreatSageRefiningMenu(player.f_8940_, player.m_150109_(), player, container, skill, ContainerLevelAccess.m_39289_(player.f_19853_, player.m_20183_()));
         } else {
            player.f_36096_ = new GreatSageCraftingMenu(player.f_8940_, player.m_150109_(), player, container, skill, ContainerLevelAccess.m_39289_(player.f_19853_, player.m_20183_()));
         }

         player.m_143399_(player.f_36096_);
         MinecraftForge.EVENT_BUS.post(new Open(player, player.f_36096_));
      }

   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      if (instance.isToggled()) {
         this.gainMastery(instance, entity);
      }

      if (entity instanceof Player) {
         Player player = (Player)entity;
         AbstractContainerMenu var6;
         if (tag.m_128471_("Repeating")) {
            var6 = player.f_36096_;
            if (var6 instanceof GreatSageCraftingMenu) {
               GreatSageCraftingMenu craftingMenu = (GreatSageCraftingMenu)var6;
               if (GreatSageCraftingMenu.autoCrafting(craftingMenu, player.f_19853_, player, craftingMenu.craftingContainer, craftingMenu.resultContainer)) {
                  this.gainMastery(instance, entity);
                  player.m_6330_(SoundEvents.f_12574_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            } else if (GreatSageCraftingMenu.autoCrafting(player.f_19853_, player, instance, this.getSpatialStorage(instance))) {
               this.gainMastery(instance, entity);
            }
         }

         if (tag.m_128471_("Brewing")) {
            var6 = player.f_36096_;
            if (var6 instanceof GreatSageRefiningMenu) {
               GreatSageRefiningMenu menu = (GreatSageRefiningMenu)var6;
               if (GreatSageRefiningMenu.autoRefining(menu, player.f_19853_, player, menu.brewingContainer, menu.resultContainer)) {
                  this.gainMastery(instance, entity);
                  player.m_6330_(SoundEvents.f_12566_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            } else if (GreatSageRefiningMenu.autoRefining(player.f_19853_, player, instance, this.getSpatialStorage(instance))) {
               this.gainMastery(instance, entity);
            }

            if (!tag.m_128471_("RepeatBrewing")) {
               tag.m_128379_("Brewing", false);
               instance.markDirty();
            }
         }

      }
   }

   @NotNull
   public SpatialStorageContainer getSpatialStorage(ManasSkillInstance instance) {
      SpatialStorageContainer container = new SpatialStorageContainer(20, 128);
      container.m_7797_(instance.getOrCreateTag().m_128437_("SpatialStorage", 10));
      return container;
   }

   private void gainMastery(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 12 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
   }

   public boolean canCopy(ManasSkillInstance instance) {
      if (!instance.isTemporarySkill() && instance.getMastery() >= 0) {
         ManasSkill var3 = instance.getSkill();
         if (!(var3 instanceof Skill)) {
            return false;
         } else {
            Skill skill = (Skill)var3;
            return skill.getType().equals(Skill.SkillType.COMMON) || skill.getType().equals(Skill.SkillType.EXTRA) || skill.getType().equals(Skill.SkillType.INTRINSIC);
         }
      } else {
         return false;
      }
   }
}
