package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.entity.magic.misc.MadOgreOrbsEntity;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.Iterator;
import java.util.List;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class BerserkSkill extends Skill {
   public BerserkSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 20000.0D;
   }

   public double learningCost() {
      return 5000.0D;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.berserk.rage");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.berserk.mad_ogre");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 300.0D;
         break;
      case 2:
         var10000 = 5000.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      return this.canTick(instance, entity);
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return entity.m_21023_((MobEffect)TensuraMobEffects.MAD_OGRE.get());
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.isMastered(entity)) {
         List<MadOgreOrbsEntity> list = entity.m_9236_().m_6443_(MadOgreOrbsEntity.class, entity.m_20191_(), (living) -> {
            return living.m_20202_() == entity;
         });
         if (!list.isEmpty()) {
            return;
         }

         this.summonOrbs(entity, entity.m_9236_(), instance);
      } else if (entity.m_21023_((MobEffect)TensuraMobEffects.RAMPAGE.get())) {
         CompoundTag tag = instance.getOrCreateTag();
         int time = tag.m_128451_("activatedTimes");
         if (time % 6 == 0) {
            this.addMasteryPoint(instance, entity);
         }

         tag.m_128405_("activatedTimes", time + 1);
      }

   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      if (this.canTick(instance, entity)) {
         entity.m_21195_((MobEffect)TensuraMobEffects.MAD_OGRE.get());
      }

   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity living, LivingHurtEvent e) {
      if (this.isInSlot(living)) {
         if (DamageSourceHelper.isFireDamage(e.getSource())) {
            e.setAmount(e.getAmount() * 2.0F);
         }

         LivingEntity target = e.getEntity();
         if (DamageSourceHelper.isPhysicalAttack(e.getSource()) && !SkillHelper.outOfMagicule(living, 200.0D)) {
            target.m_6469_(DamageSource.m_19370_(living).m_19389_().m_19383_(), e.getAmount());
            target.m_20254_(10);
            target.f_19802_ = 0;
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      if (instance.getMode() == 1) {
         if (entity.m_21023_((MobEffect)TensuraMobEffects.STRENGTHEN.get())) {
            entity.m_21195_((MobEffect)TensuraMobEffects.STRENGTHEN.get());
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12031_, SoundSource.PLAYERS, 0.5F, 0.5F);
         } else {
            if (SkillHelper.outOfMagicule(entity, instance)) {
               return;
            }

            int strength = instance.isMastered(entity) ? 9 : 4;
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.STRENGTHEN.get(), 6000, strength, false, false, true));
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 0.5F, 0.5F);
         }

      } else {
         if (!entity.m_21023_((MobEffect)TensuraMobEffects.MAD_OGRE.get())) {
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.RAMPAGE.get(), 600, 0, false, false));
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.MAD_OGRE.get(), 12000, instance.isMastered(entity) ? 1 : 0, false, false));
            this.addMasteryPoint(instance, entity, 5 + SkillUtils.getBonusMasteryPoint(instance, entity, 5));
            instance.setCoolDown(1200);
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215769_, SoundSource.PLAYERS, 0.5F, 0.5F);
            if (instance.isMastered(entity)) {
               this.summonOrbs(entity, level, instance);
            }
         } else if (!entity.m_6144_() && instance.isMastered(entity)) {
            if (instance.isMastered(entity)) {
               List<MadOgreOrbsEntity> list = entity.m_9236_().m_6443_(MadOgreOrbsEntity.class, entity.m_20191_(), (living) -> {
                  return living.m_20202_() == entity;
               });
               if (list.isEmpty()) {
                  MadOgreOrbsEntity orbs = this.summonOrbs(entity, entity.m_9236_(), instance);
                  orbs.shootOrbs(entity);
               } else {
                  Iterator var5 = list.iterator();

                  while(var5.hasNext()) {
                     MadOgreOrbsEntity orbs = (MadOgreOrbsEntity)var5.next();
                     orbs.shootOrbs(entity);
                  }
               }

               entity.m_21011_(InteractionHand.MAIN_HAND, true);
            }
         } else {
            entity.m_21195_((MobEffect)TensuraMobEffects.RAMPAGE.get());
            entity.m_21195_((MobEffect)TensuraMobEffects.MAD_OGRE.get());
            instance.setCoolDown(600);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

      }
   }

   public MadOgreOrbsEntity summonOrbs(LivingEntity entity, Level level, ManasSkillInstance instance) {
      MadOgreOrbsEntity orbs = new MadOgreOrbsEntity(level, entity);
      orbs.m_7998_(entity, true);
      orbs.m_20242_(true);
      orbs.m_7910_(1.25F);
      orbs.setDamage(100.0F);
      orbs.setExplosionRadius(3.0F);
      orbs.setMpCost(this.magiculeCost(entity, instance));
      orbs.setSkill(instance);
      orbs.m_21557_(true);
      orbs.m_146884_(entity.m_20182_());
      entity.m_9236_().m_7967_(orbs);
      return orbs;
   }
}
