package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class GuardianSkill extends Skill {
   protected static final UUID GUARDIAN = UUID.fromString("84eceda2-570c-11ee-8c99-0242ac120002");

   public GuardianSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 20000.0D;
   }

   public double learningCost() {
      return 100.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.guardian.grant");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.guardian.iron_wall");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 100.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!SkillHelper.outOfMagicule(entity, instance)) {
         Level level = entity.m_9236_();
         switch(instance.getMode()) {
         case 1:
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11671_, SoundSource.PLAYERS, 1.0F, 1.0F);
            List<LivingEntity> list = level.m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(25.0D), (living) -> {
               return !entity.m_7306_(living) && living.m_6084_() && living.m_7307_(entity);
            });
            if (list.isEmpty()) {
               return;
            }

            this.addMasteryPoint(instance, entity);
            Iterator var10 = list.iterator();

            while(var10.hasNext()) {
               LivingEntity target = (LivingEntity)var10.next();
               target.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.PROTECTION.get(), 3600, 0, false, false, true), entity);
               TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_175828_, 1.0D);
            }

            return;
         case 2:
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            AttributeInstance armor = entity.m_21051_(Attributes.f_22284_);
            AttributeInstance knockBack;
            if (armor != null && armor.m_22111_(GUARDIAN) != null) {
               armor.m_22127_(GUARDIAN);
               knockBack = entity.m_21051_(Attributes.f_22278_);
               if (knockBack != null) {
                  knockBack.m_22127_(GUARDIAN);
               }

               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11665_, SoundSource.PLAYERS, 1.0F, 1.0F);
            } else {
               if (armor != null) {
                  float amount = instance.isMastered(entity) ? 40.0F : 15.0F;
                  AttributeModifier modifier = new AttributeModifier(GUARDIAN, "Guardian", (double)amount, Operation.ADDITION);
                  armor.m_22125_(modifier);
               }

               knockBack = entity.m_21051_(Attributes.f_22278_);
               float knockAmount = instance.isMastered(entity) ? 1.0F : 0.4F;
               AttributeModifier attributemodifier = new AttributeModifier(GUARDIAN, "Guardian", (double)knockAmount, Operation.ADDITION);
               if (knockBack != null && !knockBack.m_22109_(attributemodifier)) {
                  knockBack.m_22125_(attributemodifier);
               }

               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11671_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         }

      }
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance armor = entity.m_21051_(Attributes.f_22284_);
      if (armor != null) {
         armor.m_22127_(GUARDIAN);
      }

      AttributeInstance attributeInstance = entity.m_21051_(Attributes.f_22278_);
      if (attributeInstance != null) {
         attributeInstance.m_22127_(GUARDIAN);
      }

      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11665_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }

   public static boolean onSubordinateHurt(LivingHurtEvent event) {
      LivingEntity entity = event.getEntity();
      if (event.getSource().m_19378_()) {
         return false;
      } else {
         DamageSource var3 = event.getSource();
         if (var3 instanceof TensuraDamageSource) {
            TensuraDamageSource source = (TensuraDamageSource)var3;
            if ((double)source.getIgnoreBarrier() >= 1.5D) {
               return false;
            }
         }

         LivingEntity var5 = SkillHelper.getSubordinateOwner(entity);
         if (var5 instanceof Player) {
            Player player = (Player)var5;
            if (event.getSource().m_7639_() == player) {
               return false;
            } else if (isGuardianToggled(player)) {
               player.m_6469_(DamageSourceHelper.turnTensura(event.getSource()).setNoKnock(), event.getAmount());
               player.m_5661_(Component.m_237110_("tensura.skill.guardian.substitution_notification", new Object[]{event.getAmount(), entity.m_7755_()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), true);
               event.setCanceled(true);
               return true;
            } else {
               return false;
            }
         } else {
            return false;
         }
      }
   }

   public static boolean isGuardianToggled(LivingEntity player) {
      return (Boolean)SkillAPI.getSkillsFrom(player).getSkill((ManasSkill)UniqueSkills.GUARDIAN.get()).map(ManasSkillInstance::isToggled).orElse(false);
   }
}
