package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.event.SkillPlunderEvent;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import java.util.Optional;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingAttackEvent;

public class PrideSkill extends Skill {
   public PrideSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 100000.0D;
   }

   public int getMaxMastery() {
      return 1500;
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         LivingEntity entity = event.getEntity();
         if (!instance.onCoolDown()) {
            if (this.isInSlot(entity)) {
               DamageSource damageSource = event.getSource();
               if (!damageSource.m_19378_()) {
                  if (damageSource.m_7639_() == null || !damageSource.m_7639_().m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER)) {
                     if (damageSource instanceof TensuraDamageSource) {
                        TensuraDamageSource source = (TensuraDamageSource)damageSource;
                        if (!((double)source.getIgnoreBarrier() >= 1.75D)) {
                           ManasSkillInstance targetInstance = source.getSkill();
                           if (targetInstance != null && !targetInstance.isTemporarySkill() && targetInstance.getSkill() != this) {
                              Entity var8 = source.m_7639_();
                              if (var8 instanceof Player) {
                                 Player player = (Player)var8;
                                 if (player.m_150110_().f_35934_) {
                                    return;
                                 }
                              }

                              ManasSkill skill = targetInstance.getSkill();
                              int chance = this.copyChance(entity, instance, skill);
                              if (entity.m_217043_().m_188503_(100) >= chance) {
                                 if (chance != 0 && !SkillUtils.hasSkill(entity, skill)) {
                                    this.addMasteryPoint(instance, entity);
                                 }

                                 if (entity instanceof Player) {
                                    Player player = (Player)entity;
                                    player.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
                                 }

                              } else {
                                 SkillPlunderEvent plunderEvent = new SkillPlunderEvent(source.m_7639_(), entity, false, skill);
                                 if (!MinecraftForge.EVENT_BUS.post(plunderEvent)) {
                                    if (SkillUtils.learnSkill(entity, plunderEvent.getSkill(), instance.getRemoveTime())) {
                                       if (entity instanceof Player) {
                                          Player player = (Player)entity;
                                          player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{plunderEvent.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                                          TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
                                             Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(player).getSkill(plunderEvent.getSkill());
                                             if (!optional.isEmpty()) {
                                                if (cap.getSkillInSlot(0) == null) {
                                                   cap.setInstanceInSlot((ManasSkillInstance)optional.get(), 0);
                                                } else if (cap.getSkillInSlot(1) == null) {
                                                   cap.setInstanceInSlot((ManasSkillInstance)optional.get(), 1);
                                                } else if (cap.getSkillInSlot(2) == null) {
                                                   cap.setInstanceInSlot((ManasSkillInstance)optional.get(), 2);
                                                }

                                                TensuraSkillCapability.sync(player);
                                             }
                                          });
                                       }

                                       ManasSkill var11 = plunderEvent.getSkill();
                                       if (var11 instanceof TensuraSkill) {
                                          TensuraSkill tensuraSkill = (TensuraSkill)var11;
                                          double mastery = tensuraSkill.getObtainingEpCost() / 10000.0D;
                                          this.addMasteryPoint(instance, entity, (int)(mastery + (double)SkillUtils.getBonusMasteryPoint(instance, entity, (int)mastery)));
                                          instance.setCoolDown(Math.max((int)(360.0D * mastery), 1));
                                       } else {
                                          this.addMasteryPoint(instance, entity);
                                       }

                                       entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215778_, SoundSource.PLAYERS, 2.0F, 1.0F);
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            }
         }
      }
   }

   private int copyChance(LivingEntity owner, ManasSkillInstance pride, ManasSkill targetSkill) {
      if (this.cantCopy(targetSkill)) {
         return 0;
      } else {
         return pride.isMastered(owner) ? 100 : 25;
      }
   }

   private boolean cantCopy(ManasSkill manasSkill) {
      if (manasSkill instanceof SpiritualMagic) {
         return true;
      } else {
         boolean var10000;
         if (manasSkill instanceof Skill) {
            Skill skill = (Skill)manasSkill;
            if (skill.getType().equals(Skill.SkillType.ULTIMATE)) {
               var10000 = true;
               return var10000;
            }
         }

         var10000 = false;
         return var10000;
      }
   }
}
