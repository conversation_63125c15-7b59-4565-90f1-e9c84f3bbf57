package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.MobEffectEvent.Remove;

public class ReverserSkill extends Skill {
   public ReverserSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 30000.0D;
   }

   public double learningCost() {
      return 2000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return this.canTurnChaos(living);
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 3 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 3 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.reverser.reverse");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.reverser.buff");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.reverser.debuff");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   private boolean canTurnChaos(LivingEntity living) {
      if (TensuraEPCapability.isChaos(living)) {
         return true;
      } else {
         return TensuraEPCapability.isMajin(living) ? true : TensuraPlayerCapability.isTrueHero(living);
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      TensuraEPCapability.getFrom(entity).ifPresent((cap) -> {
         cap.setChaos(true);
      });
      TensuraEPCapability.sync(entity);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      TensuraEPCapability.getFrom(entity).ifPresent((cap) -> {
         cap.setChaos(false);
      });
      TensuraEPCapability.sync(entity);
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!SkillHelper.outOfMagicule(entity, instance)) {
         LivingEntity target = SkillHelper.getTargetingEntity(entity, 3.0D, false);
         Player player;
         boolean chaos;
         if (target != null) {
            if (target instanceof Player) {
               player = (Player)target;
               if (player.m_150110_().f_35934_) {
                  return;
               }
            }

            if (instance.getMode() == 1) {
               if (!instance.isMastered(entity)) {
                  if (entity instanceof Player) {
                     player = (Player)entity;
                     player.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  }

                  return;
               }

               if (this.canTurnChaos(target)) {
                  TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
                     if (cap.isChaos()) {
                        cap.setChaos(false);
                        TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_235898_, 2.0D);
                        entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11739_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     } else {
                        cap.setChaos(true);
                        TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.SOUL.get(), 2.0D);
                        entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11739_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     }

                  });
                  TensuraEPCapability.sync(target);
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
               } else if (entity instanceof Player) {
                  player = (Player)entity;
                  player.m_5661_(Component.m_237115_("tensura.targeting.not_allowed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
               }
            }

            chaos = instance.getMode() == 2 ? this.reverseDebuff(target) : this.reverseBuff(target);
            if (chaos) {
               this.addMasteryPoint(instance, entity);
               TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123749_, 2.0D);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

            entity.m_21011_(InteractionHand.MAIN_HAND, true);
         } else if (instance.getMode() == 2) {
            if (this.reverseDebuff(entity)) {
               this.addMasteryPoint(instance, entity);
               TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123749_, 2.0D);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

            entity.m_21011_(InteractionHand.MAIN_HAND, true);
         } else if (instance.getMode() == 1) {
            if (!this.canTurnChaos(entity)) {
               if (entity instanceof Player) {
                  player = (Player)entity;
                  player.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
               }

               return;
            }

            chaos = TensuraEPCapability.isChaos(entity);
            instance.setToggled(!chaos);
            if (chaos) {
               instance.onToggleOff(entity);
               TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_235898_, 2.0D);
            } else {
               instance.onToggleOn(entity);
               TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.SOUL.get(), 2.0D);
            }

            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11739_, SoundSource.PLAYERS, 1.0F, 1.0F);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
         }

      }
   }

   private boolean reverseDebuff(LivingEntity entity) {
      boolean success = false;
      List<MobEffectInstance> effects = List.copyOf(entity.m_21220_());
      Iterator var4 = effects.iterator();

      while(var4.hasNext()) {
         MobEffectInstance effectInstance = (MobEffectInstance)var4.next();
         if (!MinecraftForge.EVENT_BUS.post(new Remove(entity, effectInstance)) && effectInstance.m_19544_().m_19483_() == MobEffectCategory.HARMFUL) {
            List<MobEffect> list = this.debuffToBuff(effectInstance.m_19544_());
            if (!list.isEmpty()) {
               list.forEach((reversed) -> {
                  entity.m_7292_(new MobEffectInstance(reversed, effectInstance.m_19557_(), effectInstance.m_19564_(), effectInstance.m_19571_(), effectInstance.m_19572_(), effectInstance.m_19575_()));
               });
               success = true;
            }

            entity.m_21195_(effectInstance.m_19544_());
         }
      }

      return success;
   }

   private boolean reverseBuff(LivingEntity entity) {
      boolean success = false;
      List<MobEffectInstance> effects = List.copyOf(entity.m_21220_());
      Iterator var4 = effects.iterator();

      while(var4.hasNext()) {
         MobEffectInstance effectInstance = (MobEffectInstance)var4.next();
         if (!MinecraftForge.EVENT_BUS.post(new Remove(entity, effectInstance)) && effectInstance.m_19544_().m_19486_()) {
            List<MobEffect> list = this.buffToDebuff(effectInstance.m_19544_());
            if (!list.isEmpty()) {
               list.forEach((reversed) -> {
                  entity.m_7292_(new MobEffectInstance(reversed, effectInstance.m_19557_(), effectInstance.m_19564_(), effectInstance.m_19571_(), effectInstance.m_19572_(), effectInstance.m_19575_()));
               });
               entity.m_21195_(effectInstance.m_19544_());
               success = true;
            }
         }
      }

      return success;
   }

   private List<MobEffect> debuffToBuff(MobEffect mobEffect) {
      if (mobEffect.equals(MobEffects.f_19597_)) {
         return List.of(MobEffects.f_19596_);
      } else if (mobEffect.equals(TensuraMobEffects.PARALYSIS.get())) {
         return List.of(MobEffects.f_19596_);
      } else if (mobEffect.equals(MobEffects.f_19615_)) {
         return List.of(MobEffects.f_19605_);
      } else if (mobEffect.equals(MobEffects.f_19614_)) {
         return List.of(MobEffects.f_19605_);
      } else if (mobEffect.equals(TensuraMobEffects.CORROSION.get())) {
         return List.of(MobEffects.f_19605_);
      } else if (mobEffect.equals(TensuraMobEffects.FATAL_POISON.get())) {
         return List.of(MobEffects.f_19605_);
      } else if (mobEffect.equals(TensuraMobEffects.BURDEN.get())) {
         return List.of(MobEffects.f_19591_, MobEffects.f_19596_, MobEffects.f_19603_);
      } else if (mobEffect.equals(TensuraMobEffects.FRAGILITY.get())) {
         return List.of(MobEffects.f_19606_);
      } else if (mobEffect.equals(MobEffects.f_19599_)) {
         return List.of(MobEffects.f_19598_);
      } else if (mobEffect.equals(MobEffects.f_19612_)) {
         return List.of(MobEffects.f_19618_);
      } else if (mobEffect.equals(MobEffects.f_19590_)) {
         return List.of(MobEffects.f_19621_);
      } else {
         return mobEffect.equals(MobEffects.f_19613_) ? List.of(MobEffects.f_19600_) : List.of();
      }
   }

   private List<MobEffect> buffToDebuff(MobEffect mobEffect) {
      if (mobEffect.equals(MobEffects.f_19596_)) {
         return List.of(MobEffects.f_19597_);
      } else if (mobEffect.equals(MobEffects.f_19605_)) {
         return List.of(MobEffects.f_19615_);
      } else if (mobEffect.equals(MobEffects.f_19591_)) {
         return List.of((MobEffect)TensuraMobEffects.BURDEN.get());
      } else if (mobEffect.equals(MobEffects.f_19603_)) {
         return List.of((MobEffect)TensuraMobEffects.BURDEN.get());
      } else if (mobEffect.equals(MobEffects.f_19606_)) {
         return List.of((MobEffect)TensuraMobEffects.FRAGILITY.get());
      } else if (mobEffect.equals(MobEffects.f_19598_)) {
         return List.of(MobEffects.f_19599_);
      } else if (mobEffect.equals(MobEffects.f_19618_)) {
         return List.of(MobEffects.f_19612_);
      } else if (mobEffect.equals(MobEffects.f_19621_)) {
         return List.of(MobEffects.f_19590_);
      } else {
         return mobEffect.equals(MobEffects.f_19600_) ? List.of(MobEffects.f_19613_) : List.of();
      }
   }
}
