package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;

public class RoyalBeastSkill extends Skill {
   public RoyalBeastSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 20000.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 0.0D;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      return this.canTick(instance, entity);
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      MobEffectInstance ogre = entity.m_21124_((MobEffect)TensuraMobEffects.BEAST_TRANSFORMATION.get());
      return ogre != null && ogre.m_19564_() == 1;
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 6 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      if (this.canTick(instance, entity)) {
         entity.m_21195_((MobEffect)TensuraMobEffects.BEAST_TRANSFORMATION.get());
      }

   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      MobEffectInstance effectInstance = entity.m_21124_((MobEffect)TensuraMobEffects.MAGIC_INTERFERENCE.get());
      Player player;
      if (effectInstance != null && effectInstance.m_19564_() >= 1) {
         if (entity instanceof Player) {
            player = (Player)entity;
            player.m_5661_(Component.m_237115_("tensura.skill.magic_interference").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }

      } else {
         if (!entity.m_21023_((MobEffect)TensuraMobEffects.BEAST_TRANSFORMATION.get())) {
            if (SkillHelper.outOfMagicule(entity, instance)) {
               return;
            }

            instance.setCoolDown(1200);
            entity.m_21153_(entity.m_21233_());
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215769_, SoundSource.PLAYERS, 1.0F, 1.0F);
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.BEAST_TRANSFORMATION.get(), this.isMastered(instance, entity) ? 7200 : 3600, 1, false, false, false));
            if (entity instanceof Player) {
               player = (Player)entity;
               TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                  boolean shouldSync = false;
                  double maxMagicule = entity.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
                  if (cap.getMagicule() != maxMagicule) {
                     cap.setMagicule(maxMagicule);
                     shouldSync = true;
                  }

                  double maxAura = entity.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get());
                  if (cap.getAura() != maxAura) {
                     cap.setMagicule(maxAura);
                     shouldSync = true;
                  }

                  if (shouldSync) {
                     TensuraPlayerCapability.sync(player);
                  }

               });
            }

            TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123812_);
            TensuraParticleHelper.spawnServerParticles(entity.f_19853_, (ParticleOptions)TensuraParticles.LIGHTNING_SPARK.get(), entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.5D, true);
            TensuraParticleHelper.spawnServerParticles(entity.f_19853_, (ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get(), entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.5D, true);
         } else {
            entity.m_21195_((MobEffect)TensuraMobEffects.BEAST_TRANSFORMATION.get());
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

      }
   }
}
