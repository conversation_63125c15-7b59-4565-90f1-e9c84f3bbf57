package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.ISpatialStorage;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.menu.ResearcherEnchantmentMenu;
import com.github.manasmods.tensura.menu.ResearcherSpatialStorageMenu;
import com.github.manasmods.tensura.menu.container.SpatialStorageContainer;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.ClientboundSpatialStorageOpenPacket;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import java.util.Map;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.ContainerLevelAccess;
import net.minecraft.world.inventory.InventoryMenu;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.player.PlayerContainerEvent.Open;
import net.minecraftforge.network.NetworkHooks;
import net.minecraftforge.network.PacketDistributor;
import org.jetbrains.annotations.NotNull;

public class GodlyCraftsmanSkill extends Skill implements ISpatialStorage {
   public GodlyCraftsmanSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 60000.0D;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return SkillUtils.isSkillMastered(entity, (ManasSkill)UniqueSkills.RESEARCHER.get());
   }

   public int getMasteryOnEPAcquirement(Player entity) {
      return 0;
   }

   public double learningCost() {
      return 10000.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      ResearcherSkill.addSelectedEnchantments(entity, Map.of(), true, this);
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         if (player.m_6047_() && !(player.f_36096_ instanceof ResearcherEnchantmentMenu)) {
            player.f_36096_.m_6877_(player);
            player.m_6330_(SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
            NetworkHooks.openScreen(player, new SimpleMenuProvider((pContainerId, pPlayerInventory, pPlayer) -> {
               return new ResearcherEnchantmentMenu(pContainerId, pPlayerInventory, ContainerLevelAccess.m_39289_(player.f_19853_, player.m_20183_()), this);
            }, Component.m_237119_()), (buf) -> {
               buf.m_130085_(SkillUtils.getSkillId(this));
            });
            return;
         }
      }

      this.openSpatialStorage(entity, instance);
   }

   @NotNull
   public SpatialStorageContainer getSpatialStorage(ManasSkillInstance instance) {
      SpatialStorageContainer container = new SpatialStorageContainer(80, 256);
      container.m_7797_(instance.getOrCreateTag().m_128437_("SpatialStorage", 10));
      return container;
   }

   public void openSpatialStorage(LivingEntity entity, ManasSkillInstance instance) {
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         player.m_9217_();
         if (player.f_36096_ instanceof InventoryMenu) {
            player.m_6330_(SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
         } else {
            player.m_6330_(SoundEvents.f_12088_, SoundSource.PLAYERS, 1.0F, 1.0F);
            player.f_36096_.m_6877_(player);
         }

         ManasSkill skill = instance.getSkill();
         SpatialStorageContainer container = this.getSpatialStorage(instance);
         TensuraNetwork.INSTANCE.send(PacketDistributor.PLAYER.with(() -> {
            return player;
         }), new ClientboundSpatialStorageOpenPacket(player.m_19879_(), player.f_8940_, container.m_6643_(), container.m_6893_(), SkillUtils.getSkillId(skill), (byte)3));
         player.f_36096_ = new ResearcherSpatialStorageMenu(player.f_8940_, player.m_150109_(), player, container, skill);
         player.m_143399_(player.f_36096_);
         MinecraftForge.EVENT_BUS.post(new Open(player, player.f_36096_));
      }

   }
}
