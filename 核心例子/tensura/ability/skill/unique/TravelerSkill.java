package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.extra.SpatialMotionSkill;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.entity.magic.projectile.SpatialArrowProjectile;
import com.github.manasmods.tensura.menu.SpatialMenu;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.mojang.math.Vector3f;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import net.minecraftforge.network.NetworkHooks;

public class TravelerSkill extends Skill {
   public TravelerSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 25000.0D;
   }

   public double learningCost() {
      return 5000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public int modes() {
      return 5;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      int var10000;
      if (reverse) {
         switch(instance.getMode()) {
         case 1:
            var10000 = instance.isMastered(entity) ? 5 : 4;
            break;
         case 2:
            var10000 = 1;
            break;
         case 3:
            var10000 = 2;
            break;
         case 4:
            var10000 = 3;
            break;
         case 5:
            var10000 = 4;
            break;
         default:
            var10000 = 0;
         }

         return var10000;
      } else {
         switch(instance.getMode()) {
         case 1:
            var10000 = 2;
            break;
         case 2:
            var10000 = 3;
            break;
         case 3:
            var10000 = 4;
            break;
         case 4:
            var10000 = instance.isMastered(entity) ? 5 : 1;
            break;
         default:
            var10000 = 1;
         }

         return var10000;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.traveler.instant_motion");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.traveler.teleport");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.sniper.spatial");
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill.mode.traveler.stardust_arrow");
         break;
      case 5:
         var10000 = Component.m_237115_("tensura.skill.mode.traveler.stardust_rain");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public String modeLearningId(int mode) {
      String var10000;
      switch(mode) {
      case 4:
         var10000 = "stardustArrow";
         break;
      case 5:
         var10000 = "stardustRain";
         break;
      default:
         var10000 = "None";
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
      case 2:
         var10000 = 100.0D;
         break;
      case 3:
      default:
         var10000 = 0.0D;
         break;
      case 4:
         var10000 = 150.0D;
         break;
      case 5:
         var10000 = 5000.0D;
      }

      return var10000;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 4:
         var10000 = 150.0D;
         break;
      case 5:
         var10000 = 5000.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity living, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (!SkillUtils.isSkillToggled(living, (ManasSkill)ExtraSkills.SPATIAL_DOMINATION.get())) {
            if (!SkillUtils.isSkillToggled(living, (ManasSkill)ExtraSkills.SPATIAL_MANIPULATION.get())) {
               if (DamageSourceHelper.isSpatialDamage(e.getSource())) {
                  e.setAmount(e.getAmount() * 2.0F);
               }

            }
         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      CompoundTag tag;
      int learnPoint;
      Player player;
      Player player;
      switch(instance.getMode()) {
      case 1:
         if (MobEffectHelper.noTeleportation(entity)) {
            if (entity instanceof Player) {
               player = (Player)entity;
               player.m_5661_(Component.m_237115_("tensura.skill.spatial_blockade").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
            }

            return;
         }

         BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 200.0D);
         BlockPos pos = result.m_82425_().m_121945_(result.m_82434_());
         if (level.m_8055_(pos).m_60713_((Block)TensuraBlocks.LABYRINTH_BARRIER_BLOCK.get())) {
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
            return;
         }

         if (SkillHelper.outOfMagicule(entity, 5.0D * Math.sqrt(entity.m_20275_((double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_())))) {
            return;
         }

         SpatialMotionSkill.warp(entity, (double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_());
         this.addMasteryPoint(instance, entity);
         instance.setCoolDown(instance.isMastered(entity) ? 2 : 5);
         break;
      case 2:
         if (MobEffectHelper.noTeleportation(entity)) {
            if (entity instanceof Player) {
               player = (Player)entity;
               player.m_5661_(Component.m_237115_("tensura.skill.spatial_blockade").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
            }

            return;
         }

         if (entity instanceof ServerPlayer) {
            ServerPlayer serverPlayer = (ServerPlayer)entity;
            if (level.m_46472_() == TensuraDimensions.LABYRINTH) {
               serverPlayer.m_6330_(SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
               serverPlayer.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
            } else {
               NetworkHooks.openScreen(serverPlayer, new SimpleMenuProvider(SpatialMenu::new, Component.m_237119_()), (buf) -> {
                  buf.writeBoolean(false);
               });
               serverPlayer.m_6330_(SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         }

         entity.m_21195_((MobEffect)TensuraMobEffects.WARPING.get());
      case 3:
      default:
         break;
      case 4:
         if (SkillHelper.outOfEachEP(entity, this.magiculeCost(entity, instance))) {
            return;
         }

         tag = instance.getOrCreateTag();
         learnPoint = tag.m_128451_("stardustArrow");
         if (learnPoint < 100) {
            tag.m_128405_("stardustArrow", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
            if (entity instanceof Player) {
               player = (Player)entity;
               if (tag.m_128451_("stardustArrow") >= 100) {
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(4)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               } else {
                  instance.setCoolDown(10);
                  SkillUtils.learningFailPenalty(entity);
                  player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(4)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
               }

               player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

            instance.markDirty();
            return;
         }

         SpatialArrowProjectile arrow = new SpatialArrowProjectile(level, entity);
         arrow.setSkill(instance);
         arrow.setDamage(30.0F);
         arrow.setMpCost(this.magiculeCost(entity, instance));
         if (!this.warpShotArrow(entity, arrow)) {
            arrow.m_6034_(entity.m_20185_(), entity.m_20188_() - 0.20000000298023224D, entity.m_20189_());
            Vector3f vector3f = new Vector3f(entity.m_20252_(2.0F));
            arrow.m_6686_((double)vector3f.m_122239_(), (double)vector3f.m_122260_(), (double)vector3f.m_122269_(), 2.0F, 0.0F);
         }

         level.m_7967_(arrow);
         level.m_6263_((Player)null, arrow.m_20185_(), arrow.m_20186_(), arrow.m_20189_(), SoundEvents.f_11687_, SoundSource.PLAYERS, 1.0F, 1.0F);
         break;
      case 5:
         if (SkillHelper.outOfEachEP(entity, this.magiculeCost(entity, instance))) {
            return;
         }

         tag = instance.getOrCreateTag();
         learnPoint = tag.m_128451_("stardustRain");
         if (learnPoint < 100) {
            tag.m_128405_("stardustRain", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
            if (entity instanceof Player) {
               player = (Player)entity;
               if (tag.m_128451_("stardustRain") >= 100) {
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(5)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               } else {
                  instance.setCoolDown(10);
                  SkillUtils.learningFailPenalty(entity);
                  player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(5)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
               }

               player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

            instance.markDirty();
            return;
         }

         LivingEntity target = SkillHelper.getTargetingEntity(entity, 20.0D, false);
         if (target == null) {
            return;
         }

         Vec3 pos = target.m_20182_().m_82520_(0.0D, (double)target.m_20192_(), 0.0D);
         int arrowAmount = 12;

         for(int i = 0; i < arrowAmount; ++i) {
            Vec3 arrowPos = pos.m_82549_((new Vec3(0.0D, Math.random() - 0.5D, 0.6D)).m_82541_().m_82490_((double)(target.m_20205_() + 6.0F)).m_82524_(360.0F * (float)i * 0.017453292F / (float)arrowAmount));
            SpatialArrowProjectile arrow = new SpatialArrowProjectile(level, entity);
            arrow.setSkill(instance);
            arrow.setSpeed(1.0F);
            arrow.m_146884_(arrowPos);
            arrow.shootFromRot(pos.m_82546_(arrowPos).m_82541_());
            arrow.setLife(50);
            arrow.setDamage(30.0F);
            arrow.setMpCost((double)(5000.0F / (float)arrowAmount));
            level.m_7967_(arrow);
            level.m_6263_((Player)null, arrow.m_20185_(), arrow.m_20186_(), arrow.m_20189_(), SoundEvents.f_11687_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }
      }

   }

   private boolean warpShotArrow(LivingEntity owner, SpatialArrowProjectile arrow) {
      if (!owner.m_6144_()) {
         return false;
      } else {
         Entity entity = SkillHelper.getTargetingEntity(owner, 30.0D, 0.0D, false, false);
         if (entity == null) {
            return false;
         } else {
            arrow.shootFromBehind(entity, 2.0F, 0.0F);
            return true;
         }
      }
   }
}
