package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.intrinsic.CharmSkill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Objects;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.Stats;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class GreedSkill extends Skill {
   protected static final UUID GREED = UUID.fromString("b75a5836-984a-11ee-b9d1-0242ac120002");

   public GreedSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 100000.0D;
   }

   public int getMaxMastery() {
      return 1500;
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 3 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 3 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.greed.spiritual");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.greed.flare");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.greed.death");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return instance.getMode() == 3 ? 1000.0D : 0.0D;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      if (!tag.m_128441_("target") && tag.m_128451_("heldSeconds") == 0) {
         return false;
      } else {
         return !this.isHeld(entity);
      }
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      tag.m_128473_("target");
      tag.m_128405_("heldSeconds", 0);
      tag.m_128405_("targetDesire", 0);
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity attacker, LivingHurtEvent e) {
      AttributeInstance attack = attacker.m_21051_(Attributes.f_22281_);
      if (attack != null) {
         AttributeModifier modifier = attack.m_22111_(GREED);
         if (modifier != null) {
            DamageSource source = e.getSource();
            if (source.m_7639_() == attacker) {
               if (DamageSourceHelper.isPhysicalAttack(source)) {
                  if (SkillHelper.outOfMagicule(attacker, modifier.m_22218_() * 10.0D)) {
                     attack.m_22127_(GREED);
                  }

               }
            }
         }
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      Level level = entity.m_9236_();
      CompoundTag tag;
      int second;
      LivingEntity target;
      Player player;
      switch(instance.getMode()) {
      case 1:
         if (entity.m_6144_()) {
            return false;
         }

         tag = instance.getOrCreateTag();
         second = tag.m_128451_("heldSeconds");
         target = (LivingEntity)SkillHelper.getTargetingEntity(LivingEntity.class, entity, 30.0D, 0.5D, true, true);
         if (target != null && (!tag.m_128441_("target") || Objects.equals(tag.m_128342_("target"), target.m_20148_()))) {
            if (!CharmSkill.canMindControl(target, level) && entity instanceof Player) {
               player = (Player)entity;
               player.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
               return false;
            }

            if (heldTicks % 100 == 0 && heldTicks > 0) {
               this.addMasteryPoint(instance, entity);
            }

            int controlling = target instanceof Player ? 60 : 90;
            double distance = (double)target.m_20270_(entity);
            if (distance < 5.0D) {
               controlling -= 30;
            }

            if (distance > 20.0D) {
               controlling += 30;
            }

            if (heldTicks % 20 == 0) {
               tag.m_128405_("heldSeconds", second + 1);
            }

            int desire = tag.m_128451_("targetDesire");
            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.time_held.max", new Object[]{second, controlling - desire}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), true);
               player.m_6330_(SoundEvents.f_11867_, SoundSource.PLAYERS, 0.2F, 1.0F);
            }

            if (second >= controlling - desire) {
               TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
                  if (!Objects.equals(cap.getTemporaryOwner(), entity.m_20148_())) {
                     cap.setTemporaryOwner(entity.m_20148_());
                     if (entity instanceof Player) {
                        Player player = (Player)entity;
                        if (target instanceof TamableAnimal) {
                           TamableAnimal animal = (TamableAnimal)target;
                           animal.m_21828_(player);
                        } else if (target instanceof TensuraHorseEntity) {
                           TensuraHorseEntity horse = (TensuraHorseEntity)target;
                           horse.m_30637_(player);
                        }
                     }

                     TensuraEPCapability.sync(target);
                     entity.m_21011_(InteractionHand.MAIN_HAND, true);
                     TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123750_);
                     level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 0.5F, 1.0F);
                  }

               });
               tag.m_128405_("targetDesire", 0);
               tag.m_128405_("heldSeconds", 0);
               tag.m_128473_("target");
            }
         } else if (heldTicks % 20 == 0) {
            tag.m_128405_("heldSeconds", Math.max(0, second - 1));
            if (tag.m_128451_("heldSeconds") <= 0) {
               tag.m_128473_("target");
            }

            if (entity instanceof Player) {
               player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.time_held", new Object[]{second}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), true);
            }
         }
         break;
      case 3:
         tag = instance.getOrCreateTag();
         second = tag.m_128451_("heldSeconds");
         target = (LivingEntity)SkillHelper.getTargetingEntity(LivingEntity.class, entity, 5.0D, 0.5D, true, true);
         if (target == null || tag.m_128441_("target") && !Objects.equals(tag.m_128342_("target"), target.m_20148_())) {
            if (heldTicks % 20 == 0) {
               tag.m_128405_("heldSeconds", Math.max(0, second - 1));
               if (tag.m_128451_("heldSeconds") <= 0) {
                  tag.m_128473_("target");
               }

               if (entity instanceof Player) {
                  player = (Player)entity;
                  player.m_5661_(Component.m_237110_("tensura.skill.time_held", new Object[]{second}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), true);
               }
            }
         } else {
            if (target instanceof Player) {
               player = (Player)target;
               if (player.m_150110_().f_35934_) {
                  return false;
               }
            }

            if (heldTicks % 20 == 0) {
               tag.m_128405_("heldSeconds", second + 1);
            }

            if (entity instanceof ServerPlayer) {
               ServerPlayer player = (ServerPlayer)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.time_held.max", new Object[]{second, 10}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), true);
               TensuraParticleHelper.addServerParticlesAroundSelfToOnePlayer(player, target, ParticleTypes.f_123790_, 1.0D);
            }

            if (second >= 10) {
               tag.m_128405_("heldSeconds", 0);
               tag.m_128473_("target");
               if (SkillHelper.outOfMagicule(entity, instance)) {
                  return false;
               }

               this.addMasteryPoint(instance, entity);
               if (target.m_6469_(this.sourceWithMP(TensuraDamageSources.deathWish(entity), entity, instance), target.m_21233_() * 10.0F)) {
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123765_);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123808_);
                  return false;
               }
            }
         }
      }

      return true;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      CompoundTag tag = instance.getOrCreateTag();
      LivingEntity target;
      Player player;
      switch(instance.getMode()) {
      case 1:
         target = SkillHelper.getTargetingEntity(entity, 30.0D, false);
         if (target == null) {
            return;
         }

         UUID uuid = entity.m_20148_();
         if (entity.m_6144_()) {
            TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
               if (Objects.equals(cap.getTemporaryOwner(), uuid)) {
                  cap.setTemporaryOwner((UUID)null);
                  UUID owner = cap.getPermanentOwner();
                  if (target instanceof TensuraTamableEntity) {
                     TensuraTamableEntity tamable = (TensuraTamableEntity)target;
                     tamable.resetOwner(owner);
                  } else if (target instanceof TensuraHorseEntity) {
                     TensuraHorseEntity horse = (TensuraHorseEntity)target;
                     horse.resetOwner(owner);
                  } else if (target instanceof TamableAnimal) {
                     TamableAnimal animal = (TamableAnimal)target;
                     animal.m_21816_(owner);
                     if (owner == null) {
                        animal.m_7105_(false);
                     }
                  }

                  SkillHelper.setWander(target);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123792_);
                  TensuraEPCapability.sync(target);
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
               }

            });
            return;
         }

         if (target instanceof Player) {
            Player player = (Player)target;
            if (player.m_150110_().f_35934_) {
               return;
            }
         }

         if (target.m_7307_(entity)) {
            return;
         }

         if (target instanceof Player && TensuraGameRules.noPlayerMindControl(level)) {
            return;
         }

         int desire = 0;
         if (target instanceof ServerPlayer) {
            ServerPlayer player = (ServerPlayer)target;
            desire = player.m_8951_().m_13015_(Stats.f_12988_.m_12902_(Stats.f_12941_)) / 10;
         }

         tag.m_128405_("targetDesire", desire);
         tag.m_128362_("target", target.m_20148_());
         tag.m_128405_("heldSeconds", 0);
         break;
      case 2:
         target = SkillHelper.getTargetingEntity(entity, 20.0D, false);
         double multiplier;
         double boost;
         AttributeInstance attack;
         AttributeModifier modifier;
         AttributeModifier attackModifier;
         Race race;
         if (target != null && target.m_6084_()) {
            if (target.m_7307_(entity)) {
               race = TensuraPlayerCapability.getRace(target);
               multiplier = race == null ? 2.0D : race.getSpiritualHealthMultiplier();
               boost = TensuraEPCapability.getSpiritualHealth(target) / multiplier / 10.0D;
               if (SkillHelper.outOfMagicule(entity, boost * 20.0D)) {
                  return;
               }

               attack = target.m_21051_(Attributes.f_22281_);
               if (attack == null) {
                  return;
               }

               modifier = attack.m_22111_(GREED);
               if (modifier != null) {
                  attack.m_22127_(GREED);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_175828_);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11738_, SoundSource.PLAYERS, 1.0F, 1.0F);
               } else {
                  attackModifier = new AttributeModifier(GREED, "Greed Attack", boost, Operation.ADDITION);
                  attack.m_22125_(attackModifier);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_175829_);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11736_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            } else {
               if (target instanceof Player) {
                  player = (Player)target;
                  if (player.m_150110_().f_35934_) {
                     return;
                  }
               }

               race = TensuraPlayerCapability.getRace(entity);
               multiplier = race == null ? 2.0D : race.getSpiritualHealthMultiplier();
               boost = TensuraEPCapability.getSpiritualHealth(entity) / multiplier / 10.0D;
               if (SkillHelper.outOfMagicule(entity, boost * 20.0D)) {
                  return;
               }

               if (target.m_6469_(this.sourceWithMP(TensuraDamageSources.deathWish(entity), entity, instance), (float)boost)) {
                  this.addMasteryPoint(instance, entity);
                  instance.setCoolDown(120);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123765_);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123808_);
               }
            }
         } else {
            race = TensuraPlayerCapability.getRace(entity);
            multiplier = race == null ? 2.0D : race.getSpiritualHealthMultiplier();
            boost = TensuraEPCapability.getSpiritualHealth(entity) / multiplier / 5.0D;
            if (SkillHelper.outOfMagicule(entity, boost * 10.0D)) {
               return;
            }

            attack = entity.m_21051_(Attributes.f_22281_);
            if (attack == null) {
               return;
            }

            modifier = attack.m_22111_(GREED);
            if (modifier != null) {
               attack.m_22127_(GREED);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11738_, SoundSource.PLAYERS, 1.0F, 1.0F);
            } else {
               attackModifier = new AttributeModifier(GREED, "Greed Attack", boost, Operation.ADDITION);
               attack.m_22125_(attackModifier);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11736_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         }
         break;
      case 3:
         target = SkillHelper.getTargetingEntity(entity, 30.0D, false);
         if (target == null) {
            return;
         }

         if (target instanceof Player) {
            player = (Player)target;
            if (player.m_150110_().f_35934_) {
               return;
            }
         }

         if (target.m_7307_(entity)) {
            return;
         }

         if (target instanceof Player && TensuraGameRules.noPlayerMindControl(level)) {
            return;
         }

         tag.m_128362_("target", target.m_20148_());
         tag.m_128405_("heldSeconds", 0);
      }

   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance attack = entity.m_21051_(Attributes.f_22281_);
      if (attack != null) {
         AttributeModifier modifier = attack.m_22111_(GREED);
         if (modifier != null) {
            attack.m_22127_(GREED);
         }
      }

      CompoundTag tag = instance.getOrCreateTag();
      tag.m_128405_("targetDesire", 0);
      tag.m_128473_("target");
      tag.m_128405_("heldSeconds", 0);
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      CompoundTag tag = instance.getOrCreateTag();
      tag.m_128405_("targetDesire", 0);
      tag.m_128473_("target");
      tag.m_128405_("heldSeconds", 0);
   }
}
