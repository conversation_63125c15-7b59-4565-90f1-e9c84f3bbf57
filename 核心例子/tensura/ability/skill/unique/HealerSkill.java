package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.network.PacketDistributor;

public class HealerSkill extends Skill {
   public HealerSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 20000.0D;
   }

   public double learningCost() {
      return 3000.0D;
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      int var10000;
      if (reverse) {
         switch(instance.getMode()) {
         case 1:
            var10000 = instance.isMastered(entity) ? 3 : 2;
            break;
         case 2:
            var10000 = 1;
            break;
         case 3:
            var10000 = 2;
            break;
         default:
            var10000 = 0;
         }

         return var10000;
      } else {
         switch(instance.getMode()) {
         case 1:
            var10000 = 2;
            break;
         case 2:
            var10000 = instance.isMastered(entity) ? 3 : 1;
            break;
         default:
            var10000 = 1;
         }

         return var10000;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.healer.heal");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.healer.virus");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.healer.plague");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 2:
         var10000 = 100.0D;
         break;
      case 3:
         var10000 = 300.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!SkillHelper.outOfMagicule(entity, instance)) {
         Level level = entity.m_9236_();
         LivingEntity target;
         switch(instance.getMode()) {
         case 1:
            target = SkillHelper.getTargetingEntity(entity, 5.0D, false);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
            float healingHP;
            double lackedMana;
            double healingSpiritual;
            double lackedSpiritual;
            double lackedMP;
            int cost;
            if (target != null && entity.m_6144_()) {
               this.addMasteryPoint(instance, entity);
               cost = instance.isMastered(entity) ? 40 : 80;
               healingHP = target.m_21233_() - target.m_21223_();
               lackedMana = SkillHelper.outOfMagiculeStillConsume(entity, (double)((int)(healingHP * (float)cost)));
               if (lackedMana > 0.0D) {
                  healingHP = (float)((double)healingHP - lackedMana / (double)cost);
               }

               target.m_5634_(healingHP);
               if (this.isMastered(instance, entity)) {
                  healingSpiritual = TensuraEPCapability.getSpiritualHealth(target);
                  lackedSpiritual = target.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get()) - healingSpiritual;
                  lackedMP = SkillHelper.outOfMagiculeStillConsume(entity, (double)((int)(lackedSpiritual * 60.0D)));
                  if (lackedMP > 0.0D) {
                     lackedSpiritual -= lackedMP / 60.0D;
                  }

                  TensuraEPCapability.setSpiritualHealth(target, healingSpiritual + lackedSpiritual);
               }

               TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123750_, 1.0D);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
            } else {
               entity.m_21195_((MobEffect)TensuraMobEffects.INFECTION.get());
               cost = instance.isMastered(entity) ? 40 : 80;
               healingHP = entity.m_21233_() - entity.m_21223_();
               lackedMana = SkillHelper.outOfMagiculeStillConsume(entity, (double)((int)(healingHP * (float)cost)));
               if (lackedMana > 0.0D) {
                  healingHP = (float)((double)healingHP - lackedMana / (double)cost);
               }

               entity.m_5634_(healingHP);
               if (this.isMastered(instance, entity)) {
                  healingSpiritual = TensuraEPCapability.getSpiritualHealth(entity);
                  lackedSpiritual = entity.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get()) - healingSpiritual;
                  lackedMP = SkillHelper.outOfMagiculeStillConsume(entity, (double)((int)(lackedSpiritual * 50.0D)));
                  if (lackedMP > 0.0D) {
                     lackedSpiritual -= lackedMP / 50.0D;
                  }

                  TensuraEPCapability.setSpiritualHealth(entity, healingSpiritual + lackedSpiritual);
               }

               TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123750_, 1.0D);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
            break;
         case 2:
            target = SkillHelper.getTargetingEntity(entity, 5.0D, false);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            if (target != null) {
               this.addMasteryPoint(instance, entity);
               if (target.m_21023_((MobEffect)TensuraMobEffects.INFECTION.get())) {
                  target.m_21195_((MobEffect)TensuraMobEffects.INFECTION.get());
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123749_, 1.0D);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
               } else {
                  if (target instanceof Player) {
                     Player player = (Player)target;
                     if (player.m_150110_().f_35934_) {
                        return;
                     }
                  }

                  instance.setCoolDown(instance.isMastered(entity) ? 3 : 1);
                  SkillHelper.checkThenAddEffectSource(target, entity, new MobEffectInstance((MobEffect)TensuraMobEffects.INFECTION.get(), 900, 0));
                  DamageSourceHelper.markHurt(target, entity);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123784_, 1.0D);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123784_, 2.0D);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215734_, SoundSource.PLAYERS, 10.0F, 1.0F);
               }
            }
         }

      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity living, int heldTicks) {
      if (instance.getMode() != 3) {
         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(living, instance)) {
         return false;
      } else {
         if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, living);
         }

         Level level = living.m_9236_();
         level.m_6263_((Player)null, living.m_20185_(), living.m_20186_(), living.m_20189_(), SoundEvents.f_215734_, SoundSource.PLAYERS, 10.0F, 1.0F);
         if (heldTicks % 10 == 0) {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return living;
            }), new RequestFxSpawningPacket(new ResourceLocation("tensura:healer_plague"), living.m_19879_(), 0.0D, 1.0D, 0.0D, true));
         }

         List<LivingEntity> list = level.m_6443_(LivingEntity.class, living.m_20191_().m_82400_(7.0D), (entity) -> {
            return !entity.m_7306_(living) && entity.m_6084_() && !entity.m_7307_(living);
         });
         if (!list.isEmpty()) {
            Iterator var6 = list.iterator();

            while(true) {
               LivingEntity target;
               Player player;
               do {
                  if (!var6.hasNext()) {
                     return true;
                  }

                  target = (LivingEntity)var6.next();
                  if (!(target instanceof Player)) {
                     break;
                  }

                  player = (Player)target;
               } while(player.m_150110_().f_35934_);

               SkillHelper.checkThenAddEffectSource(target, living, new MobEffectInstance((MobEffect)TensuraMobEffects.INFECTION.get(), 900, 0));
            }
         } else {
            return true;
         }
      }
   }
}
