package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class MercilessSkill extends Skill {
   public MercilessSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 50000.0D;
   }

   public double learningCost() {
      return 1000.0D;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.merciless.steal");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.merciless.consume");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 100.0D;
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity attacker, LivingHurtEvent e) {
      if (instance.getMode() == 2) {
         if (this.isInSlot(attacker)) {
            LivingEntity target = e.getEntity();
            if (attacker instanceof Player) {
               Player player = (Player)attacker;
               if (!SkillHelper.outOfMagicule(player, instance)) {
                  SkillHelper.addEffectWithSource(target, player, (MobEffect)TensuraMobEffects.SOUL_DRAIN.get(), 100, 0);
               }
            } else {
               target.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.SOUL_DRAIN.get(), 100, 0, false, false), attacker);
            }

         }
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity living, int heldTicks) {
      if (instance.getMode() != 1) {
         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(living, instance)) {
         return false;
      } else {
         if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, living);
         }

         living.m_9236_().m_6263_((Player)null, living.m_20185_(), living.m_20186_(), living.m_20189_(), SoundEvents.f_12404_, SoundSource.PLAYERS, 1.0F, 1.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(living, (ParticleOptions)TensuraParticles.SOUL.get(), 1.0D);
         List<LivingEntity> list = living.m_9236_().m_6443_(LivingEntity.class, living.m_20191_().m_82400_(15.0D), (entity) -> {
            return !entity.m_7306_(living) && entity.m_6084_() && !entity.m_7307_(living);
         });
         if (!list.isEmpty()) {
            double ownerEP = TensuraEPCapability.getEP(living);
            Iterator var7 = list.iterator();

            while(true) {
               LivingEntity target;
               Player player;
               do {
                  if (!var7.hasNext()) {
                     return true;
                  }

                  target = (LivingEntity)var7.next();
                  if (!(target instanceof Player)) {
                     break;
                  }

                  player = (Player)target;
               } while(player.m_150110_().f_35934_);

               MobEffectInstance fear = target.m_21124_((MobEffect)TensuraMobEffects.FEAR.get());
               boolean shouldConsume = (double)target.m_21223_() < (double)target.m_21233_() * 0.1D || TensuraEPCapability.getEP(target) < ownerEP * 0.1D || fear != null && fear.m_19564_() >= 4;
               if (shouldConsume) {
                  target.m_6469_(this.sourceWithMP(TensuraDamageSources.soulConsume(living), living, instance), target.m_21233_() * 10.0F);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.SOUL.get(), 1.0D);
               }
            }
         } else {
            return true;
         }
      }
   }
}
