package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.entity.ProjectileImpactEvent;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.network.PacketDistributor;
import org.jetbrains.annotations.NotNull;

public class EnvySkill extends Skill {
   public EnvySkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 100000.0D;
   }

   public int getMaxMastery() {
      return 1500;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.envy.absorb");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.envy.strength_sap");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return instance.getMode() == 2 ? 1000.0D : 0.0D;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return this.isInSlot(entity);
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (this.isInSlot(entity)) {
         entity.m_7292_(new MobEffectInstance(MobEffects.f_19621_, 240, 2, false, false, false));
         entity.m_21195_(MobEffects.f_19613_);
         entity.m_21195_(MobEffects.f_19597_);
      }
   }

   @NotNull
   public List<MobEffect> getImmuneEffects(ManasSkillInstance instance, LivingEntity entity) {
      return (List)(!this.isInSlot(entity) ? new ArrayList() : List.of(MobEffects.f_19613_, MobEffects.f_19597_));
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         LivingEntity entity = event.getEntity();
         if (this.isInSlot(entity)) {
            DamageSource damageSource = event.getSource();
            if (!damageSource.m_19378_() && !damageSource.m_19387_()) {
               if (damageSource.m_7640_() != null && damageSource.m_7640_() == damageSource.m_7639_()) {
                  if (!(entity.m_217043_().m_188501_() > 0.2F)) {
                     entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
                     event.setCanceled(true);
                     if (SkillUtils.canNegateDodge(entity, damageSource)) {
                        event.setCanceled(false);
                     }

                  }
               }
            }
         }
      }
   }

   public void onProjectileHit(ManasSkillInstance instance, LivingEntity entity, ProjectileImpactEvent event) {
      if (this.isInSlot(entity)) {
         if (!SkillUtils.isProjectileAlwaysHit(event.getProjectile())) {
            if (!(entity.m_217043_().m_188501_() > 0.2F)) {
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
               event.setCanceled(true);
            }
         }
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() != 2) {
         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 200 == 0) {
            if (heldTicks > 0) {
               this.addMasteryPoint(instance, entity);
            }

            if ((double)entity.m_217043_().m_188501_() <= 0.2D) {
               int level = 0;
               MobEffectInstance insanity = entity.m_21124_((MobEffect)TensuraMobEffects.INSANITY.get());
               if (insanity != null) {
                  level = insanity.m_19564_() + 1;
               }

               entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.INSANITY.get(), 240, level, false, false, false));
            }
         }

         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12362_, SoundSource.PLAYERS, 0.5F, 0.5F);
         if (heldTicks % 10 == 0) {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return entity;
            }), new RequestFxSpawningPacket(new ResourceLocation("tensura:strength_sap"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
         }

         List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(15.0D), (living) -> {
            return !living.m_7306_(entity) && living.m_6084_() && !living.m_7307_(entity);
         });
         if (!list.isEmpty()) {
            double ownerEP = TensuraEPCapability.getEP(entity);
            Iterator var7 = list.iterator();

            while(true) {
               LivingEntity target;
               Player player;
               do {
                  if (!var7.hasNext()) {
                     return true;
                  }

                  target = (LivingEntity)var7.next();
                  if (!(target instanceof Player)) {
                     break;
                  }

                  player = (Player)target;
               } while(player.m_150110_().f_35934_);

               double targetEP = TensuraEPCapability.getEP(target);
               double difference = 1.0D - targetEP / ownerEP - 0.4D;
               if (instance.isMastered(entity)) {
                  difference += 0.2D;
               }

               if (!(difference < 0.0D)) {
                  int level = 1 + (int)(difference / 0.1D);
                  target.m_7292_(new MobEffectInstance(MobEffects.f_19597_, 200, level));
                  target.m_7292_(new MobEffectInstance(MobEffects.f_19613_, 200, level));
                  SkillHelper.reduceEnergy(target, entity, 0.001D, true);
               }
            }
         } else {
            return true;
         }
      }
   }

   private boolean failedAbsorb(LivingEntity entity, double EP, double targetEP) {
      if (targetEP > EP * 10.0D) {
         return (double)entity.m_217043_().m_188501_() >= 0.1D;
      } else if (targetEP >= EP * 5.0D) {
         return (double)entity.m_217043_().m_188501_() >= 0.2D;
      } else {
         return (double)entity.m_217043_().m_188501_() >= 0.3D;
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() != 2) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            Level level = entity.m_9236_();
            LivingEntity target = SkillHelper.getTargetingEntity(entity, 5.0D, false);
            if (target != null && target.m_6084_()) {
               if (target instanceof Player) {
                  Player player = (Player)target;
                  if (player.m_150110_().f_35934_) {
                     return;
                  }
               }

               double EP = TensuraEPCapability.getEP(entity);
               double targetEP = SkillUtils.getEPGain(target, entity);
               Player player;
               if (targetEP < EP) {
                  if (entity instanceof Player) {
                     player = (Player)entity;
                     player.m_5661_(Component.m_237115_("tensura.targeting.ep_not_meet").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  }

                  return;
               }

               this.addMasteryPoint(instance, entity);
               if (this.failedAbsorb(entity, EP, targetEP)) {
                  if (entity instanceof Player) {
                     player = (Player)entity;
                     player.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  }

                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  return;
               }

               double difference = Math.min((double)target.m_217043_().m_188501_() >= 0.4D ? (targetEP - EP) / 2.0D : targetEP - EP, (Double)TensuraConfig.INSTANCE.skillsConfig.maximumEPSteal.get());
               if (target instanceof Player) {
                  Player playerTarget = (Player)target;
                  if (TensuraGameRules.canEpSteal(level)) {
                     DamageSourceHelper.markHurt(target, entity);
                     SkillHelper.gainMaxMP(entity, difference / 2.0D);
                     SkillHelper.gainMP(entity, difference / 4.0D, false);
                     SkillHelper.gainAP(entity, difference / 4.0D, false);
                     TensuraPlayerCapability.getFrom(playerTarget).ifPresent((cap) -> {
                        double reducedAura = cap.getBaseAura() - difference / 2.0D;
                        double reducedMana = cap.getBaseMagicule() - difference / 2.0D;
                        if (reducedAura < 0.0D) {
                           reducedMana -= reducedAura * -1.0D;
                           reducedAura = 100.0D;
                        } else if (reducedMana < 0.0D) {
                           reducedAura -= reducedMana * -1.0D;
                           reducedMana = 100.0D;
                        }

                        double minusMP = cap.getBaseMagicule() - reducedMana;
                        cap.setMagicule(cap.getMagicule() - minusMP);
                        double minusAP = cap.getBaseAura() - reducedAura;
                        cap.setAura(cap.getAura() - minusAP);
                        cap.setBaseMagicule(reducedMana, playerTarget);
                        cap.setBaseAura(reducedAura, playerTarget);
                     });
                     TensuraPlayerCapability.sync(playerTarget);
                     instance.setCoolDown(instance.isMastered(entity) ? 10 : 30);
                     entity.m_21011_(InteractionHand.MAIN_HAND, true);
                     level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123792_, 1.0D);
                  } else if (entity instanceof Player) {
                     Player player = (Player)entity;
                     player.m_5661_(Component.m_237115_("tensura.targeting.not_allowed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  }
               } else {
                  if (target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_EP_PLUNDER)) {
                     return;
                  }

                  DamageSourceHelper.markHurt(target, entity);
                  SkillHelper.gainMaxMP(entity, difference / 2.0D);
                  SkillHelper.gainMP(entity, difference / 4.0D, false);
                  SkillHelper.gainAP(entity, difference / 4.0D, false);
                  TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
                     cap.setEP(target, cap.getEP() - difference);
                  });
                  instance.setCoolDown(instance.isMastered(entity) ? 10 : 30);
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123792_, 1.0D);
               }
            } else if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237115_("tensura.targeting.not_targeted").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
            }

         }
      }
   }
}
