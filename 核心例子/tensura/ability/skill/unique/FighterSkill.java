package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class FighterSkill extends Skill {
   public FighterSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity attacker, LivingHurtEvent e) {
      if (this.isInSlot(attacker)) {
         DamageSource source = e.getSource();
         if (source.m_7640_() == attacker) {
            if (DamageSourceHelper.isPhysicalAttack(source)) {
               float damage = instance.isMastered(attacker) ? 150.0F : 75.0F;
               e.setAmount(e.getAmount() + damage);
               this.addMasteryPoint(instance, attacker);
            }
         }
      }
   }
}
