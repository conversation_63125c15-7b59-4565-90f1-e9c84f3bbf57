package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.extra.DemonLordHakiSkill;
import com.github.manasmods.tensura.ability.skill.extra.HakiSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.CharmSkill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.NeutralMob;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;

public class VillainSkill extends Skill {
   private static final String HAKI = "4d8ed402-9190-4ac5-b623-e620ffc838de";

   public VillainSkill() {
      super(Skill.SkillType.UNIQUE);
      this.addHeldAttributeModifier(Attributes.f_22279_, "4d8ed402-9190-4ac5-b623-e620ffc838de", -0.949999988079071D, Operation.MULTIPLY_TOTAL);
   }

   public double getObtainingEpCost() {
      return 60000.0D;
   }

   public double learningCost() {
      return 250.0D;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return this.isInSlot(entity);
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.villain.haki");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.villain.charisma");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 25.0D;
         break;
      case 2:
         var10000 = 200.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(15.0D), (living) -> {
         return living.m_6084_() && (living.m_7307_(entity) || living.m_7306_(entity));
      });
      if (!list.isEmpty()) {
         Iterator var4 = list.iterator();

         while(var4.hasNext()) {
            LivingEntity target = (LivingEntity)var4.next();
            target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.ALLY_BOOST.get(), 240, 0, false, false, false));
         }

      }
   }

   public double getAttributeModifierAmplifier(ManasSkillInstance instance, LivingEntity entity, AttributeModifier modifier) {
      return modifier.m_22218_() * (instance.isMastered(entity) ? 0.9473684210526315D : 1.0D);
   }

   public void addHeldAttributeModifiers(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 1) {
         super.addHeldAttributeModifiers(instance, entity);
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() != 1) {
         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 100 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         DemonLordHakiSkill.activateDemonLordHaki(instance, entity, heldTicks);
         return true;
      }
   }

   public void onScroll(ManasSkillInstance instance, LivingEntity entity, double delta) {
      if (instance.getMode() == 1) {
         HakiSkill.changeEPUsed(instance, entity, delta);
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() == 1) {
         if (this.hasAttributeApplied(entity, Attributes.f_22279_, "4d8ed402-9190-4ac5-b623-e620ffc838de")) {
            instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 2) {
         Level level = entity.m_9236_();
         List<LivingEntity> list = level.m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(10.0D), (targetx) -> {
            return !targetx.m_7306_(entity) && targetx.m_6084_() && !targetx.m_7307_(entity);
         });
         if (!list.isEmpty()) {
            if (!SkillHelper.outOfMagicule(entity, instance)) {
               this.addMasteryPoint(instance, entity);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11868_, SoundSource.PLAYERS, 1.0F, 1.0F);
               Iterator var5 = list.iterator();

               while(true) {
                  LivingEntity target;
                  NeutralMob mob;
                  do {
                     Mob mob;
                     do {
                        do {
                           do {
                              do {
                                 if (!var5.hasNext()) {
                                    return;
                                 }

                                 target = (LivingEntity)var5.next();
                              } while(target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_CHARISMA));
                           } while(!CharmSkill.canMindControl(target, level));
                        } while(target.m_21023_((MobEffect)TensuraMobEffects.RAMPAGE.get()));

                        if (!(target instanceof Mob)) {
                           break;
                        }

                        mob = (Mob)target;
                     } while(mob.m_5912_());

                     if (!(target instanceof NeutralMob)) {
                        break;
                     }

                     mob = (NeutralMob)target;
                  } while(mob.m_21660_());

                  if (SkillHelper.getSubordinateOwner(target) == null && !SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_NULLIFICATION.get())) {
                     int duration = SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_RESISTANCE.get()) ? 1200 : 2400;
                     SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.MIND_CONTROL.get(), duration, 0, false, false, false, true);
                     if (target.m_21023_((MobEffect)TensuraMobEffects.MIND_CONTROL.get())) {
                        TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
                           if (!Objects.equals(cap.getTemporaryOwner(), entity.m_20148_())) {
                              cap.setTemporaryOwner(entity.m_20148_());
                              if (entity instanceof Player) {
                                 Player player = (Player)entity;
                                 if (target instanceof TamableAnimal) {
                                    TamableAnimal animal = (TamableAnimal)target;
                                    animal.m_21828_(player);
                                 } else if (target instanceof TensuraHorseEntity) {
                                    TensuraHorseEntity horse = (TensuraHorseEntity)target;
                                    horse.m_30637_(player);
                                 }
                              }

                              TensuraEPCapability.sync(target);
                              entity.m_21011_(InteractionHand.MAIN_HAND, true);
                              TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123750_);
                           }

                        });
                     }
                  }
               }
            }
         }
      }
   }
}
