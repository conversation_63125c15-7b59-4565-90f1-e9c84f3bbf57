package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.ISpatialStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.menu.container.SpatialStorageContainer;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.living.LivingDamageEvent;
import org.jetbrains.annotations.NotNull;

public class InfinityPrisonSkill extends Skill implements ISpatialStorage {
   public InfinityPrisonSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 90000.0D;
   }

   public double learningCost() {
      return 10000.0D;
   }

   public int modes() {
      return 2;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 10000.0D;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.infinity_prison.imprison");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.infinity_prison.imaginary_space");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMastery() < 0) {
         return false;
      } else {
         return instance.getMode() != 1;
      }
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         LivingEntity entity = event.getEntity();
         if (this.isInSlot(entity)) {
            DamageSource damageSource = event.getSource();
            if (!damageSource.m_19378_()) {
               if (damageSource instanceof TensuraDamageSource) {
                  TensuraDamageSource tensuraSource = (TensuraDamageSource)damageSource;
                  if (tensuraSource.getIgnoreBarrier() >= 2.0F) {
                     return;
                  }
               }

               Entity var6 = damageSource.m_7639_();
               if (var6 instanceof LivingEntity) {
                  LivingEntity source = (LivingEntity)var6;
                  if (TensuraEPCapability.getEP(source) >= TensuraEPCapability.getEP(entity) * 0.75D) {
                     return;
                  }

                  if (entity instanceof Player) {
                     Player player = (Player)entity;
                     double cost = (double)(event.getAmount() * 25.0F);
                     if (TensuraPlayerCapability.getMagicule(player) < cost) {
                        return;
                     }

                     TensuraPlayerCapability.decreaseMagicule(player, cost);
                  }

                  event.setCanceled(true);
               }

            }
         }
      }
   }

   public void onTakenDamage(ManasSkillInstance instance, LivingDamageEvent event) {
      if (!event.isCanceled()) {
         if (this.isInSlot(event.getEntity())) {
            DamageSource damageSource = event.getSource();
            if (!damageSource.m_19378_()) {
               if (damageSource instanceof TensuraDamageSource) {
                  TensuraDamageSource tensuraSource = (TensuraDamageSource)damageSource;
                  if (tensuraSource.getIgnoreBarrier() >= 2.0F) {
                     return;
                  }
               }

               Entity var5 = damageSource.m_7639_();
               if (var5 instanceof LivingEntity) {
                  LivingEntity source = (LivingEntity)var5;
                  if (TensuraEPCapability.getEP(source) >= TensuraEPCapability.getEP(event.getEntity()) * 0.75D) {
                     return;
                  }

                  float amount = event.getAmount();
                  double lackedMana = SkillHelper.outOfMagiculeStillConsume(event.getEntity(), (double)((int)(amount * 25.0F)));
                  if (lackedMana > 0.0D) {
                     amount = (float)((double)amount - lackedMana / 25.0D);
                  }

                  if (damageSource instanceof TensuraDamageSource) {
                     TensuraDamageSource tensuraSource = (TensuraDamageSource)damageSource;
                     if (tensuraSource.getMpCost() > 0.0D) {
                        CompoundTag tag = instance.getOrCreateTag();
                        double mp = tag.m_128459_("mpStorage") + tensuraSource.getMpCost() * (double)amount / (double)event.getAmount();
                        tag.m_128347_("mpStorage", mp);
                        instance.markDirty();
                     }
                  }

                  if (amount < event.getAmount()) {
                     event.setAmount(event.getAmount() - amount);
                  } else {
                     event.setCanceled(true);
                  }
               }

            }
         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 1) {
         LivingEntity target = (LivingEntity)SkillHelper.getTargetingEntity(LivingEntity.class, entity, 30.0D, 0.2D, false, true);
         Player player;
         if (target == null) {
            if (entity instanceof Player) {
               player = (Player)entity;
               player.m_5661_(Component.m_237115_("tensura.targeting.not_targeted").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
            }

            instance.setCoolDown(instance.isMastered(entity) ? 10 : 20);
            return;
         }

         if (target.m_21023_((MobEffect)TensuraMobEffects.INFINITE_IMPRISONMENT.get())) {
            target.m_21195_((MobEffect)TensuraMobEffects.INFINITE_IMPRISONMENT.get());
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123747_, 1.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123749_, 1.0D);
         } else {
            if (target instanceof Player) {
               player = (Player)target;
               if (player.m_150110_().f_35934_) {
                  return;
               }
            }

            if (SkillHelper.outOfMagicule(entity, instance)) {
               return;
            }

            instance.addMasteryPoint(entity);
            instance.setCoolDown(instance.isMastered(entity) ? 10 : 20);
            int duration = this.isMastered(instance, entity) ? 12000 : 6000;
            SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.INFINITE_IMPRISONMENT.get(), duration, 0, false, false, false, true);
            DamageSourceHelper.markHurt(target, entity);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123747_, 1.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123813_, 1.0D);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }
      } else {
         this.openSpatialStorage(entity, instance);
      }

   }

   @NotNull
   public SpatialStorageContainer getSpatialStorage(ManasSkillInstance instance) {
      SpatialStorageContainer container = new SpatialStorageContainer(90, 999);
      container.m_7797_(instance.getOrCreateTag().m_128437_("SpatialStorage", 10));
      return container;
   }
}
