package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.api.entity.subclass.ISummonable;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.event.SkillPlunderEvent;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class UsurperSkill extends Skill {
   public UsurperSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 50000.0D;
   }

   public double learningCost() {
      return 5000.0D;
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      int var10000;
      if (reverse) {
         switch(instance.getMode()) {
         case 1:
            var10000 = this.isMastered(instance, entity) ? 3 : 2;
            break;
         case 2:
            var10000 = 1;
            break;
         case 3:
            var10000 = 2;
            break;
         default:
            var10000 = 0;
         }

         return var10000;
      } else {
         switch(instance.getMode()) {
         case 1:
            var10000 = 2;
            break;
         case 2:
            var10000 = this.isMastered(instance, entity) ? 3 : 1;
            break;
         default:
            var10000 = 1;
         }

         return var10000;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.usurper.rob");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.usurper.copy");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.usurper.force_takeover");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
      case 2:
         var10000 = 1000.0D;
         break;
      case 3:
         var10000 = 5000.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public void onTouchEntity(ManasSkillInstance instance, LivingEntity attacker, LivingHurtEvent e) {
      if (this.isInSlot(e.getEntity())) {
         if (attacker.m_217043_().m_188499_()) {
            if (SkillHelper.drainMP(e.getEntity(), attacker, 0.01D, true) && attacker instanceof Player) {
               Player player = (Player)attacker;
               player.m_6330_(SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!SkillHelper.outOfMagicule(entity, instance)) {
         LivingEntity target = SkillHelper.getTargetingEntity(entity, 10.0D, false);
         if (target != null && target.m_6084_()) {
            if (target instanceof Player) {
               Player player = (Player)target;
               if (player.m_150110_().f_35934_) {
                  return;
               }
            }

            double EP = TensuraEPCapability.getEP(entity);
            double targetEP = TensuraEPCapability.getEP(target);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            ServerLevel level = (ServerLevel)entity.m_9236_();
            Player player;
            switch(instance.getMode()) {
            case 1:
               if (targetEP > EP) {
                  int chance = instance.isMastered(entity) ? 50 : 25;
                  if (entity.m_217043_().m_188503_(100) <= chance) {
                     this.robRandomSkill(instance, entity, target);
                     this.addMasteryPoint(instance, entity);
                     instance.setCoolDown(10);
                     DamageSourceHelper.markHurt(target, entity);
                  } else if (entity instanceof Player) {
                     Player player = (Player)entity;
                     player.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                     level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  }
               } else if (entity instanceof Player) {
                  player = (Player)entity;
                  player.m_5661_(Component.m_237115_("tensura.targeting.ep_not_meet").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
               break;
            case 2:
               int chance = instance.isMastered(entity) ? 50 : 25;
               if (entity.m_217043_().m_188503_(100) <= chance) {
                  this.copyRandomSkill(instance, entity, target);
                  this.addMasteryPoint(instance, entity);
                  instance.setCoolDown(10);
                  DamageSourceHelper.markHurt(target, entity);
               } else if (entity instanceof Player) {
                  Player player = (Player)entity;
                  player.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
               break;
            case 3:
               if (target instanceof ISummonable) {
                  ISummonable summonable = (ISummonable)target;
                  if (summonable.getSummoningTick() > 0) {
                     if (entity.m_6144_()) {
                        TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
                           if (Objects.equals(cap.getTemporaryOwner(), entity.m_20148_())) {
                              cap.setTemporaryOwner((UUID)null);
                              UUID owner = cap.getPermanentOwner();
                              if (target instanceof TensuraTamableEntity) {
                                 TensuraTamableEntity tamable = (TensuraTamableEntity)target;
                                 tamable.resetOwner(owner);
                              } else if (target instanceof TensuraHorseEntity) {
                                 TensuraHorseEntity horse = (TensuraHorseEntity)target;
                                 horse.resetOwner(owner);
                              } else if (target instanceof TamableAnimal) {
                                 TamableAnimal animal = (TamableAnimal)target;
                                 animal.m_21816_(owner);
                                 if (owner == null) {
                                    animal.m_7105_(false);
                                 }
                              }

                              entity.m_21011_(InteractionHand.MAIN_HAND, true);
                              TensuraEPCapability.sync(target);
                              TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123792_);
                              level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12052_, SoundSource.PLAYERS, 1.0F, 1.0F);
                           }

                        });
                        return;
                     }

                     LivingEntity owner;
                     label96: {
                        if (summonable instanceof TamableAnimal) {
                           TamableAnimal tamable = (TamableAnimal)summonable;
                           if (tamable.m_21826_() != null) {
                              owner = tamable.m_21826_();
                              break label96;
                           }
                        }

                        if (summonable.getSummonerUUID() != null) {
                           Entity var13 = SkillHelper.getEntityFromUUID(level, summonable.getSummonerUUID());
                           if (var13 instanceof LivingEntity) {
                              LivingEntity summoner = (LivingEntity)var13;
                              owner = summoner;
                              break label96;
                           }
                        }

                        owner = target;
                     }

                     double ownerEP = TensuraEPCapability.getEP(owner);
                     if (ownerEP <= EP * 0.75D) {
                        TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
                           cap.setTemporaryOwner(entity.m_20148_());
                           if (summonable instanceof TamableAnimal) {
                              TamableAnimal tamable = (TamableAnimal)summonable;
                              if (entity instanceof Player) {
                                 Player player = (Player)entity;
                                 tamable.m_21828_(player);
                              }
                           }

                           if (target instanceof Mob) {
                              Mob mob = (Mob)target;
                              SkillHelper.removeTarget(mob);
                           }

                           TensuraEPCapability.sync(target);
                        });
                        instance.setCoolDown(10);
                        entity.m_21011_(InteractionHand.MAIN_HAND, true);
                        DamageSourceHelper.markHurt(target, entity);
                        level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
                        return;
                     }
                  }
               }

               if (targetEP <= EP) {
                  this.robRandomSkill(instance, entity, target);
                  instance.setCoolDown(10);
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  DamageSourceHelper.markHurt(target, entity);
               } else if (entity instanceof Player) {
                  player = (Player)entity;
                  player.m_5661_(Component.m_237115_("tensura.targeting.ep_not_meet").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }

         }
      }
   }

   public void robRandomSkill(ManasSkillInstance instance, LivingEntity entity, LivingEntity living) {
      Level level = entity.m_9236_();
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      List<ManasSkillInstance> collection = SkillAPI.getSkillsFrom(living).getLearnedSkills().stream().filter(this::canRob).toList();
      if (!collection.isEmpty()) {
         ManasSkill skill = ((ManasSkillInstance)collection.get(living.m_217043_().m_188503_(collection.size()))).getSkill();
         boolean skillSteal = TensuraGameRules.canStealSkill(level);
         SkillPlunderEvent event;
         if (storage.getSkill(skill).isEmpty()) {
            event = new SkillPlunderEvent(entity, living, skillSteal, skill);
            if (!MinecraftForge.EVENT_BUS.post(event)) {
               if (learnSkillHalfMastery(instance, entity, new TensuraSkillInstance(event.getSkill())) && entity instanceof Player) {
                  Player player = (Player)entity;
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{event.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               }

               if (skillSteal) {
                  SkillAPI.getSkillsFrom(living).forgetSkill(event.getSkill());
                  SkillAPI.getSkillsFrom(living).syncChanges();
               }

               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         } else {
            Object var12 = storage.getSkill(skill).get();
            if (var12 instanceof TensuraSkillInstance) {
               TensuraSkillInstance skillInstance = (TensuraSkillInstance)var12;
               event = new SkillPlunderEvent(entity, living, skillSteal, skill);
               if (!MinecraftForge.EVENT_BUS.post(event)) {
                  skillInstance.setMastery(Math.max(50, skillInstance.getMastery()));
                  if (skillSteal) {
                     SkillAPI.getSkillsFrom(living).forgetSkill(event.getSkill());
                     SkillAPI.getSkillsFrom(living).syncChanges();
                  }

                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }
         }

         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }
   }

   public void copyRandomSkill(ManasSkillInstance instance, LivingEntity entity, LivingEntity living) {
      Level level = entity.m_9236_();
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      List<ManasSkillInstance> collection = SkillAPI.getSkillsFrom(living).getLearnedSkills().stream().filter(this::canCopy).toList();
      if (!collection.isEmpty()) {
         ManasSkill skill = ((ManasSkillInstance)collection.get(living.m_217043_().m_188503_(collection.size()))).getSkill();
         if (storage.getSkill(skill).isEmpty()) {
            if (learnSkillHalfMastery(instance, entity, new TensuraSkillInstance(skill)) && entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
            }
         } else {
            Object var10 = storage.getSkill(skill).get();
            if (var10 instanceof TensuraSkillInstance) {
               TensuraSkillInstance skillInstance = (TensuraSkillInstance)var10;
               skillInstance.setMastery(Math.max(50, skillInstance.getMastery()));
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         }

         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }
   }

   public static boolean learnSkillHalfMastery(ManasSkillInstance usurper, LivingEntity entity, ManasSkillInstance skill) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      Optional<ManasSkillInstance> optional = storage.getSkill(skill.getSkill());
      if (optional.isEmpty()) {
         skill.setMastery(50);
         skill.setRemoveTime(usurper.getRemoveTime());
         return storage.learnSkill(skill);
      } else {
         ManasSkillInstance instance = (ManasSkillInstance)optional.get();
         if (instance.getMastery() >= 0 && !instance.isTemporarySkill()) {
            return false;
         } else {
            ManasSkillInstance clone = instance.clone();
            clone.setRemoveTime(usurper.getRemoveTime());
            clone.setMastery(50);
            UnlockSkillEvent event = new UnlockSkillEvent(clone, entity);
            if (MinecraftForge.EVENT_BUS.post(event)) {
               return false;
            } else {
               instance.deserialize(event.getSkillInstance().toNBT());
               instance.markDirty();
               storage.syncChanges();
               return true;
            }
         }
      }
   }

   public boolean canRob(ManasSkillInstance instance) {
      if (!instance.isTemporarySkill() && instance.getMastery() >= 0) {
         ManasSkill var3 = instance.getSkill();
         if (!(var3 instanceof Skill)) {
            return !(instance.getSkill() instanceof SpiritualMagic);
         } else {
            Skill skill = (Skill)var3;
            return skill.getType().equals(Skill.SkillType.COMMON) || skill.getType().equals(Skill.SkillType.EXTRA);
         }
      } else {
         return false;
      }
   }

   public boolean canCopy(ManasSkillInstance instance) {
      if (!instance.isTemporarySkill() && instance.getMastery() >= 0) {
         ManasSkill var3 = instance.getSkill();
         if (!(var3 instanceof Skill)) {
            return !(instance.getSkill() instanceof SpiritualMagic);
         } else {
            Skill skill = (Skill)var3;
            return skill.getType().equals(Skill.SkillType.COMMON) || skill.getType().equals(Skill.SkillType.EXTRA) || skill.getType().equals(Skill.SkillType.INTRINSIC);
         }
      } else {
         return false;
      }
   }
}
