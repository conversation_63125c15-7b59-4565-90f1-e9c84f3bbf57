package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.UUID;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.common.ToolActions;
import net.minecraftforge.event.entity.living.LivingDamageEvent;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class BerserkerSkill extends Skill {
   public static final UUID BERSERKER = UUID.fromString("8465ec3c-4e31-11ee-be56-0242ac120002");

   public BerserkerSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 20000.0D;
   }

   public void onTouchEntity(ManasSkillInstance instance, LivingEntity entity, LivingHurtEvent e) {
      if (this.isInSlot(entity)) {
         if (e.getSource().m_7640_() == entity && DamageSourceHelper.isPhysicalAttack(e.getSource())) {
            LivingEntity target = e.getEntity();
            int durabilityBreak = (int)Math.max(1.0F, e.getAmount() / 4.0F);
            EquipmentSlot[] var6 = EquipmentSlot.values();
            int var7 = var6.length;

            for(int var8 = 0; var8 < var7; ++var8) {
               EquipmentSlot slot = var6[var8];
               if (!slot.m_20743_().equals(Type.HAND) || target.m_6844_(slot).canPerformAction(ToolActions.SHIELD_BLOCK)) {
                  ItemStack slotStack = target.m_6844_(slot);
                  slotStack.m_41622_(durabilityBreak, target, (living) -> {
                     living.m_21166_(slot);
                  });
               }
            }
         }

      }
   }

   public void onTakenDamage(ManasSkillInstance instance, LivingDamageEvent e) {
      if (this.isInSlot(e.getEntity())) {
         Entity var4 = e.getSource().m_7639_();
         if (var4 instanceof LivingEntity) {
            LivingEntity source = (LivingEntity)var4;
            source.m_21120_(InteractionHand.MAIN_HAND).m_41622_(5, source, (entity) -> {
               entity.m_21190_(InteractionHand.MAIN_HAND);
            });
         }

      }
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance damage = entity.m_21051_(Attributes.f_22281_);
      if (damage != null && damage.m_22111_(BERSERKER) != null) {
         damage.m_22127_(BERSERKER);
      }

      AttributeInstance speed = entity.m_21051_(Attributes.f_22279_);
      if (speed != null && speed.m_22111_(BERSERKER) != null) {
         speed.m_22127_(BERSERKER);
      }

      AttributeInstance armor = entity.m_21051_(Attributes.f_22284_);
      if (armor != null && armor.m_22111_(BERSERKER) != null) {
         armor.m_22127_(BERSERKER);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11738_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }

   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      double EP = TensuraEPCapability.getEP(entity);
      AttributeInstance armor = entity.m_21051_(Attributes.f_22284_);
      if (armor != null) {
         if (armor.m_22111_(BERSERKER) != null) {
            armor.m_22127_(BERSERKER);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11738_, SoundSource.PLAYERS, 1.0F, 1.0F);
         } else {
            AttributeModifier armorModifier = new AttributeModifier(BERSERKER, "BerserkerArmor", getArmor(EP), Operation.ADDITION);
            armor.m_22125_(armorModifier);
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(10);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11736_, SoundSource.PLAYERS, 1.0F, 1.0F);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11913_, SoundSource.PLAYERS, 1.0F, 1.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get(), 2.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get(), 3.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get(), 2.0D);
            TensuraParticleHelper.spawnServerParticles(entity.f_19853_, (ParticleOptions)TensuraParticles.PURPLE_LIGHTNING_SPARK.get(), entity.m_20185_(), entity.m_20186_() + (double)(entity.m_20206_() / 2.0F), entity.m_20189_(), 100, 0.08D, 0.08D, 0.08D, 0.2D, true);
         }
      }

      AttributeInstance damage = entity.m_21051_(Attributes.f_22281_);
      if (damage != null) {
         if (damage.m_22111_(BERSERKER) != null) {
            damage.m_22127_(BERSERKER);
         } else {
            damage.m_22125_(new AttributeModifier(BERSERKER, "BerserkerAttack", instance.isMastered(entity) ? getAttack(EP) * 2.0D : getAttack(EP), Operation.ADDITION));
         }
      }

      AttributeInstance speed = entity.m_21051_(Attributes.f_22279_);
      if (speed != null) {
         if (speed.m_22111_(BERSERKER) != null) {
            speed.m_22127_(BERSERKER);
         } else {
            speed.m_22125_(new AttributeModifier(BERSERKER, "BerserkerSpeed", getSpeed(EP) / 100.0D, Operation.ADDITION));
         }
      }

   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance attack = entity.m_21051_(Attributes.f_22281_);
      return attack != null && attack.m_22111_(BERSERKER) != null;
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get());
   }

   public static double getArmor(double EP) {
      return EP >= 2000000.0D ? 100.0D : EP / 20000.0D;
   }

   public static double getAttack(double EP) {
      if (EP <= 40000.0D) {
         return 5.0D;
      } else if (EP >= 2000000.0D) {
         return 55.0D;
      } else {
         double attackIncrease = EP / 40000.0D;
         return 5.0D + attackIncrease;
      }
   }

   public static double getSpeed(double EP) {
      return EP >= 1000000.0D ? 40.0D : EP / 25000.0D;
   }
}
