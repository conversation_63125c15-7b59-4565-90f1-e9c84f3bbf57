package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.entity.projectile.SevererBladeProjectile;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

public class SevererSkill extends Skill {
   public SevererSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double learningCost() {
      return 200.0D;
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 3 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 3 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.severer.sword");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.severer.blade_storm");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.severer.severance");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 100.0D;
         break;
      case 2:
         var10000 = 2000.0D;
         break;
      case 3:
         var10000 = 200.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      switch(instance.getMode()) {
      case 1:
         if (entity.m_21205_().m_41619_()) {
            this.spawnDummySword(instance, entity, InteractionHand.MAIN_HAND);
         } else if (entity.m_21206_().m_41619_()) {
            this.spawnDummySword(instance, entity, InteractionHand.OFF_HAND);
         }
         break;
      case 2:
         ItemStack spatialBlade = null;
         if (entity.m_21120_(InteractionHand.MAIN_HAND).m_150930_((Item)TensuraToolItems.SPATIAL_BLADE.get())) {
            spatialBlade = entity.m_21120_(InteractionHand.MAIN_HAND);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
         } else if (entity.m_21120_(InteractionHand.OFF_HAND).m_150930_((Item)TensuraToolItems.SPATIAL_BLADE.get())) {
            spatialBlade = entity.m_21120_(InteractionHand.OFF_HAND);
            entity.m_21011_(InteractionHand.OFF_HAND, true);
         }

         if (spatialBlade == null) {
            if (entity.m_21205_().m_41619_()) {
               this.spawnDummySword(instance, entity, InteractionHand.MAIN_HAND);
            }

            return;
         }

         if (SkillHelper.outOfMagicule(entity, instance)) {
            return;
         }

         this.addMasteryPoint(instance, entity);
         instance.setCoolDown(instance.isMastered(entity) ? 1 : 3);
         int arrowAmount = instance.isMastered(entity) ? 10 : 5;
         this.spawnSeveranceBlade(entity, spatialBlade, instance, arrowAmount);
         break;
      case 3:
         if (entity.m_21023_((MobEffect)TensuraMobEffects.SEVERANCE_BLADE.get())) {
            return;
         }

         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
         int severance = instance.isMastered(entity) ? 2 : 0;
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.SEVERANCE_BLADE.get(), 2400, severance, false, false, false));
      }

   }

   private void spawnDummySword(ManasSkillInstance instance, LivingEntity entity, InteractionHand hand) {
      if (!SkillHelper.outOfMagicule(entity, instance)) {
         this.addMasteryPoint(instance, entity);
         ItemStack blade = new ItemStack((ItemLike)TensuraToolItems.SPATIAL_BLADE.get());
         blade.m_41784_().m_128379_("dummy", true);
         blade.m_41663_((Enchantment)TensuraEnchantments.SEVERANCE.get(), instance.isMastered(entity) ? 10 : 5);
         entity.m_21008_(hand, blade);
         entity.m_21011_(hand, true);
         entity.f_19853_.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }
   }

   private void spawnSeveranceBlade(LivingEntity entity, ItemStack stack, ManasSkillInstance instance, int arrowAmount) {
      int arrowRot = 360 / arrowAmount;

      for(int i = 0; i < arrowAmount; ++i) {
         Vec3 arrowOffset = (new Vec3(0.0D, 1.0D, 0.0D)).m_82535_(((float)(arrowRot * i) - (float)arrowRot / 2.0F) * 0.017453292F);
         Vec3 arrowPos = entity.m_146892_().m_82549_(entity.m_20154_().m_82541_().m_82490_(1.0D)).m_82549_(arrowOffset.m_82496_(-entity.m_146909_() * 0.017453292F).m_82524_(-entity.m_146908_() * 0.017453292F));
         SevererBladeProjectile blade = new SevererBladeProjectile(entity.m_9236_(), entity, stack);
         blade.m_146884_(arrowPos);
         blade.setOwnerOffset(arrowOffset);
         blade.setLookDistance(instance.isMastered(entity) ? 30.0F : 20.0F);
         blade.setDelayTick(20);
         entity.m_9236_().m_7967_(blade);
         entity.f_19853_.m_6263_((Player)null, arrowPos.m_7096_(), arrowPos.m_7098_(), arrowPos.m_7094_(), SoundEvents.f_11687_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }

   }
}
