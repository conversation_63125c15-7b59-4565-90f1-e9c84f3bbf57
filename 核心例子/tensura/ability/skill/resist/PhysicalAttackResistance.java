package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import javax.annotation.Nullable;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;

public class PhysicalAttackResistance extends ResistSkill {
   public boolean isDamageResisted(DamageSource damageSource, ManasSkillInstance instance) {
      return DamageSourceHelper.isPhysicalAttack(damageSource);
   }

   public double learningCost() {
      return 20.0D;
   }

   public int pointRequirement() {
      return 700;
   }

   public boolean isResistanceBypass(DamageSource damageSource) {
      return canBypassPhysical(damageSource);
   }

   @Nullable
   protected ManasSkill getNullificationForm() {
      return (ManasSkill)ResistanceSkills.PHYSICAL_ATTACK_NULLIFICATION.get();
   }

   public static boolean canBypassPhysical(DamageSource damageSource) {
      Entity var2 = damageSource.m_7639_();
      if (var2 instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)var2;
         if (living.m_21023_((MobEffect)TensuraMobEffects.HAKI_COAT.get())) {
            return true;
         } else if (living.m_21023_((MobEffect)TensuraMobEffects.MAGIC_AURA.get())) {
            return true;
         } else {
            MobEffectInstance instance = living.m_21124_((MobEffect)TensuraMobEffects.SEVERANCE_BLADE.get());
            if (instance != null && instance.m_19564_() >= 4) {
               return true;
            } else {
               if (damageSource instanceof TensuraDamageSource) {
                  TensuraDamageSource source = (TensuraDamageSource)damageSource;
                  if (source.getIgnoreResistance() == 1.0F) {
                     return true;
                  }
               }

               return SkillUtils.reducingResistances(living);
            }
         }
      } else {
         return false;
      }
   }
}
