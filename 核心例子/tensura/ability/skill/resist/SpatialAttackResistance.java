package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import javax.annotation.Nullable;
import net.minecraft.world.damagesource.DamageSource;

public class SpatialAttackResistance extends ResistSkill {
   public boolean isDamageResisted(DamageSource damageSource, ManasSkillInstance instance) {
      return DamageSourceHelper.isSpatialDamage(damageSource);
   }

   public double learningCost() {
      return 15.0D;
   }

   public int pointRequirement() {
      return 500;
   }

   @Nullable
   protected ManasSkill getNullificationForm() {
      return (ManasSkill)ResistanceSkills.SPATIAL_ATTACK_NULLIFICATION.get();
   }
}
