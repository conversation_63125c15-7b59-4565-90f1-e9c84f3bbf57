package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.NotNull;

public class ColdNullification extends ResistSkill {
   public ColdNullification() {
      super(ResistSkill.ResistType.NULLIFICATION);
   }

   public void onLearnSkill(ManasSkillInstance instance, LivingEntity entity, UnlockSkillEvent event) {
      if (instance.getMastery() >= 0) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         if (SkillUtils.hasSkill(entity, (ManasSkill)ResistanceSkills.HEAT_RESISTANCE.get()) || SkillUtils.hasSkill(entity, (ManasSkill)ResistanceSkills.HEAT_NULLIFICATION.get())) {
            ManasSkill skill = (ManasSkill)ResistanceSkills.THERMAL_FLUCTUATION_RESISTANCE.get();
            if (storage.learnSkill(skill) && entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
            }
         }

      }
   }

   public boolean isDamageResisted(DamageSource damageSource, ManasSkillInstance instance) {
      return DamageSourceHelper.isCold(damageSource);
   }

   @NotNull
   public List<MobEffect> getImmuneEffects(ManasSkillInstance instance, LivingEntity entity) {
      return (List)(!instance.isToggled() ? new ArrayList() : List.of((MobEffect)TensuraMobEffects.CHILL.get(), (MobEffect)TensuraMobEffects.FROST.get()));
   }
}
