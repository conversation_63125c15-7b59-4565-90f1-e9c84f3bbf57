package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.google.common.collect.ImmutableList;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import org.jetbrains.annotations.NotNull;

public class AbnormalConditionResistance extends ResistSkill {
   public static final ImmutableList<MobEffect> ABNORMAL_RESIST;

   public boolean isDamageResisted(DamageSource damageSource, ManasSkillInstance instance) {
      return DamageSourceHelper.isAbnormal(damageSource);
   }

   public double learningCost() {
      return 15.0D;
   }

   public int pointRequirement() {
      return 700;
   }

   @NotNull
   public List<MobEffect> getImmuneEffects(ManasSkillInstance instance, LivingEntity entity) {
      return (List)(!instance.isToggled() ? new ArrayList() : ABNORMAL_RESIST);
   }

   @Nullable
   protected ManasSkill getNullificationForm() {
      return (ManasSkill)ResistanceSkills.ABNORMAL_CONDITION_NULLIFICATION.get();
   }

   static {
      ABNORMAL_RESIST = ImmutableList.of(MobEffects.f_19612_, MobEffects.f_19614_, MobEffects.f_19610_, MobEffects.f_19604_, MobEffects.f_216964_, MobEffects.f_19599_, MobEffects.f_19597_, MobEffects.f_19613_, (MobEffect)TensuraMobEffects.BURDEN.get(), (MobEffect)TensuraMobEffects.FRAGILITY.get(), (MobEffect)TensuraMobEffects.CURSE.get());
   }
}
