package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.world.damagesource.DamageSource;

public class MagicResistance extends ResistSkill {
   public boolean isDamageResisted(DamageSource damageSource, ManasSkillInstance instance) {
      return DamageSourceHelper.isTensuraMagic(damageSource);
   }

   public double learningCost() {
      return 20.0D;
   }

   public int pointRequirement() {
      return 700;
   }
}
