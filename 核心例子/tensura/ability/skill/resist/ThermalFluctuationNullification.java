package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import org.jetbrains.annotations.NotNull;

public class ThermalFluctuationNullification extends ResistSkill {
   public ThermalFluctuationNullification() {
      super(ResistSkill.ResistType.NULLIFICATION);
   }

   public boolean isDamageResisted(DamageSource damageSource, ManasSkillInstance instance) {
      return DamageSourceHelper.isTemperature(damageSource);
   }

   @NotNull
   public List<MobEffect> getImmuneEffects(ManasSkillInstance instance, LivingEntity entity) {
      return (List)(!instance.isToggled() ? new ArrayList() : List.of((MobEffect)TensuraMobEffects.CHILL.get(), (MobEffect)TensuraMobEffects.FROST.get()));
   }
}
