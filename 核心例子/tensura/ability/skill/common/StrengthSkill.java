package com.github.manasmods.tensura.ability.skill.common;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class StrengthSkill extends Skill {
   public StrengthSkill() {
      super(Skill.SkillType.COMMON);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return newEP > 3000.0D;
   }

   public double learningCost() {
      return 30.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 30.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (SkillHelper.outOfMagicule(entity, instance)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.lack_magicule.toggled_off", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
         }

         instance.setToggled(false);
      } else {
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.STRENGTHEN.get(), 240, 0, false, false, false));
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!entity.m_21023_((MobEffect)TensuraMobEffects.STRENGTHEN.get()) || instance.isToggled()) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            this.addMasteryPoint(instance, entity);
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.STRENGTHEN.get(), this.isMastered(instance, entity) ? 2400 : 1200, this.isMastered(instance, entity) ? 1 : 0, false, false, true));
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11671_, SoundSource.PLAYERS, 0.5F, 0.5F);
         }
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      this.onTick(instance, entity);
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11671_, SoundSource.PLAYERS, 0.5F, 0.5F);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      MobEffectInstance effect = entity.m_21124_((MobEffect)TensuraMobEffects.STRENGTHEN.get());
      if (effect != null && effect.m_19564_() <= 0) {
         entity.m_21195_((MobEffect)TensuraMobEffects.STRENGTHEN.get());
      }

      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11665_, SoundSource.PLAYERS, 0.5F, 0.5F);
   }
}
