package com.github.manasmods.tensura.ability.skill.common;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.stats.Stats;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class SelfRegenerationSkill extends Skill {
   public SelfRegenerationSkill() {
      super(Skill.SkillType.COMMON);
   }

   public double learningCost() {
      return 100.0D;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         int slime = player.m_8951_().m_13015_(Stats.f_12986_.m_12902_((EntityType)TensuraEntityTypes.SLIME.get()));
         int metal = player.m_8951_().m_13015_(Stats.f_12986_.m_12902_((EntityType)TensuraEntityTypes.METAL_SLIME.get()));
         int supermassive = player.m_8951_().m_13015_(Stats.f_12986_.m_12902_((EntityType)TensuraEntityTypes.SUPERMASSIVE_SLIME.get()));
         return metal + slime + supermassive >= 500;
      } else {
         return false;
      }
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public boolean canBeSlotted(ManasSkillInstance instance) {
      return instance.getMastery() < 0;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 100.0D;
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      double maxHealth = (double)entity.m_21233_() - TensuraEffectsCapability.getSeverance(entity);
      if (entity instanceof Player) {
         if ((double)entity.m_21223_() >= maxHealth) {
            return;
         }

         if (SkillHelper.outOfMagicule(entity, instance)) {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.lack_magicule.toggled_off", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
            }

            instance.setToggled(false);
            instance.onToggleOff(entity);
            instance.markDirty();
            return;
         }
      }

      entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.SELF_REGENERATION.get(), 240, instance.isMastered(entity) ? 1 : 0, false, false, false));
      if ((double)entity.m_21223_() < maxHealth) {
         CompoundTag tag = instance.getOrCreateTag();
         int time = tag.m_128451_("activatedTimes");
         if (time % 10 == 0) {
            this.addMasteryPoint(instance, entity);
         }

         tag.m_128405_("activatedTimes", time + 1);
      }

   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.SELF_REGENERATION.get(), 240, instance.isMastered(entity) ? 1 : 0, false, false, false));
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_21195_((MobEffect)TensuraMobEffects.SELF_REGENERATION.get());
   }
}
