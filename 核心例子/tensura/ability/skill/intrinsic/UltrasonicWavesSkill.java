package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.damagesource.EntityDamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;

public class UltrasonicWavesSkill extends Skill {
   public UltrasonicWavesSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.ultrasonic_waves.sonic_boom");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.ultrasonic_waves.auditory_sense");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 30.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      if (instance.getMode() == 2) {
         if (!entity.m_21023_((MobEffect)TensuraMobEffects.AUDITORY_SENSE.get())) {
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.AUDITORY_SENSE.get(), 200, 0, false, false, false));
         }
      } else if (!SkillHelper.outOfMagicule(entity, instance)) {
         this.addMasteryPoint(instance, entity);
         instance.setCoolDown(instance.isMastered(entity) ? 1 : 3);
         Vec3 target = entity.m_20182_().m_82549_(entity.m_20154_().m_82490_(8.0D));
         Vec3 source = entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_(), 0.0D);
         Vec3 sourceToTarget = target.m_82546_(source);
         Vec3 normalizes = sourceToTarget.m_82541_();
         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215771_, SoundSource.PLAYERS, 5.0F, 1.0F);

         for(int particleIndex = 1; particleIndex < Mth.m_14107_(sourceToTarget.m_82553_()); ++particleIndex) {
            Vec3 particlePos = source.m_82549_(normalizes.m_82490_((double)particleIndex));
            ((ServerLevel)level).m_8767_(ParticleTypes.f_235902_, particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_, 1, 0.0D, 0.0D, 0.0D, 0.0D);
            AABB aabb = (new AABB(new BlockPos(particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_))).m_82400_(2.0D);
            List<LivingEntity> list = level.m_6443_(LivingEntity.class, aabb, (entityData) -> {
               return !entityData.m_7306_(entity) && !entityData.m_7307_(entity);
            });
            if (!list.isEmpty()) {
               Iterator var12 = list.iterator();

               while(var12.hasNext()) {
                  LivingEntity living = (LivingEntity)var12.next();
                  if (!RaceHelper.isSpiritualLifeForm(living)) {
                     DamageSource damagesource = (new EntityDamageSource("sonic_boom", entity)).m_19389_();
                     living.m_6469_(this.sourceWithMP(damagesource, entity, instance), 8.0F);
                  }
               }
            }
         }

      }
   }
}
