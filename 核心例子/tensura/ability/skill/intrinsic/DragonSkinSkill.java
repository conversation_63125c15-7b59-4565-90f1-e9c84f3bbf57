package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.item.TensuraArmourMaterials;
import com.github.manasmods.tensura.util.attribute.TensuraAttributeHelper;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ArmorMaterial;

public class DragonSkinSkill extends Skill {
   protected static final UUID DRAGON_ARMOR = UUID.fromString("728f0316-3fdd-11ee-be56-0242ac120002");

   public DragonSkinSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      return !this.hasArmor(entity);
   }

   private boolean hasArmor(LivingEntity entity) {
      AttributeInstance armor = entity.m_21051_(Attributes.f_22284_);
      if (armor == null) {
         return false;
      } else {
         boolean hasArmor = false;

         for(int i = 0; i <= 3; ++i) {
            if (armor.m_22111_((UUID)TensuraAttributeHelper.ARMOR_MODIFIER_UUID_PER_SLOT.get(i)) != null) {
               hasArmor = true;
               break;
            }
         }

         return hasArmor;
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance armor = entity.m_21051_(Attributes.f_22284_);
      if (armor != null && armor.m_22111_(DRAGON_ARMOR) == null) {
         armor.m_22125_(new AttributeModifier(DRAGON_ARMOR, "Dragon Skin", (double)this.calculateArmor(entity), Operation.ADDITION));
      }

      AttributeInstance toughness = entity.m_21051_(Attributes.f_22285_);
      if (toughness != null && toughness.m_22111_(DRAGON_ARMOR) == null) {
         toughness.m_22125_(new AttributeModifier(DRAGON_ARMOR, "Dragon Skin", (double)this.calculateToughness(entity), Operation.ADDITION));
      }

      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11894_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance armor = entity.m_21051_(Attributes.f_22284_);
      if (armor != null) {
         armor.m_22127_(DRAGON_ARMOR);
      }

      AttributeInstance toughness = entity.m_21051_(Attributes.f_22285_);
      if (toughness != null) {
         toughness.m_22127_(DRAGON_ARMOR);
      }

      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11824_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }

   protected int calculateArmor(LivingEntity entity) {
      int armor = 0;
      EquipmentSlot[] var3 = EquipmentSlot.values();
      int var4 = var3.length;

      for(int var5 = 0; var5 < var4; ++var5) {
         EquipmentSlot slot = var3[var5];
         if (slot.m_20743_().equals(Type.ARMOR)) {
            armor += this.getMaterial(entity).m_7365_(slot);
         }
      }

      return armor;
   }

   protected float calculateToughness(LivingEntity entity) {
      return this.getMaterial(entity).m_6651_() * 4.0F;
   }

   protected ArmorMaterial getMaterial(LivingEntity entity) {
      ArmorMaterial material = TensuraArmourMaterials.PURE_MAGISTEEL;
      double EP = TensuraEPCapability.getEP(entity);
      if (EP >= 800000.0D) {
         material = TensuraArmourMaterials.HIHIIROKANE;
      } else if (EP >= 400000.0D) {
         material = TensuraArmourMaterials.ADAMANTITE;
      }

      return material;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (this.hasArmor(entity)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.lack_requirement.toggled_off", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
         }

         instance.setToggled(false);
         instance.onToggleOff(entity);
         instance.markDirty();
      } else {
         CompoundTag tag = instance.getOrCreateTag();
         int time = tag.m_128451_("activatedTimes");
         if (time % 10 == 0) {
            this.addMasteryPoint(instance, entity);
         }

         tag.m_128405_("activatedTimes", time + 1);
      }
   }
}
