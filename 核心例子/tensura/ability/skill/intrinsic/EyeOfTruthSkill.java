package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.race.ITensuraPlayerCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.handler.CapabilityHandler;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.google.common.collect.ImmutableList;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.NotNull;

public class EyeOfTruthSkill extends Skill {
   public static final ImmutableList<MobEffect> VISION;

   public EyeOfTruthSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      ITensuraPlayerCapability cap = (ITensuraPlayerCapability)CapabilityHandler.getCapability(entity, TensuraPlayerCapability.CAPABILITY);
      if (cap == null) {
         return false;
      } else {
         return cap.isHeroEgg() || cap.isTrueHero();
      }
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         ITensuraPlayerCapability cap = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, TensuraPlayerCapability.CAPABILITY);
         if (cap == null) {
            return false;
         } else {
            return cap.isHeroEgg() || cap.isTrueHero();
         }
      } else {
         return true;
      }
   }

   public boolean canBeSlotted(ManasSkillInstance instance) {
      return instance.getMastery() < 0;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 10 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
      entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get(), 200, 3, false, false, false));
   }

   @NotNull
   public List<MobEffect> getImmuneEffects(ManasSkillInstance instance, LivingEntity entity) {
      return (List)(!instance.isToggled() ? new ArrayList() : VISION);
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      ImmutableList var10001 = VISION;
      Objects.requireNonNull(var10001);
      SkillHelper.removePredicateEffect(entity, var10001::contains);
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get())) {
         int level = ((MobEffectInstance)Objects.requireNonNull(entity.m_21124_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get()))).m_19564_();
         if (level == 3) {
            entity.m_21195_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get());
         }
      }

   }

   static {
      VISION = ImmutableList.of(MobEffects.f_19610_, MobEffects.f_19604_, MobEffects.f_216964_);
   }
}
