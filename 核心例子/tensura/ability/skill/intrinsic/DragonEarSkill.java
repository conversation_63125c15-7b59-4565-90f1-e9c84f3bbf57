package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;

public class DragonEarSkill extends Skill {
   public DragonEarSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 0.0D;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return !instance.isMastered(entity) ? false : instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.AUDITORY_SENSE.get(), 200, 1, false, false, false));
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity living, int heldTicks) {
      if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(living, instance)) {
         return false;
      } else {
         if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, living);
         }

         living.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.AUDITORY_SENSE.get(), 5, 2, false, false, false));
         return true;
      }
   }
}
