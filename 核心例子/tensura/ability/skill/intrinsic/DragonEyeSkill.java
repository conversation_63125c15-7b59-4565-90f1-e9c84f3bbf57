package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class DragonEyeSkill extends Skill {
   public DragonEyeSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   protected boolean canActivateInRaceLimit(ManasSkillInstance instance) {
      return true;
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks % 60 == 0 && heldTicks > 0) {
         this.addMasteryPoint(instance, entity);
      }

      return true;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
            if (player.m_6047_()) {
               int mode = cap.getAnalysisMode();
               switch(mode) {
               case 1:
                  cap.setAnalysisMode(2);
                  player.m_5661_(Component.m_237115_("tensura.skill.analytical.analyzing_mode.block").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
                  break;
               case 2:
                  cap.setAnalysisMode(0);
                  player.m_5661_(Component.m_237115_("tensura.skill.analytical.analyzing_mode.both").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
                  break;
               default:
                  cap.setAnalysisMode(1);
                  player.m_5661_(Component.m_237115_("tensura.skill.analytical.analyzing_mode.entity").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
               }

               player.m_6330_(SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
               TensuraSkillCapability.sync(player);
            } else {
               CompoundTag tag = instance.getOrCreateTag();
               if (!tag.m_128441_("range")) {
                  tag.m_128347_("range", 2.0D);
               }

               instance.markDirty();
               int level = instance.isMastered(entity) ? 2 : 1;
               int distance = Math.min((int)tag.m_128459_("range") * 5, 100);
               if (cap.getAnalysisLevel() != level || cap.getAnalysisDistance() <= distance) {
                  tag.m_128405_("oldLevel", cap.getAnalysisLevel());
                  tag.m_128405_("oldRange", cap.getAnalysisDistance());
                  cap.setAnalysisLevel(level);
                  cap.setAnalysisDistance(distance);
                  TensuraSkillCapability.sync(player);
               }

            }
         });
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         if (this.isHeld(entity)) {
            TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
               int oldLevel = instance.getOrCreateTag().m_128451_("oldLevel");
               int oldRange = instance.getOrCreateTag().m_128451_("oldRange");
               if (cap.getAnalysisLevel() != oldLevel || cap.getAnalysisDistance() != oldRange) {
                  cap.setAnalysisLevel(oldLevel);
                  cap.setAnalysisDistance(oldRange);
                  TensuraSkillCapability.sync(player);
               }

            });
            return;
         }
      }

   }

   public void onScroll(ManasSkillInstance instance, LivingEntity entity, double delta) {
      CompoundTag tag = instance.getOrCreateTag();
      double newRange = tag.m_128459_("range") + delta;
      if (newRange > 100.0D) {
         newRange = 100.0D;
      } else if (newRange < 2.0D) {
         newRange = 2.0D;
      }

      if (tag.m_128459_("range") != newRange) {
         tag.m_128347_("range", newRange);
         instance.markDirty();
         if (entity instanceof Player) {
            Player player = (Player)entity;
            int range = Math.min((int)newRange * 5, 100);
            TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
               if (cap.getAnalysisDistance() != range) {
                  cap.setAnalysisDistance(range);
                  TensuraSkillCapability.sync(player);
               }

            });
         }
      }

   }
}
