package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.effect.template.Transformation;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;

public class WindTransformSkill extends Skill implements Transformation {
   protected static final String TRANSFORM = "ea832112-f6c0-4390-84e8-765293eca8b5";

   public WindTransformSkill() {
      super(Skill.SkillType.INTRINSIC);
      this.addHeldAttributeModifier(Attributes.f_22279_, "ea832112-f6c0-4390-84e8-765293eca8b5", -0.5D, Operation.MULTIPLY_TOTAL);
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 30.0D;
   }

   public void onLearnSkill(ManasSkillInstance instance, LivingEntity living, UnlockSkillEvent event) {
      if (instance.getMastery() >= 0 && !instance.isTemporarySkill()) {
         Iterator var4 = SkillAPI.getSkillRegistry().getValues().iterator();

         while(var4.hasNext()) {
            ManasSkill manasSkill = (ManasSkill)var4.next();
            if (manasSkill instanceof SpiritualMagic) {
               SpiritualMagic skill = (SpiritualMagic)manasSkill;
               if (skill.getElemental() == MagicElemental.WIND && skill.getLevel().getId() <= 2 && SkillUtils.learnSkill(living, (ManasSkill)skill) && living instanceof Player) {
                  Player player = (Player)living;
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               }
            }
         }

      }
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      if (!(TensuraEPCapability.getEP(entity) < 400000.0D)) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         ManasSkill skill = (ManasSkill)ExtraSkills.MAGIC_WIND_TRANSFORM.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity living, int heldTicks) {
      if (this.failedToActivate(living, (MobEffect)null)) {
         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(living, instance)) {
         return false;
      } else {
         if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, living);
         }

         living.m_9236_().m_6263_((Player)null, living.m_20185_(), living.m_20186_(), living.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(living, ParticleTypes.f_123766_, 2.0D);
         ((ServerLevel)living.m_9236_()).m_8767_((SimpleParticleType)TensuraParticles.SMALL_GUST.get(), living.m_20185_(), living.m_20186_() + (double)living.m_20206_() / 2.0D, living.m_20189_(), 10, 0.08D, 0.08D, 0.08D, 0.15D);
         List<LivingEntity> list = living.m_9236_().m_6443_(LivingEntity.class, living.m_20191_().m_82400_(5.0D), (entity) -> {
            return !entity.m_7306_(living) && entity.m_6084_() && !entity.m_7307_(living);
         });
         if (!list.isEmpty()) {
            DamageSource damageSource = TensuraDamageSources.elementalAttack("tensura.wind_attack", living, this.magiculeCost(living, instance), instance, true);
            Iterator var6 = list.iterator();

            while(var6.hasNext()) {
               LivingEntity target = (LivingEntity)var6.next();
               target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.PARALYSIS.get(), 100, 0, false, false, false));
               target.m_6469_(damageSource, 2.0F);
            }
         }

         return true;
      }
   }
}
