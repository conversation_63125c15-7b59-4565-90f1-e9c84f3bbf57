package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.item.custom.ArmoursaurusGauntletItem;
import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;

public class BodyArmorSkill extends Skill {
   public BodyArmorSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 50.0D;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return true;
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 10 == 0) {
         EquipmentSlot[] var5 = EquipmentSlot.values();
         int var6 = var5.length;

         for(int var7 = 0; var7 < var6; ++var7) {
            EquipmentSlot slot = var5[var7];
            if (entity.m_6844_(slot).m_204117_(TensuraTags.Items.BODY_ARMOR_ITEMS)) {
               this.addMasteryPoint(instance, entity);
            }
         }

         tag.m_128405_("activatedTimes", 0);
      } else {
         tag.m_128405_("activatedTimes", time + 1);
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_6144_()) {
         if (entity.m_6844_(EquipmentSlot.MAINHAND).m_41619_() && !SkillHelper.outOfMagicule(entity, instance)) {
            entity.m_8061_(EquipmentSlot.MAINHAND, ((ArmoursaurusGauntletItem)TensuraToolItems.ARMOURSAURUS_GAUNTLET.get()).m_7968_());
         } else if (entity.m_6844_(EquipmentSlot.MAINHAND).m_150930_((Item)TensuraToolItems.ARMOURSAURUS_GAUNTLET.get())) {
            entity.m_8061_(EquipmentSlot.MAINHAND, ItemStack.f_41583_);
         }
      } else if (!entity.m_6844_(EquipmentSlot.HEAD).m_41619_() && !entity.m_6844_(EquipmentSlot.CHEST).m_41619_() && !entity.m_6844_(EquipmentSlot.LEGS).m_41619_() && !entity.m_6844_(EquipmentSlot.FEET).m_41619_()) {
         if (entity.m_6844_(EquipmentSlot.HEAD).m_150930_((Item)TensuraArmorItems.ARMOURSAURUS_HELMET.get())) {
            entity.m_8061_(EquipmentSlot.HEAD, ItemStack.f_41583_);
         }

         if (entity.m_6844_(EquipmentSlot.CHEST).m_150930_((Item)TensuraArmorItems.ARMOURSAURUS_CHESTPLATE.get())) {
            entity.m_8061_(EquipmentSlot.CHEST, ItemStack.f_41583_);
         }

         if (entity.m_6844_(EquipmentSlot.LEGS).m_150930_((Item)TensuraArmorItems.ARMOURSAURUS_LEGGINGS.get())) {
            entity.m_8061_(EquipmentSlot.LEGS, ItemStack.f_41583_);
         }

         if (entity.m_6844_(EquipmentSlot.FEET).m_150930_((Item)TensuraArmorItems.ARMOURSAURUS_BOOTS.get())) {
            entity.m_8061_(EquipmentSlot.FEET, ItemStack.f_41583_);
         }
      } else if (!SkillHelper.outOfMagicule(entity, instance)) {
         if (entity.m_6844_(EquipmentSlot.HEAD).m_41619_()) {
            entity.m_8061_(EquipmentSlot.HEAD, ((Item)TensuraArmorItems.ARMOURSAURUS_HELMET.get()).m_7968_());
         }

         if (entity.m_6844_(EquipmentSlot.CHEST).m_41619_()) {
            entity.m_8061_(EquipmentSlot.CHEST, ((Item)TensuraArmorItems.ARMOURSAURUS_CHESTPLATE.get()).m_7968_());
         }

         if (entity.m_6844_(EquipmentSlot.LEGS).m_41619_()) {
            entity.m_8061_(EquipmentSlot.LEGS, ((Item)TensuraArmorItems.ARMOURSAURUS_LEGGINGS.get()).m_7968_());
         }

         if (entity.m_6844_(EquipmentSlot.FEET).m_41619_()) {
            entity.m_8061_(EquipmentSlot.FEET, ((Item)TensuraArmorItems.ARMOURSAURUS_BOOTS.get()).m_7968_());
         }
      }

      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11679_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }
}
