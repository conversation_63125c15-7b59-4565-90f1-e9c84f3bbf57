package com.github.manasmods.tensura.ability.magic.spiritual.darkness;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;

public class DarknessMagic extends SpiritualMagic {
   public DarknessMagic() {
      super(MagicElemental.DARKNESS, SpiritualMagic.SpiritLevel.LESSER);
   }

   public int defaultCast() {
      return 40;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 100.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            Level level = entity.m_9236_();
            this.addMasteryPoint(instance, entity);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12053_, SoundSource.PLAYERS, 1.0F, 1.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.DARK_RED_LIGHTNING_SPARK.get());
            float radius = instance.isMastered(entity) ? 15.0F : 7.5F;
            List<LivingEntity> list = level.m_6443_(LivingEntity.class, entity.m_20191_().m_82400_((double)radius), (living) -> {
               return !living.m_7306_(entity) && living.m_6084_() && !living.m_7307_(entity);
            });
            if (!list.isEmpty()) {
               int darknessTime = instance.isMastered(entity) ? 3600 : 1800;
               Iterator var8 = list.iterator();

               while(true) {
                  LivingEntity target;
                  Player player;
                  do {
                     if (!var8.hasNext()) {
                        return;
                     }

                     target = (LivingEntity)var8.next();
                     if (!(target instanceof Player)) {
                        break;
                     }

                     player = (Player)target;
                  } while(player.m_150110_().f_35934_);

                  target.m_147207_(new MobEffectInstance(MobEffects.f_216964_, darknessTime, 4, false, false, false), entity);
               }
            }
         }
      }
   }
}
