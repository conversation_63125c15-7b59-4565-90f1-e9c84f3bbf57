package com.github.manasmods.tensura.ability.magic.spiritual.light;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.block.LightAirBlock;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.world.TensuraGameRules;
import net.minecraft.core.BlockPos;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.level.material.Fluids;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.MinecraftForge;

public class LightMagic extends SpiritualMagic {
   public LightMagic() {
      super(MagicElemental.LIGHT, SpiritualMagic.SpiritLevel.LESSER);
   }

   public boolean isInstant(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public int defaultCast() {
      return 10;
   }

   public int masteryCast() {
      return 1;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 50.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         Level level = entity.m_9236_();
         if (TensuraGameRules.canSkillGrief(level)) {
            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 6.0D);
            if (result.m_6662_() != Type.ENTITY) {
               BlockPos pos = result.m_82425_();
               BlockPos relative = pos.m_121945_(result.m_82434_());
               boolean placeableOnBlock = level.m_8055_(relative).m_60795_() && result.m_6662_() == Type.BLOCK;
               if (placeableOnBlock || level.m_8055_(relative).m_60819_().m_192917_(Fluids.f_76193_)) {
                  if (SkillHelper.outOfMagicule(entity, instance)) {
                     return;
                  }

                  this.addMasteryPoint(instance, entity);
                  SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, instance, relative);
                  if (MinecraftForge.EVENT_BUS.post(preGrief)) {
                     return;
                  }

                  BlockState lightAir = ((Block)TensuraBlocks.LIGHT_AIR.get()).m_49966_();
                  if (level.m_8055_(relative).m_60819_().m_164512_(Fluids.f_76193_)) {
                     lightAir.m_61124_(LightAirBlock.WATERLOGGED, true);
                  }

                  level.m_7731_(relative, lightAir, 11);
                  level.m_142346_(entity, GameEvent.f_157797_, relative);
                  if (!instance.isMastered(entity)) {
                     level.m_186460_(relative, (Block)TensuraBlocks.LIGHT_AIR.get(), 60);
                  }

                  MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, relative));
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_144048_, SoundSource.NEUTRAL, 1.0F, 1.0F);
               }
            }

         }
      }
   }
}
