package com.github.manasmods.tensura.ability.magic.spiritual.fire;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierEntity;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.network.PacketDistributor;

public class FlareCircleMagic extends SpiritualMagic {
   public FlareCircleMagic() {
      super(MagicElemental.FLAME, SpiritualMagic.SpiritLevel.GREATER);
   }

   public int defaultCast() {
      return 60;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 2000.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Entity target = SkillHelper.getTargetingEntity(entity, 20.0D, false, true);
      Vec3 pos;
      if (target != null) {
         pos = target.m_20182_();
      } else {
         BlockHitResult result = SkillHelper.getPlayerPOVHitResult(entity.f_19853_, entity, Fluid.NONE, 20.0D);
         pos = result.m_82450_();
      }

      CompoundTag tag = instance.getOrCreateTag();
      tag.m_128405_("BarrierID", 0);
      tag.m_128347_("circleX", pos.f_82479_);
      tag.m_128347_("circleY", pos.f_82480_);
      tag.m_128347_("circleZ", pos.f_82481_);
      instance.markDirty();
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks == 0 && this.alreadyCasting(entity)) {
         return false;
      } else {
         int castTime = this.castingTime(instance, entity);
         Level level = entity.m_9236_();
         CompoundTag tag = instance.getOrCreateTag();
         BlockPos pos = new BlockPos(tag.m_128459_("circleX"), tag.m_128459_("circleY"), tag.m_128459_("circleZ"));
         if (heldTicks >= castTime) {
            if (heldTicks == castTime + 1) {
               this.addMasteryPoint(instance, entity);
            }

            BarrierEntity.spawnLastingBarrier((EntityType)TensuraEntityTypes.FLARE_CIRCLE.get(), 80.0F, 5.0F, 7.0F, 30, entity.m_21233_() / 2.0F, new Vec3((double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_()), entity, instance, this.magiculeCost(entity, instance), 1000.0D, heldTicks);
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
            return heldTicks - castTime <= (instance.isMastered(entity) ? 200 : 100);
         } else {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               this.addCastingParticle(instance, player, heldTicks);
               TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
                  return entity;
               }), new RequestFxSpawningPacket(new ResourceLocation("tensura:flare_circle_circle"), pos, 0.0D, 0.0D, 0.0D, 0, true));
            }

            return true;
         }
      }
   }
}
