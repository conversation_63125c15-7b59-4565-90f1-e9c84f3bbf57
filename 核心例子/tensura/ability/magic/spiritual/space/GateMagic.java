package com.github.manasmods.tensura.ability.magic.spiritual.space;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.menu.SpatialMenu;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.network.NetworkHooks;

public class GateMagic extends SpiritualMagic {
   public GateMagic() {
      super(MagicElemental.SPACE, SpiritualMagic.SpiritLevel.MEDIUM);
   }

   public int defaultCast() {
      return 300;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 50.0D;
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      ManasSkill skill = (ManasSkill)ExtraSkills.SPATIAL_MOTION.get();
      ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
      manipulation.setMastery(-100);
      if (SkillAPI.getSkillsFrom(entity).learnSkill(manipulation) && entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
      }

   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (MobEffectHelper.noTeleportation(entity)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237115_("tensura.skill.spatial_blockade").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }

      } else if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            this.addMasteryPoint(instance, entity);
            if (entity instanceof ServerPlayer) {
               ServerPlayer serverPlayer = (ServerPlayer)entity;
               if (entity.f_19853_.m_46472_() == TensuraDimensions.LABYRINTH) {
                  serverPlayer.m_6330_(SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  serverPlayer.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
               } else {
                  NetworkHooks.openScreen(serverPlayer, new SimpleMenuProvider(SpatialMenu::new, Component.m_237119_()), (buf) -> {
                     buf.writeBoolean(true);
                  });
                  serverPlayer.m_6330_(SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }

            entity.m_21195_((MobEffect)TensuraMobEffects.WARPING.get());
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            instance.setCoolDown(instance.isMastered(entity) ? 10 : 20);
         }
      }
   }
}
