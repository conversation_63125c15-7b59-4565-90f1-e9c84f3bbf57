package com.github.manasmods.tensura.ability.magic.summon;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.api.entity.subclass.IElementalSpirit;
import com.github.manasmods.tensura.api.entity.subclass.ISummonable;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.List;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.block.Blocks;

public abstract class SummonElementalMagic extends SummoningMagic<TamableAnimal> {
   private final SpiritualMagic.SpiritLevel level;

   public SummonElementalMagic(SpiritualMagic.SpiritLevel level) {
      this.level = level;
   }

   public int defaultCast() {
      return 100;
   }

   public int masteryCast() {
      return 60;
   }

   public void onLearnSkill(ManasSkillInstance instance, LivingEntity living, UnlockSkillEvent event) {
      instance.setMode(0);
   }

   public int modes() {
      return 5;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (!(entity instanceof Player)) {
         return 0;
      } else {
         Player player = (Player)entity;
         int currentSpirit = instance.getMode() - 1;
         int nextMode = 0;

         for(int tries = 0; nextMode == 0 && tries <= 5; ++tries) {
            if (reverse) {
               currentSpirit = currentSpirit - 1 == -1 ? 4 : currentSpirit - 1;
            } else {
               currentSpirit = currentSpirit + 1 == 5 ? 0 : currentSpirit + 1;
            }

            if (this.containsSpirit(instance, player, currentSpirit)) {
               nextMode = currentSpirit + 1;
            }
         }

         return nextMode;
      }
   }

   private boolean containsSpirit(TensuraSkillInstance instance, Player player, int spirit) {
      int level = this.getLevel().getId();
      List<MagicElemental> elementals = MagicElemental.getCommonElementals();
      if (TensuraSkillCapability.getSpiritLevel(player, ((MagicElemental)elementals.get(spirit)).getId()) >= level) {
         return true;
      } else {
         CompoundTag tag = instance.getTag();
         if (tag == null) {
            return false;
         } else if (!tag.m_128441_("SpiritTamed")) {
            return false;
         } else {
            return tag.m_128469_("SpiritTamed").m_128451_(((MagicElemental)MagicElemental.getCommonElementals().get(spirit)).getNamespace()) >= level;
         }
      }
   }

   public static void addSpiritSummonLevel(ManasSkillInstance instance, IElementalSpirit spirit) {
      CompoundTag tag = instance.getOrCreateTag();
      CompoundTag spiritTamed;
      if (tag.m_128441_("SpiritTamed")) {
         spiritTamed = tag.m_128469_("SpiritTamed");
         int currentLevel = spiritTamed.m_128451_(spirit.getElemental().getNamespace());
         if (currentLevel >= spirit.getSpiritLevel().getId()) {
            return;
         }

         spiritTamed.m_128405_(spirit.getElemental().getNamespace(), spirit.getSpiritLevel().getId());
         instance.markDirty();
      } else {
         spiritTamed = new CompoundTag();
         spiritTamed.m_128405_(spirit.getElemental().getNamespace(), spirit.getSpiritLevel().getId());
         tag.m_128365_("SpiritTamed", spiritTamed);
         instance.markDirty();
      }

   }

   protected int getSuccessCooldown(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity) ? 3600 : 6000;
   }

   protected void removeExistingSummon(ManasSkillInstance instance, LivingEntity entity) {
      TamableAnimal summon = (TamableAnimal)SkillHelper.getTargetingEntity(TamableAnimal.class, entity, 30.0D, 0.2D, false);
      if (summon != null) {
         if (summon instanceof IElementalSpirit) {
            IElementalSpirit spirit = (IElementalSpirit)summon;
            if (summon.m_21830_(entity)) {
               if (spirit.getSummoningTick() >= 0) {
                  if (spirit.getSpiritLevel() == this.getLevel()) {
                     summon.m_6469_(TensuraDamageSources.noEnergySource(entity), summon.m_21233_());
                     instance.setCoolDown(0);
                  }
               }
            }
         }
      }
   }

   protected void addAdditionalSummonData(ManasSkillInstance instance, LivingEntity entity, TamableAnimal summon) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         summon.m_21828_(player);
      }

      if (summon instanceof ISummonable) {
         ISummonable summonable = (ISummonable)summon;
         summonable.setSummoningTick(6000);
      }

      summon.m_217045_();
   }

   protected ResourceLocation getSummoningFxLocation(ManasSkillInstance instance) {
      String name = ((MagicElemental)MagicElemental.getCommonElementals().get(instance.getMode() - 1)).getNamespace();
      return new ResourceLocation("tensura:" + name + "_circle");
   }

   protected ParticleOptions getSummoningParticle(ManasSkillInstance instance) {
      Object var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_152537_.m_49966_());
         break;
      case 2:
      default:
         var10000 = (SimpleParticleType)TensuraParticles.RED_FIRE.get();
         break;
      case 3:
         var10000 = ParticleTypes.f_123789_;
         break;
      case 4:
         var10000 = (SimpleParticleType)TensuraParticles.WATER_BUBBLE.get();
         break;
      case 5:
         var10000 = (SimpleParticleType)TensuraParticles.SMALL_GUST.get();
      }

      return (ParticleOptions)var10000;
   }

   protected SoundEvent getSummoningSound(ManasSkillInstance instance) {
      SoundEvent var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = SoundEvents.f_144135_;
         break;
      case 2:
      default:
         var10000 = SoundEvents.f_11705_;
         break;
      case 3:
         var10000 = SoundEvents.f_11852_;
         break;
      case 4:
         var10000 = SoundEvents.f_12324_;
         break;
      case 5:
         var10000 = SoundEvents.f_12317_;
      }

      return var10000;
   }

   protected SoundEvent getFailSound(ManasSkillInstance instance) {
      SoundEvent var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = SoundEvents.f_12059_;
         break;
      case 2:
      default:
         var10000 = SoundEvents.f_11703_;
         break;
      case 3:
         var10000 = SoundEvents.f_11787_;
         break;
      case 4:
         var10000 = SoundEvents.f_215691_;
      }

      return var10000;
   }

   public SpiritualMagic.SpiritLevel getLevel() {
      return this.level;
   }
}
