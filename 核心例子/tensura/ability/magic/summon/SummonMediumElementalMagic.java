package com.github.manasmods.tensura.ability.magic.summon;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import org.jetbrains.annotations.NotNull;

public class SummonMediumElementalMagic extends SummonElementalMagic {
   public SummonMediumElementalMagic() {
      super(SpiritualMagic.SpiritLevel.MEDIUM);
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 500.0D;
   }

   @NotNull
   public Component getModeName(int mode) {
      Object var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("entity.tensura.beast_gnome");
         break;
      case 2:
         var10000 = Component.m_237115_("entity.tensura.salamander");
         break;
      case 3:
         var10000 = Component.m_237115_("entity.tensura.winged_cat");
         break;
      case 4:
         var10000 = Component.m_237115_("entity.tensura.aqua_frog");
         break;
      case 5:
         var10000 = Component.m_237115_("entity.tensura.feathered_serpent");
         break;
      default:
         var10000 = super.getModeName(mode);
      }

      return (Component)var10000;
   }

   protected EntityType<? extends TamableAnimal> getSummonedType(ManasSkillInstance instance) {
      EntityType var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = (EntityType)TensuraEntityTypes.BEAST_GNOME.get();
         break;
      case 2:
      default:
         var10000 = (EntityType)TensuraEntityTypes.SALAMANDER.get();
         break;
      case 3:
         var10000 = (EntityType)TensuraEntityTypes.WINGED_CAT.get();
         break;
      case 4:
         var10000 = (EntityType)TensuraEntityTypes.AQUA_FROG.get();
         break;
      case 5:
         var10000 = (EntityType)TensuraEntityTypes.FEATHERED_SERPENT.get();
      }

      return var10000;
   }
}
