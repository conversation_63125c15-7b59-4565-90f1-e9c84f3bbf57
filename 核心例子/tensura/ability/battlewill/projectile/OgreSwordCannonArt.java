package com.github.manasmods.tensura.ability.battlewill.projectile;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.projectile.AuraSlashProjectile;
import com.github.manasmods.tensura.registry.battlewill.MeleeArts;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.player.Player;

public class OgreSwordCannonArt extends Battewill {
   public double learningCost() {
      return 2000.0D;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return SkillUtils.isSkillMastered(entity, (ManasSkill)MeleeArts.OGRE_SWORD_GUILLOTINE.get());
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 200.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      instance.getOrCreateTag().m_128405_("PowerScale", 0);
      instance.markDirty();
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      CompoundTag tag = instance.getOrCreateTag();
      int power = tag.m_128451_("PowerScale");
      int max = instance.isMastered(entity) ? 100 : 50;
      if (heldTicks > 0 && heldTicks % 4 == 0 && power < max) {
         tag.m_128405_("PowerScale", power + (instance.isMastered(entity) ? 2 : 1));
         instance.markDirty();
      }

      TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_175830_, 1.0D);
      if (entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237110_("tensura.skill.power_scale", new Object[]{(double)tag.m_128451_("PowerScale") / 10.0D}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), true);
      }

      return true;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      CompoundTag tag = instance.getOrCreateTag();
      int power = tag.m_128451_("PowerScale") / 10;
      tag.m_128405_("PowerScale", 0);
      instance.markDirty();
      if (power >= 1) {
         double cost = this.auraCost(entity, instance) * (double)power;
         if (!SkillHelper.outOfAura(entity, cost)) {
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            if (power >= 2) {
               this.addMasteryPoint(instance, entity);
            }

            AuraSlashProjectile slash = new AuraSlashProjectile(entity.m_9236_(), entity);
            slash.setSpeed(0.5F);
            slash.setDamage((float)entity.m_21133_(Attributes.f_22281_) * (1.5F + 0.5F * (float)power));
            slash.setSize((float)power);
            slash.setLife(20 * power);
            slash.setApCost(cost * (double)power);
            slash.setSkill(instance);
            slash.m_20242_(true);
            slash.shootFromRot(entity.m_20154_().m_82542_(1.0D, 0.0D, 1.0D));
            slash.m_146884_(entity.m_20182_().m_82520_(0.0D, (double)(entity.m_20206_() / 2.0F - slash.m_20206_() / 2.0F), 0.0D));
            entity.m_9236_().m_7967_(slash);
            entity.f_19853_.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215778_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }
      }
   }
}
