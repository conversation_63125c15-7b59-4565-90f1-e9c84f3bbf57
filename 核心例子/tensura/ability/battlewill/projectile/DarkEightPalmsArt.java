package com.github.manasmods.tensura.ability.battlewill.projectile;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.projectile.AuraBulletProjectile;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;

public class DarkEightPalmsArt extends Battewill {
   public double learningCost() {
      return 2000.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 200.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      instance.getOrCreateTag().m_128405_("PowerScale", 0);
      instance.markDirty();
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      CompoundTag tag = instance.getOrCreateTag();
      int power = tag.m_128451_("PowerScale");
      if (heldTicks > 0 && heldTicks % 2 == 0 && power < 80) {
         tag.m_128405_("PowerScale", power + (instance.isMastered(entity) ? 2 : 1));
         instance.markDirty();
      }

      TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_175830_, 1.0D);
      if (entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237110_("tensura.skill.power_scale", new Object[]{(double)tag.m_128451_("PowerScale") / 10.0D}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), true);
      }

      return true;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      CompoundTag tag = instance.getOrCreateTag();
      int power = tag.m_128451_("PowerScale") / 10;
      tag.m_128405_("PowerScale", 0);
      instance.markDirty();
      if (power >= 1) {
         double cost = this.auraCost(entity, instance) * (double)power;
         if (!SkillHelper.outOfAura(entity, cost)) {
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            if (power >= 4) {
               this.addMasteryPoint(instance, entity);
            }

            Entity target = SkillHelper.getTargetingEntity(entity, 40.0D, false, true);
            Vec3 pos;
            if (target != null) {
               pos = target.m_146892_();
            } else {
               BlockHitResult result = SkillHelper.getPlayerPOVHitResult(entity.f_19853_, entity, Fluid.NONE, 40.0D);
               pos = result.m_82450_().m_82520_(0.0D, 0.5D, 0.0D);
            }

            this.spawnAuraBullets(instance, entity, pos, power);
         }
      }
   }

   private void spawnAuraBullets(ManasSkillInstance instance, LivingEntity entity, Vec3 pos, int amount) {
      int rot = 360 / amount;

      for(int i = 0; i < amount; ++i) {
         Vec3 bulletPos = entity.m_146892_().m_82520_(0.0D, 2.0D, 0.0D).m_82549_((new Vec3(0.0D, 2.0D, 0.0D)).m_82535_(((float)(rot * i) - (float)rot / 2.0F) * 0.017453292F).m_82496_(-entity.m_146909_() * 0.017453292F).m_82524_(-entity.m_146908_() * 0.017453292F));
         AuraBulletProjectile bullet = new AuraBulletProjectile(entity.m_9236_(), entity);
         bullet.setSpeed(1.5F);
         bullet.m_146884_(bulletPos);
         bullet.shootFromRot(pos.m_82546_(bulletPos).m_82541_());
         bullet.setLife(50);
         bullet.setDamage(100.0F);
         bullet.m_20242_(true);
         bullet.setExplosionRadius(3.0F);
         bullet.setSize(0.5F);
         bullet.setSkill(instance);
         bullet.setApCost(this.magiculeCost(entity, instance));
         bullet.setColor(AuraBulletProjectile.AuraColor.YELLOW);
         entity.m_9236_().m_7967_(bullet);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }

   }
}
