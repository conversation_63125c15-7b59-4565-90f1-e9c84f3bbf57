package com.github.manasmods.tensura.ability.battlewill.projectile;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.entity.magic.projectile.AuraBulletProjectile;
import com.github.manasmods.tensura.registry.battlewill.ProjectileArts;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

public class DeathMarchDanceArt extends <PERSON><PERSON>will {
   public double learningCost() {
      return 1000.0D;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return SkillUtils.isSkillMastered(entity, (ManasSkill)ProjectileArts.MAXIMUM_MAGIC_BULLET.get());
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 100.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      AuraBulletProjectile bullet = new AuraBulletProjectile(entity.m_9236_(), entity);
      bullet.setSize(0.75F);
      bullet.setSkill(instance);
      bullet.setDamage(100.0F);
      bullet.setLife(1200);
      bullet.m_20242_(true);
      bullet.m_146884_(entity.m_146892_().m_82520_(0.0D, 1.0D, 0.0D));
      entity.m_9236_().m_7967_(bullet);
      CompoundTag tag = instance.getOrCreateTag();
      tag.m_128405_("BulletID", bullet.m_19879_());
      instance.markDirty();
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      Level level = entity.m_9236_();
      CompoundTag tag = instance.getOrCreateTag();
      int id = tag.m_128451_("BulletID");
      Entity idEntity = level.m_6815_(id);
      if (idEntity instanceof AuraBulletProjectile) {
         AuraBulletProjectile bullet = (AuraBulletProjectile)idEntity;
         int oldSize = (int)bullet.getSize();
         int time = instance.isMastered(entity) ? 20 : 40;
         bullet.m_146884_(entity.m_146892_().m_82520_(0.0D, 1.0D + 0.25D * (double)(bullet.getSize() - 1.0F), 0.0D));
         int maxSize = instance.isMastered(entity) ? 10 : 5;
         double timer;
         if (oldSize < maxSize) {
            if (heldTicks > 0 && heldTicks % time == 0 && !SkillHelper.outOfAura(entity, instance)) {
               bullet.setSize((float)(oldSize + 1));
               bullet.setDamage(bullet.getDamage() + 100.0F);
               bullet.setColor(bullet.getColorBySize(bullet.getSize()));
            }

            timer = (double)oldSize + (double)(heldTicks - time * oldSize) / (double)time;
         } else {
            timer = (double)maxSize;
         }

         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.power_scale", new Object[]{this.roundDouble.format(timer)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), true);
         }

         return true;
      } else {
         tag.m_128405_("BulletID", 0);
         instance.markDirty();
         return false;
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      Level level = entity.m_9236_();
      CompoundTag tag = instance.getOrCreateTag();
      int id = tag.m_128451_("BulletID");
      Entity idEntity = level.m_6815_(id);
      if (idEntity instanceof AuraBulletProjectile) {
         AuraBulletProjectile bullet = (AuraBulletProjectile)idEntity;
         this.spawnAuraBullets(instance, entity, (int)bullet.getSize() * 5, bullet.m_20182_(), bullet.getAuraColor());
         bullet.m_146870_();
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 3.0F, 1.0F);
      } else {
         tag.m_128405_("BulletID", 0);
         instance.markDirty();
      }
   }

   private void spawnAuraBullets(ManasSkillInstance instance, LivingEntity entity, int amount, Vec3 pos, AuraBulletProjectile.AuraColor dyeColor) {
      if (amount > 0) {
         if (amount >= 3) {
            instance.addMasteryPoint(entity);
         }

         int rot = 360 / amount;

         for(int i = 0; i < amount; ++i) {
            Vec3 bulletPos = pos.m_82549_((new Vec3(0.0D, 1.0D, 0.0D)).m_82535_(((float)(rot * i) - (float)rot / 2.0F) * 0.017453292F).m_82496_(1.5707964F));
            AuraBulletProjectile bullet = new AuraBulletProjectile(entity.m_9236_(), entity);
            bullet.setSpeed(0.5F);
            bullet.setColor(dyeColor);
            bullet.m_146884_(bulletPos);
            bullet.shootFromRot(bulletPos.m_82546_(pos).m_82541_());
            bullet.setDamage(25.0F);
            bullet.setExplosionRadius(4.0F);
            bullet.setSkill(instance);
            bullet.setApCost(this.magiculeCost(entity, instance));
            entity.m_9236_().m_7967_(bullet);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

      }
   }
}
