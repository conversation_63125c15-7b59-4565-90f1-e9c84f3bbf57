package com.github.manasmods.tensura.ability.battlewill.projectile;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierEntity;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.Vec3;

public class OgreFlameArt extends Battewill {
   public double learningCost() {
      return 1000.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 100.0D;
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      Level level = entity.m_9236_();
      int castTime = 100;
      if (heldTicks >= castTime) {
         if (heldTicks == castTime + 1) {
            this.addMasteryPoint(instance, entity);
         }

         BlockPos pos = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 15.0D).m_82425_();
         BarrierEntity.spawnLastingBarrier((EntityType)TensuraEntityTypes.FLARE_CIRCLE.get(), 50.0F, 4.0F, 7.0F, 30, entity.m_21233_() / 2.0F, new Vec3((double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_()), entity, instance, this.magiculeCost(entity, instance), 100.0D, heldTicks, true);
         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
         return heldTicks - castTime <= (instance.isMastered(entity) ? 200 : 100);
      } else {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            double sec = (double)heldTicks / 20.0D;
            player.m_5661_(Component.m_237110_("tensura.magic.cast_time.max", new Object[]{this.roundDouble.format(sec), (double)castTime / 20.0D}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), true);
         }

         if (heldTicks % 5 == 0) {
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.RED_FIRE.get(), 2.0D);
         }

         return true;
      }
   }
}
