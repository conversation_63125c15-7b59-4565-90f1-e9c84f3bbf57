package com.github.manasmods.tensura.ability.battlewill.melee;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.AuraSlashProjectile;
import com.github.manasmods.tensura.registry.battlewill.MeleeArts;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.common.ToolActions;

public class AuraSlashSkill extends Battewill {
   public double learningCost() {
      return 100.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 50.0D;
   }

   private boolean canSlash(ItemStack stack) {
      return stack.canPerformAction(ToolActions.SWORD_DIG) ? true : stack.canPerformAction(ToolActions.SWORD_SWEEP);
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      boolean success = false;
      AuraSlashProjectile offSlash;
      float offDamage;
      if (this.canSlash(entity.m_21205_())) {
         if (SkillHelper.outOfAura(entity, instance)) {
            return;
         }

         success = true;
         offSlash = new AuraSlashProjectile(entity.m_9236_(), entity);
         offSlash.setSpeed(1.5F);
         offDamage = DamageSourceHelper.getMainWeaponDamage(entity, (Entity)null);
         offSlash.setDamage(instance.isMastered(entity) ? offDamage * 2.0F : offDamage);
         offSlash.setApCost(this.auraCost(entity, instance));
         offSlash.setSkill(instance);
         offSlash.m_20242_(true);
         offSlash.setPosAndShoot(entity);
         entity.m_9236_().m_7967_(offSlash);
         entity.m_21011_(InteractionHand.MAIN_HAND, true);
      }

      if ((!success || instance.isMastered(entity)) && this.canSlash(entity.m_21206_())) {
         if (!success && SkillHelper.outOfAura(entity, instance)) {
            return;
         }

         success = true;
         offSlash = new AuraSlashProjectile(entity.m_9236_(), entity);
         offSlash.setSpeed(1.5F);
         offDamage = DamageSourceHelper.getOffWeaponDamage(entity, (Entity)null);
         offSlash.setDamage(instance.isMastered(entity) ? offDamage * 2.0F : offDamage);
         offSlash.setApCost(this.auraCost(entity, instance));
         offSlash.setSkill(instance);
         offSlash.m_20242_(true);
         offSlash.setPosAndShoot(entity);
         offSlash.setPosDirection(entity, TensuraProjectile.PositionDirection.LEFT);
         entity.m_9236_().m_7967_(offSlash);
         entity.m_21011_(InteractionHand.OFF_HAND, true);
      }

      if (success) {
         instance.addMasteryPoint(entity);
         instance.setCoolDown(instance.isMastered(entity) ? 1 : 2);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12520_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }

   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      ManasSkill skill = (ManasSkill)MeleeArts.HEAVY_SLASH.get();
      ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
      manipulation.setMastery(-100);
      if (storage.learnSkill(manipulation) && entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
      }

   }
}
