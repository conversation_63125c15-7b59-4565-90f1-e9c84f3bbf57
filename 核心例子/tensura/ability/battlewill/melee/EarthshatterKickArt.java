package com.github.manasmods.tensura.ability.battlewill.melee;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;

public class EarthshatterKickArt extends Battewill {
   public double learningCost() {
      return 150.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 150.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_20096_() && !entity.isInFluidType()) {
         if (!SkillHelper.outOfAura(entity, instance)) {
            Level level = entity.m_9236_();
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            this.addMasteryPoint(instance, entity);
            AABB aabb = entity.m_20191_().m_82400_(5.0D);
            List<LivingEntity> list = level.m_6443_(LivingEntity.class, aabb, (living) -> {
               return living.m_20096_() && living != entity;
            });
            if (!list.isEmpty()) {
               float damage = instance.isMastered(entity) ? 20.0F : 10.0F;
               Iterator var7 = list.iterator();

               while(var7.hasNext()) {
                  LivingEntity target = (LivingEntity)var7.next();
                  target.m_6469_(DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(entity), 0.0D, instance), damage);
                  SkillHelper.knockBack(entity, target, 0.5F);
               }
            }

            TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123813_);
            TensuraParticleHelper.spawnServerGroundSlamParticle(entity, 10, 2.0F);
            TensuraParticleHelper.spawnServerGroundSlamParticle(entity, 10, 3.0F);
            TensuraParticleHelper.spawnServerGroundSlamParticle(entity, 10, 4.0F);
            TensuraParticleHelper.spawnServerGroundSlamParticle(entity, 10, 5.0F);
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11913_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            if (TensuraGameRules.canSkillGrief(level)) {
               SkillHelper.launchBlock(entity, entity.m_20182_(), 5, 1, 0.5F, 0.5F, (blockState) -> {
                  return entity.m_217043_().m_188503_(3) != 1 ? false : blockState.m_204336_(TensuraTags.Blocks.EARTH_DOMINATING);
               }, (pos) -> {
                  return !pos.equals(entity.m_20097_()) && !pos.equals(entity.m_20097_().m_7495_());
               }, instance);
            }

         }
      }
   }
}
