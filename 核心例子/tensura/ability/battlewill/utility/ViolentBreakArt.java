package com.github.manasmods.tensura.ability.battlewill.utility;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.UnmodifiableIterator;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.network.PacketDistributor;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.IForgeRegistry;

public class ViolentBreakArt extends Battewill {
   public static ImmutableList<MobEffect> harmfulEffects;

   public double learningCost() {
      return 150.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 150.0D;
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks >= 60) {
         if (SkillHelper.outOfAura(entity, instance)) {
            return false;
         } else {
            this.addMasteryPoint(instance, entity);
            if (!harmfulEffects.isEmpty()) {
               UnmodifiableIterator var4 = harmfulEffects.iterator();

               while(var4.hasNext()) {
                  MobEffect effect = (MobEffect)var4.next();
                  SkillHelper.removeLevelsOfEffect(entity, effect, 1);
               }
            }

            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.STRENGTHEN.get(), 12000, instance.isMastered(entity) ? 1 : 0, false, false, true));
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 2.0F, 1.0F);
            return false;
         }
      } else {
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
         if (heldTicks % 10 == 0) {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return entity;
            }), new RequestFxSpawningPacket(new ResourceLocation("tensura:violent_break"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
         }

         return true;
      }
   }

   public static void loadConfig() {
      Stream var10000 = ((List)TensuraConfig.INSTANCE.artsConfig.violentBreakEffect.get()).stream().map(ResourceLocation::new);
      IForgeRegistry var10001 = ForgeRegistries.MOB_EFFECTS;
      Objects.requireNonNull(var10001);
      harmfulEffects = ImmutableList.copyOf((Collection)var10000.map(var10001::getValue).filter(Objects::nonNull).collect(Collectors.toList()));
   }
}
