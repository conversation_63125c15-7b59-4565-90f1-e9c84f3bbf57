package com.github.manasmods.tensura.ability.battlewill.utility;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.item.templates.SimpleShieldItem;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class AuraShieldArt extends Bat<PERSON>will {
   public double learningCost() {
      return 100.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 80.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21206_().m_41619_()) {
         if (SkillHelper.outOfAura(entity, instance)) {
            return;
         }

         instance.addMasteryPoint(entity);
         entity.m_21008_(InteractionHand.OFF_HAND, ((SimpleShieldItem)TensuraToolItems.AURA_SHIELD.get()).m_7968_());
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 0.5F, 1.0F);
      } else if (entity.m_21205_().m_41619_()) {
         if (SkillHelper.outOfAura(entity, instance)) {
            return;
         }

         instance.addMasteryPoint(entity);
         entity.m_21008_(InteractionHand.MAIN_HAND, ((SimpleShieldItem)TensuraToolItems.AURA_SHIELD.get()).m_7968_());
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 0.5F, 1.0F);
      } else {
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12315_, SoundSource.PLAYERS, 0.5F, 1.0F);
      }

   }
}
