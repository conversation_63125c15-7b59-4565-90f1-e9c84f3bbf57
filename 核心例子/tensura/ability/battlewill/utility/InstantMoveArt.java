package com.github.manasmods.tensura.ability.battlewill.utility;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;

public class InstantMoveArt extends Battewill {
   public double learningCost() {
      return 100.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 50.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_20096_() || entity.isInFluidType()) {
         if (!SkillHelper.outOfAura(entity, instance)) {
            Level level = entity.m_9236_();
            double distance = instance.isMastered(entity) ? 11.0D : 6.0D;
            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, distance);
            Vec3 vec3 = SkillHelper.getFloorPos(result.m_82425_().m_121945_(result.m_82434_()));
            if (!entity.m_9236_().m_6857_().m_61937_(new BlockPos(vec3.m_7096_(), vec3.m_7098_(), vec3.m_7094_()))) {
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  player.m_5661_(Component.m_237115_("tensura.skill.teleport.out_border").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
               }

            } else {
               Vec3 source = entity.m_20182_().m_82520_(0.0D, (double)(entity.m_20206_() / 2.0F), 0.0D);
               Vec3 offSetToTarget = vec3.m_82546_(source);

               for(int particleIndex = 1; particleIndex < Mth.m_14107_(offSetToTarget.m_82553_()); ++particleIndex) {
                  Vec3 particlePos = source.m_82549_(offSetToTarget.m_82541_().m_82490_((double)particleIndex));
                  ((ServerLevel)level).m_8767_(ParticleTypes.f_123796_, particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_, 1, 0.0D, 0.0D, 0.0D, 0.0D);
               }

               entity.m_183634_();
               entity.m_20219_(vec3);
               entity.f_19812_ = true;
               instance.addMasteryPoint(entity);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         }
      }
   }
}
