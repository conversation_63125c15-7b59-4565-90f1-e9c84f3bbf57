package com.github.manasmods.tensura.registry.particle;

import net.minecraft.core.particles.ParticleType;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraParticles {
   private static final DeferredRegister<ParticleType<?>> registry;
   public static final RegistryObject<SimpleParticleType> BLANK;
   public static final RegistryObject<SimpleParticleType> DARK_RED_LIGHTNING_SPARK;
   public static final RegistryObject<SimpleParticleType> BLACK_LIGHTNING_SPARK;
   public static final RegistryObject<SimpleParticleType> BLACK_LIGHTNING_EFFECT;
   public static final RegistryObject<SimpleParticleType> DARK_PURPLE_LIGHTNING_SPARK;
   public static final RegistryObject<SimpleParticleType> LIGHTNING_SPARK;
   public static final RegistryObject<SimpleParticleType> LIGHTNING_EFFECT;
   public static final RegistryObject<SimpleParticleType> PURPLE_LIGHTNING_SPARK;
   public static final RegistryObject<SimpleParticleType> YELLOW_LIGHTNING_SPARK;
   public static final RegistryObject<SimpleParticleType> BATS_MODE;
   public static final RegistryObject<SimpleParticleType> CHAOS_EATER_EFFECT;
   public static final RegistryObject<SimpleParticleType> SOLAR_FLASH;
   public static final RegistryObject<SimpleParticleType> SONIC_BLAST;
   public static final RegistryObject<SimpleParticleType> SONIC_SOUND;
   public static final RegistryObject<SimpleParticleType> SOUND_GIANT;
   public static final RegistryObject<SimpleParticleType> SOUND_REQUIEM;
   public static final RegistryObject<SimpleParticleType> SOUL;
   public static final RegistryObject<SimpleParticleType> SNOWFLAKE;
   public static final RegistryObject<SimpleParticleType> GUST;
   public static final RegistryObject<SimpleParticleType> SMALL_GUST;
   public static final RegistryObject<SimpleParticleType> ACID_BUBBLE;
   public static final RegistryObject<SimpleParticleType> ACID_EFFECT;
   public static final RegistryObject<SimpleParticleType> BOG_BUBBLE;
   public static final RegistryObject<SimpleParticleType> BOG_EFFECT;
   public static final RegistryObject<SimpleParticleType> HEAT_EFFECT;
   public static final RegistryObject<SimpleParticleType> HELL_FLARE_FIRE;
   public static final RegistryObject<SimpleParticleType> MUD_BUBBLE;
   public static final RegistryObject<SimpleParticleType> MUD_EFFECT;
   public static final RegistryObject<SimpleParticleType> PARALYSING_BUBBLE;
   public static final RegistryObject<SimpleParticleType> PARALYSING_EFFECT;
   public static final RegistryObject<SimpleParticleType> POISON_BUBBLE;
   public static final RegistryObject<SimpleParticleType> POISON_EFFECT;
   public static final RegistryObject<SimpleParticleType> STEAM_EFFECT;
   public static final RegistryObject<SimpleParticleType> WATER_BUBBLE;
   public static final RegistryObject<SimpleParticleType> WATER_EFFECT;
   public static final RegistryObject<SimpleParticleType> BLACK_FIRE;
   public static final RegistryObject<SimpleParticleType> RED_FIRE;
   public static final RegistryObject<SimpleParticleType> PLASMA_FIRE;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.PARTICLE_TYPES, "tensura");
      BLANK = registry.register("blank", () -> {
         return new SimpleParticleType(false);
      });
      DARK_RED_LIGHTNING_SPARK = registry.register("dark_red_lightning_spark", () -> {
         return new SimpleParticleType(false);
      });
      BLACK_LIGHTNING_SPARK = registry.register("black_lightning_spark", () -> {
         return new SimpleParticleType(false);
      });
      BLACK_LIGHTNING_EFFECT = registry.register("black_lightning_effect", () -> {
         return new SimpleParticleType(false);
      });
      DARK_PURPLE_LIGHTNING_SPARK = registry.register("dark_purple_lightning_spark", () -> {
         return new SimpleParticleType(false);
      });
      LIGHTNING_SPARK = registry.register("lightning_spark", () -> {
         return new SimpleParticleType(false);
      });
      LIGHTNING_EFFECT = registry.register("lightning_effect", () -> {
         return new SimpleParticleType(false);
      });
      PURPLE_LIGHTNING_SPARK = registry.register("purple_lightning_spark", () -> {
         return new SimpleParticleType(false);
      });
      YELLOW_LIGHTNING_SPARK = registry.register("yellow_lightning_spark", () -> {
         return new SimpleParticleType(false);
      });
      BATS_MODE = registry.register("bats_mode", () -> {
         return new SimpleParticleType(false);
      });
      CHAOS_EATER_EFFECT = registry.register("chaos_eater_effect", () -> {
         return new SimpleParticleType(false);
      });
      SOLAR_FLASH = registry.register("solar_flash", () -> {
         return new SimpleParticleType(false);
      });
      SONIC_BLAST = registry.register("sonic_blast", () -> {
         return new SimpleParticleType(false);
      });
      SONIC_SOUND = registry.register("sonic_sound", () -> {
         return new SimpleParticleType(false);
      });
      SOUND_GIANT = registry.register("sound_giant", () -> {
         return new SimpleParticleType(false);
      });
      SOUND_REQUIEM = registry.register("sound_requiem", () -> {
         return new SimpleParticleType(false);
      });
      SOUL = registry.register("soul", () -> {
         return new SimpleParticleType(false);
      });
      SNOWFLAKE = registry.register("snowflake", () -> {
         return new SimpleParticleType(false);
      });
      GUST = registry.register("gust", () -> {
         return new SimpleParticleType(false);
      });
      SMALL_GUST = registry.register("small_gust", () -> {
         return new SimpleParticleType(false);
      });
      ACID_BUBBLE = registry.register("acid_bubble", () -> {
         return new SimpleParticleType(false);
      });
      ACID_EFFECT = registry.register("acid_effect", () -> {
         return new SimpleParticleType(false);
      });
      BOG_BUBBLE = registry.register("bog_bubble", () -> {
         return new SimpleParticleType(false);
      });
      BOG_EFFECT = registry.register("bog_effect", () -> {
         return new SimpleParticleType(false);
      });
      HEAT_EFFECT = registry.register("heat_effect", () -> {
         return new SimpleParticleType(false);
      });
      HELL_FLARE_FIRE = registry.register("hell_flare_fire", () -> {
         return new SimpleParticleType(false);
      });
      MUD_BUBBLE = registry.register("mud_bubble", () -> {
         return new SimpleParticleType(false);
      });
      MUD_EFFECT = registry.register("mud_effect", () -> {
         return new SimpleParticleType(false);
      });
      PARALYSING_BUBBLE = registry.register("paralysing_bubble", () -> {
         return new SimpleParticleType(false);
      });
      PARALYSING_EFFECT = registry.register("paralysing_effect", () -> {
         return new SimpleParticleType(false);
      });
      POISON_BUBBLE = registry.register("poison_bubble", () -> {
         return new SimpleParticleType(false);
      });
      POISON_EFFECT = registry.register("poison_effect", () -> {
         return new SimpleParticleType(false);
      });
      STEAM_EFFECT = registry.register("steam_effect", () -> {
         return new SimpleParticleType(false);
      });
      WATER_BUBBLE = registry.register("water_bubble", () -> {
         return new SimpleParticleType(false);
      });
      WATER_EFFECT = registry.register("water_effect", () -> {
         return new SimpleParticleType(false);
      });
      BLACK_FIRE = registry.register("black_fire", () -> {
         return new SimpleParticleType(false);
      });
      RED_FIRE = registry.register("red_fire", () -> {
         return new SimpleParticleType(false);
      });
      PLASMA_FIRE = registry.register("plasma_fire", () -> {
         return new SimpleParticleType(false);
      });
   }
}
