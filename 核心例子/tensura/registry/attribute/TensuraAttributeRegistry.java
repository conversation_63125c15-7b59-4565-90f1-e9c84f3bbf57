package com.github.manasmods.tensura.registry.attribute;

import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.RangedAttribute;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraAttributeRegistry {
   private static final DeferredRegister<Attribute> registry;
   public static RegistryObject<Attribute> BARRIER;
   public static RegistryObject<Attribute> MAX_MAGICULE;
   public static RegistryObject<Attribute> MAX_AURA;
   public static RegistryObject<Attribute> SIZE;
   public static RegistryObject<Attribute> MAX_SPIRITUAL_HEALTH;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.ATTRIBUTES, "tensura");
      BARRIER = registry.register("barrier", () -> {
         return (new RangedAttribute("tensura.attribute.barrier.name", 0.0D, 0.0D, 1.0E7D)).m_22084_(true);
      });
      MAX_MAGICULE = registry.register("max_magicule", () -> {
         return (new RangedAttribute("tensura.attribute.max_magicule.name", 0.0D, 0.0D, 1.0E13D)).m_22084_(true);
      });
      MAX_AURA = registry.register("max_aura", () -> {
         return (new RangedAttribute("tensura.attribute.max_aura.name", 0.0D, 0.0D, 1.0E13D)).m_22084_(true);
      });
      SIZE = registry.register("size", () -> {
         return (new RangedAttribute("tensura.attribute.size.name", 1.0D, 0.0D, 1000.0D)).m_22084_(true);
      });
      MAX_SPIRITUAL_HEALTH = registry.register("max_spiritual_health", () -> {
         return (new RangedAttribute("tensura.attribute.spiritual_health.name", 60.0D, 1.0D, 1000000.0D)).m_22084_(true);
      });
   }
}
