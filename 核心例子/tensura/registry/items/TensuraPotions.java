package com.github.manasmods.tensura.registry.items;

import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.item.alchemy.Potion;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraPotions {
   public static final DeferredRegister<Potion> registry;
   public static final RegistryObject<Potion> CHILL;
   public static final RegistryObject<Potion> LONG_CHILL;
   public static final RegistryObject<Potion> STRONG_CHILL;
   public static final RegistryObject<Potion> CORROSION;
   public static final RegistryObject<Potion> LONG_CORROSION;
   public static final RegistryObject<Potion> STRONG_CORROSION;
   public static final RegistryObject<Potion> FATAL_POISON;
   public static final RegistryObject<Potion> LONG_FATAL_POISON;
   public static final RegistryObject<Potion> STRONG_FATAL_POISON;
   public static final RegistryObject<Potion> FRAGILITY;
   public static final RegistryObject<Potion> LONG_FRAGILITY;
   public static final RegistryObject<Potion> STRONG_FRAGILITY;
   public static final RegistryObject<Potion> GLOWING;
   public static final RegistryObject<Potion> LONG_GLOWING;
   public static final RegistryObject<Potion> NIGHT_OWL;
   public static final RegistryObject<Potion> LONG_NIGHT_OWL;
   public static final RegistryObject<Potion> PARALYSIS;
   public static final RegistryObject<Potion> LONG_PARALYSIS;
   public static final RegistryObject<Potion> STRONG_PARALYSIS;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.POTIONS, "tensura");
      CHILL = registry.register("chill", () -> {
         return new Potion("chill", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.CHILL.get(), 900)});
      });
      LONG_CHILL = registry.register("long_chill", () -> {
         return new Potion("chill", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.CHILL.get(), 1800)});
      });
      STRONG_CHILL = registry.register("strong_chill", () -> {
         return new Potion("chill", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.CHILL.get(), 432, 1)});
      });
      CORROSION = registry.register("corrosion", () -> {
         return new Potion("corrosion", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.CORROSION.get(), 900)});
      });
      LONG_CORROSION = registry.register("long_corrosion", () -> {
         return new Potion("corrosion", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.CORROSION.get(), 1800)});
      });
      STRONG_CORROSION = registry.register("strong_corrosion", () -> {
         return new Potion("corrosion", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.CORROSION.get(), 432, 1)});
      });
      FATAL_POISON = registry.register("fatal_poison", () -> {
         return new Potion("fatal_poison", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.FATAL_POISON.get(), 900)});
      });
      LONG_FATAL_POISON = registry.register("long_fatal_poison", () -> {
         return new Potion("fatal_poison", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.FATAL_POISON.get(), 1800)});
      });
      STRONG_FATAL_POISON = registry.register("strong_fatal_poison", () -> {
         return new Potion("fatal_poison", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.FATAL_POISON.get(), 432, 1)});
      });
      FRAGILITY = registry.register("fragility", () -> {
         return new Potion("fragility", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 900)});
      });
      LONG_FRAGILITY = registry.register("long_fragility", () -> {
         return new Potion("fragility", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 1800)});
      });
      STRONG_FRAGILITY = registry.register("strong_fragility", () -> {
         return new Potion("fragility", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 432, 1)});
      });
      GLOWING = registry.register("glowing", () -> {
         return new Potion(new MobEffectInstance[]{new MobEffectInstance(MobEffects.f_19619_, 3600)});
      });
      LONG_GLOWING = registry.register("long_glowing", () -> {
         return new Potion("glowing", new MobEffectInstance[]{new MobEffectInstance(MobEffects.f_19619_, 9600)});
      });
      NIGHT_OWL = registry.register("night_owl", () -> {
         return new Potion(new MobEffectInstance[]{new MobEffectInstance(MobEffects.f_19609_, 4800), new MobEffectInstance(MobEffects.f_19611_, 4800)});
      });
      LONG_NIGHT_OWL = registry.register("long_night_owl", () -> {
         return new Potion("night_owl", new MobEffectInstance[]{new MobEffectInstance(MobEffects.f_19609_, 12000), new MobEffectInstance(MobEffects.f_19611_, 12000)});
      });
      PARALYSIS = registry.register("paralysis", () -> {
         return new Potion("paralysis", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.PARALYSIS.get(), 900, 1)});
      });
      LONG_PARALYSIS = registry.register("long_paralysis", () -> {
         return new Potion("paralysis", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.PARALYSIS.get(), 1800, 1)});
      });
      STRONG_PARALYSIS = registry.register("strong_paralysis", () -> {
         return new Potion("paralysis", new MobEffectInstance[]{new MobEffectInstance((MobEffect)TensuraMobEffects.PARALYSIS.get(), 432, 2)});
      });
   }
}
