package com.github.manasmods.tensura.registry.items;

import net.minecraft.world.entity.decoration.PaintingVariant;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraPaintingVariants {
   private static final DeferredRegister<PaintingVariant> registry;
   public static final RegistryObject<PaintingVariant> SUNFLOWER;
   public static final RegistryObject<PaintingVariant> FULBROSIA;
   public static final RegistryObject<PaintingVariant> WORLD_MAP;
   public static final RegistryObject<PaintingVariant> INGRASSIA;
   public static final RegistryObject<PaintingVariant> BLUMUND;
   public static final RegistryObject<PaintingVariant> FILTWOOD;
   public static final RegistryObject<PaintingVariant> RAJA;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.PAINTING_VARIANTS, "tensura");
      SUNFLOWER = registry.register("sunflower", () -> {
         return new PaintingVariant(80, 48);
      });
      FULBROSIA = registry.register("fulbrosia", () -> {
         return new PaintingVariant(48, 32);
      });
      WORLD_MAP = registry.register("world_map", () -> {
         return new PaintingVariant(96, 64);
      });
      INGRASSIA = registry.register("ingrassia", () -> {
         return new PaintingVariant(48, 32);
      });
      BLUMUND = registry.register("blumund", () -> {
         return new PaintingVariant(64, 32);
      });
      FILTWOOD = registry.register("filtwood", () -> {
         return new PaintingVariant(64, 32);
      });
      RAJA = registry.register("raja", () -> {
         return new PaintingVariant(32, 48);
      });
   }
}
