package com.github.manasmods.tensura.registry.battlewill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.battlewill.projectile.DarkEightPalmsArt;
import com.github.manasmods.tensura.ability.battlewill.projectile.DeathMarchDanceArt;
import com.github.manasmods.tensura.ability.battlewill.projectile.ElephantStampedeArt;
import com.github.manasmods.tensura.ability.battlewill.projectile.MagicBulletArt;
import com.github.manasmods.tensura.ability.battlewill.projectile.MaximumMagicBulletArt;
import com.github.manasmods.tensura.ability.battlewill.projectile.OgreFlameArt;
import com.github.manasmods.tensura.ability.battlewill.projectile.OgreSwordCannonArt;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class ProjectileArts {
   private static final DeferredRegister<ManasSkill> registry = DeferredRegister.create(SkillAPI.getSkillRegistryKey(), "tensura");
   public static final RegistryObject<DarkEightPalmsArt> DARK_EIGHT_PALMS;
   public static final RegistryObject<DeathMarchDanceArt> DEATH_MARCH_DANCE;
   public static final RegistryObject<ElephantStampedeArt> ELEPHANT_STAMPEDE;
   public static final RegistryObject<MagicBulletArt> MAGIC_BULLET;
   public static final RegistryObject<MaximumMagicBulletArt> MAXIMUM_MAGIC_BULLET;
   public static final RegistryObject<OgreFlameArt> OGRE_FLAME;
   public static final RegistryObject<OgreSwordCannonArt> OGRE_SWORD_CANNON;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      DARK_EIGHT_PALMS = registry.register("dark_eight_palms", DarkEightPalmsArt::new);
      DEATH_MARCH_DANCE = registry.register("death_march_dance", DeathMarchDanceArt::new);
      ELEPHANT_STAMPEDE = registry.register("elephant_stampede", ElephantStampedeArt::new);
      MAGIC_BULLET = registry.register("magic_bullet", MagicBulletArt::new);
      MAXIMUM_MAGIC_BULLET = registry.register("maximum_magic_bullet", MaximumMagicBulletArt::new);
      OGRE_FLAME = registry.register("ogre_flame", OgreFlameArt::new);
      OGRE_SWORD_CANNON = registry.register("ogre_sword_cannon", OgreSwordCannonArt::new);
   }
}
