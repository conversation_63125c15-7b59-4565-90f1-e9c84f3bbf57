package com.github.manasmods.tensura.registry.effects;

import com.github.manasmods.tensura.effect.BurdenEffect;
import com.github.manasmods.tensura.effect.ChillEffect;
import com.github.manasmods.tensura.effect.CorrosionEffect;
import com.github.manasmods.tensura.effect.CurseEffect;
import com.github.manasmods.tensura.effect.FatalPoisonEffect;
import com.github.manasmods.tensura.effect.FearEffect;
import com.github.manasmods.tensura.effect.FragilityEffect;
import com.github.manasmods.tensura.effect.FrostEffect;
import com.github.manasmods.tensura.effect.HolyDamageEffect;
import com.github.manasmods.tensura.effect.InfectionEffect;
import com.github.manasmods.tensura.effect.InsanityEffect;
import com.github.manasmods.tensura.effect.MagiculePoisonEffect;
import com.github.manasmods.tensura.effect.ParalysisEffect;
import com.github.manasmods.tensura.effect.PetrificationEffect;
import com.github.manasmods.tensura.effect.RampageEffect;
import com.github.manasmods.tensura.effect.WebbedEffect;
import com.github.manasmods.tensura.effect.battlewill.AuraSwordEffect;
import com.github.manasmods.tensura.effect.battlewill.DiamondPathEffect;
import com.github.manasmods.tensura.effect.battlewill.OgreGuillotineEffect;
import com.github.manasmods.tensura.effect.magic.DisintegratingEffect;
import com.github.manasmods.tensura.effect.magic.ShadowBindEffect;
import com.github.manasmods.tensura.effect.skill.AllSeeingEffect;
import com.github.manasmods.tensura.effect.skill.AllyBoostEffect;
import com.github.manasmods.tensura.effect.skill.BatsModeEffect;
import com.github.manasmods.tensura.effect.skill.BeastTransformationEffect;
import com.github.manasmods.tensura.effect.skill.DragonModeEffect;
import com.github.manasmods.tensura.effect.skill.EngorgementEffect;
import com.github.manasmods.tensura.effect.skill.FalsifierEffect;
import com.github.manasmods.tensura.effect.skill.FateChangeEffect;
import com.github.manasmods.tensura.effect.skill.InspirationEffect;
import com.github.manasmods.tensura.effect.skill.InstantRegenerationEffect;
import com.github.manasmods.tensura.effect.skill.LustDrainEffect;
import com.github.manasmods.tensura.effect.skill.MadOgreEffect;
import com.github.manasmods.tensura.effect.skill.MagicAuraEffect;
import com.github.manasmods.tensura.effect.skill.MagicDarkEffect;
import com.github.manasmods.tensura.effect.skill.MagicEarthEffect;
import com.github.manasmods.tensura.effect.skill.MagicFlameEffect;
import com.github.manasmods.tensura.effect.skill.MagicLightEffect;
import com.github.manasmods.tensura.effect.skill.MagicSpaceEffect;
import com.github.manasmods.tensura.effect.skill.MagicWaterEffect;
import com.github.manasmods.tensura.effect.skill.MagicWindEffect;
import com.github.manasmods.tensura.effect.skill.OgreBerserkerEffect;
import com.github.manasmods.tensura.effect.skill.PresenceConcealmentEffect;
import com.github.manasmods.tensura.effect.skill.PresenceSenseEffect;
import com.github.manasmods.tensura.effect.skill.ProtectionEffect;
import com.github.manasmods.tensura.effect.skill.ReaperReconEffect;
import com.github.manasmods.tensura.effect.skill.RestEffect;
import com.github.manasmods.tensura.effect.skill.SelfRegenerationEffect;
import com.github.manasmods.tensura.effect.skill.SeveranceBladeEffect;
import com.github.manasmods.tensura.effect.skill.ShadowStepEffect;
import com.github.manasmods.tensura.effect.skill.SpearheadEffect;
import com.github.manasmods.tensura.effect.skill.StrengthenEffect;
import com.github.manasmods.tensura.effect.skill.WarpingEffect;
import com.github.manasmods.tensura.effect.skill.debuff.BlackBurnEffect;
import com.github.manasmods.tensura.effect.skill.debuff.DrowsinessEffect;
import com.github.manasmods.tensura.effect.skill.debuff.InfiniteImprisonmentEffect;
import com.github.manasmods.tensura.effect.skill.debuff.LustEmbracementEffect;
import com.github.manasmods.tensura.effect.skill.debuff.MagicInterferenceEffect;
import com.github.manasmods.tensura.effect.skill.debuff.MindControlEffect;
import com.github.manasmods.tensura.effect.skill.debuff.MovementInterferenceEffect;
import com.github.manasmods.tensura.effect.skill.debuff.OppressionEffect;
import com.github.manasmods.tensura.effect.skill.debuff.SleepModeEffect;
import com.github.manasmods.tensura.effect.skill.debuff.SoulDrainEffect;
import com.github.manasmods.tensura.effect.skill.debuff.SpatialBlockadeEffect;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import com.github.manasmods.tensura.effect.template.TensuraMobEffect;
import java.awt.Color;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraMobEffects {
   private static final DeferredRegister<MobEffect> registry;
   public static final RegistryObject<MobEffect> BURDEN;
   public static final RegistryObject<MobEffect> CHILL;
   public static final RegistryObject<MobEffect> CORROSION;
   public static final RegistryObject<MobEffect> CURSE;
   public static final RegistryObject<MobEffect> FATAL_POISON;
   public static final RegistryObject<MobEffect> FEAR;
   public static final RegistryObject<MobEffect> FRAGILITY;
   public static final RegistryObject<MobEffect> FROST;
   public static final RegistryObject<MobEffect> HOLY_DAMAGE;
   public static final RegistryObject<MobEffect> INFECTION;
   public static final RegistryObject<MobEffect> INSANITY;
   public static final RegistryObject<MobEffect> MAGICULE_POISON;
   public static final RegistryObject<MobEffect> MAGICULE_REGENERATION;
   public static final RegistryObject<MobEffect> PARALYSIS;
   public static final RegistryObject<MobEffect> PETRIFICATION;
   public static final RegistryObject<MobEffect> SILENCE;
   public static final RegistryObject<MobEffect> WEBBED;
   public static final RegistryObject<MobEffect> TRUE_BLINDNESS;
   public static final RegistryObject<MobEffect> SLEEP_MODE;
   public static final RegistryObject<MobEffect> BATS_MODE;
   public static final RegistryObject<MobEffect> ALL_SEEING;
   public static final RegistryObject<MobEffect> AUDITORY_SENSE;
   public static final RegistryObject<MobEffect> BEAST_TRANSFORMATION;
   public static final RegistryObject<MobEffect> BLACK_BURN;
   public static final RegistryObject<MobEffect> DRAGON_MODE;
   public static final RegistryObject<MobEffect> DROWSINESS;
   public static final RegistryObject<MobEffect> ENGORGEMENT;
   public static final RegistryObject<MobEffect> FALSIFIER;
   public static final RegistryObject<MobEffect> FATE_CHANGE;
   public static final RegistryObject<MobEffect> FUTURE_VISION;
   public static final RegistryObject<MobEffect> HAKI_COAT;
   public static final RegistryObject<MobEffect> HEAT_SENSE;
   public static final RegistryObject<MobEffect> ALLY_BOOST;
   public static final RegistryObject<MobEffect> INSPIRATION;
   public static final RegistryObject<MobEffect> INSTANT_REGENERATION;
   public static final RegistryObject<MobEffect> LUST_DRAIN;
   public static final RegistryObject<MobEffect> MAD_OGRE;
   public static final RegistryObject<MobEffect> MAGIC_AURA;
   public static final RegistryObject<MobEffect> MAGIC_DARKNESS;
   public static final RegistryObject<MobEffect> MAGIC_EARTH;
   public static final RegistryObject<MobEffect> MAGIC_FLAME;
   public static final RegistryObject<MobEffect> MAGIC_LIGHT;
   public static final RegistryObject<MobEffect> MAGIC_SPACE;
   public static final RegistryObject<MobEffect> MAGIC_WATER;
   public static final RegistryObject<MobEffect> MAGIC_WIND;
   public static final RegistryObject<MobEffect> DANGER_DETECTION;
   public static final RegistryObject<MobEffect> OGRE_BERSERKER;
   public static final RegistryObject<MobEffect> PRESENCE_CONCEALMENT;
   public static final RegistryObject<MobEffect> PRESENCE_SENSE;
   public static final RegistryObject<MobEffect> PROTECTION;
   public static final RegistryObject<MobEffect> REAPER_RECON;
   public static final RegistryObject<MobEffect> REST;
   public static final RegistryObject<MobEffect> SELF_REGENERATION;
   public static final RegistryObject<MobEffect> SHADOW_STEP;
   public static final RegistryObject<MobEffect> SEVERANCE_BLADE;
   public static final RegistryObject<MobEffect> SPEARHEAD;
   public static final RegistryObject<MobEffect> STRENGTHEN;
   public static final RegistryObject<MobEffect> WARPING;
   public static final RegistryObject<MobEffect> ANTI_SKILL;
   public static final RegistryObject<MobEffect> DISINTEGRATING;
   public static final RegistryObject<MobEffect> ENERGY_BLOCKADE;
   public static final RegistryObject<MobEffect> INFINITE_IMPRISONMENT;
   public static final RegistryObject<MobEffect> LUST_EMBRACEMENT;
   public static final RegistryObject<MobEffect> MAGIC_INTERFERENCE;
   public static final RegistryObject<MobEffect> MIND_CONTROL;
   public static final RegistryObject<MobEffect> MOVEMENT_INTERFERENCE;
   public static final RegistryObject<MobEffect> OPPRESSION;
   public static final RegistryObject<MobEffect> RAMPAGE;
   public static final RegistryObject<MobEffect> SOUL_DRAIN;
   public static final RegistryObject<MobEffect> SPATIAL_BLOCKADE;
   public static final RegistryObject<MobEffect> AURA_SWORD;
   public static final RegistryObject<MobEffect> DIAMOND_PATH;
   public static final RegistryObject<MobEffect> OGRE_GUILLOTINE;
   public static final RegistryObject<MobEffect> SHADOW_BIND;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.MOB_EFFECTS, "tensura");
      BURDEN = registry.register("burden", () -> {
         return new BurdenEffect(MobEffectCategory.HARMFUL, (new Color(63, 65, 65)).getRGB());
      });
      CHILL = registry.register("chill", () -> {
         return new ChillEffect(MobEffectCategory.HARMFUL, (new Color(41, 166, 182)).getRGB());
      });
      CORROSION = registry.register("corrosion", () -> {
         return new CorrosionEffect(MobEffectCategory.HARMFUL, (new Color(5, 72, 11)).getRGB());
      });
      CURSE = registry.register("curse", () -> {
         return new CurseEffect(MobEffectCategory.HARMFUL, (new Color(117, 1, 1)).getRGB());
      });
      FATAL_POISON = registry.register("fatal_poison", () -> {
         return new FatalPoisonEffect(MobEffectCategory.HARMFUL, (new Color(82, 0, 143)).getRGB());
      });
      FEAR = registry.register("fear", () -> {
         return new FearEffect(MobEffectCategory.HARMFUL, (new Color(58, 75, 82)).getRGB());
      });
      FRAGILITY = registry.register("fragility", () -> {
         return new FragilityEffect(MobEffectCategory.HARMFUL, (new Color(98, 98, 100)).getRGB());
      });
      FROST = registry.register("frost", () -> {
         return new FrostEffect(MobEffectCategory.HARMFUL, (new Color(50, 107, 248)).getRGB());
      });
      HOLY_DAMAGE = registry.register("holy_damage", () -> {
         return new HolyDamageEffect(MobEffectCategory.NEUTRAL, (new Color(255, 166, 4)).getRGB());
      });
      INFECTION = registry.register("infection", () -> {
         return new InfectionEffect(MobEffectCategory.HARMFUL, (new Color(87, 3, 3)).getRGB());
      });
      INSANITY = registry.register("insanity", () -> {
         return new InsanityEffect(MobEffectCategory.HARMFUL, (new Color(255, 0, 0)).getRGB());
      });
      MAGICULE_POISON = registry.register("magicule_poison", () -> {
         return new MagiculePoisonEffect(MobEffectCategory.NEUTRAL, (new Color(239, 5, 77)).getRGB());
      });
      MAGICULE_REGENERATION = registry.register("magicule_regeneration", () -> {
         return new TensuraMobEffect(MobEffectCategory.BENEFICIAL, (new Color(63, 196, 55)).getRGB());
      });
      PARALYSIS = registry.register("paralysis", () -> {
         return new ParalysisEffect(MobEffectCategory.HARMFUL, (new Color(224, 208, 9)).getRGB());
      });
      PETRIFICATION = registry.register("petrification", () -> {
         return new PetrificationEffect(MobEffectCategory.HARMFUL, (new Color(94, 96, 94)).getRGB());
      });
      SILENCE = registry.register("silence", () -> {
         return new TensuraMobEffect(MobEffectCategory.HARMFUL, (new Color(51, 92, 155)).getRGB());
      });
      WEBBED = registry.register("webbed", () -> {
         return new WebbedEffect(MobEffectCategory.HARMFUL, (new Color(187, 180, 180)).getRGB());
      });
      TRUE_BLINDNESS = registry.register("true_blindness", () -> {
         return new SkillMobEffect(MobEffectCategory.HARMFUL, (new Color(0, 0, 0)).getRGB());
      });
      SLEEP_MODE = registry.register("sleep_mode", () -> {
         return new SleepModeEffect(MobEffectCategory.NEUTRAL, (new Color(0, 0, 0)).getRGB());
      });
      BATS_MODE = registry.register("bats_mode", () -> {
         return new BatsModeEffect(MobEffectCategory.BENEFICIAL, (new Color(0, 0, 0)).getRGB());
      });
      ALL_SEEING = registry.register("all_seeing", () -> {
         return new AllSeeingEffect(MobEffectCategory.BENEFICIAL, (new Color(19, 194, 211)).getRGB());
      });
      AUDITORY_SENSE = registry.register("auditory_sense", () -> {
         return new SkillMobEffect(MobEffectCategory.BENEFICIAL, (new Color(168, 187, 90)).getRGB());
      });
      BEAST_TRANSFORMATION = registry.register("beast_transformation", () -> {
         return new BeastTransformationEffect(MobEffectCategory.BENEFICIAL, (new Color(0, 210, 255, 255)).getRGB());
      });
      BLACK_BURN = registry.register("black_burn", () -> {
         return new BlackBurnEffect(MobEffectCategory.HARMFUL, (new Color(33, 32, 32)).getRGB());
      });
      DRAGON_MODE = registry.register("dragon_mode", () -> {
         return new DragonModeEffect(MobEffectCategory.BENEFICIAL, (new Color(255, 181, 0, 255)).getRGB());
      });
      DROWSINESS = registry.register("drowsiness", () -> {
         return new DrowsinessEffect(MobEffectCategory.HARMFUL, (new Color(77, 43, 176, 255)).getRGB());
      });
      ENGORGEMENT = registry.register("engorgement", () -> {
         return new EngorgementEffect(MobEffectCategory.BENEFICIAL, (new Color(246, 168, 0, 255)).getRGB());
      });
      FALSIFIER = registry.register("falsifier", () -> {
         return new FalsifierEffect(MobEffectCategory.BENEFICIAL, (new Color(117, 25, 255, 255)).getRGB());
      });
      FATE_CHANGE = registry.register("fate_change", () -> {
         return new FateChangeEffect(MobEffectCategory.BENEFICIAL, (new Color(44, 148, 99, 255)).getRGB());
      });
      FUTURE_VISION = registry.register("future_vision", () -> {
         return new SkillMobEffect(MobEffectCategory.BENEFICIAL, (new Color(20, 224, 126, 255)).getRGB());
      });
      HAKI_COAT = registry.register("haki_coat", () -> {
         return new SkillMobEffect(MobEffectCategory.BENEFICIAL, (new Color(58, 1, 1)).getRGB());
      });
      HEAT_SENSE = registry.register("heat_sense", () -> {
         return new SkillMobEffect(MobEffectCategory.BENEFICIAL, (new Color(236, 74, 74)).getRGB());
      });
      ALLY_BOOST = registry.register("ally_boost", () -> {
         return new AllyBoostEffect(MobEffectCategory.BENEFICIAL, (new Color(255, 183, 0)).getRGB());
      });
      INSPIRATION = registry.register("inspiration", () -> {
         return new InspirationEffect(MobEffectCategory.BENEFICIAL, (new Color(236, 155, 74)).getRGB());
      });
      INSTANT_REGENERATION = registry.register("instant_regeneration", () -> {
         return new InstantRegenerationEffect(MobEffectCategory.BENEFICIAL, (new Color(222, 56, 56)).getRGB());
      });
      LUST_DRAIN = registry.register("lust_drain", () -> {
         return new LustDrainEffect(MobEffectCategory.BENEFICIAL, (new Color(45, 6, 63)).getRGB());
      });
      MAD_OGRE = registry.register("mad_ogre", () -> {
         return new MadOgreEffect(MobEffectCategory.BENEFICIAL, (new Color(227, 15, 15)).getRGB());
      });
      MAGIC_AURA = registry.register("magic_aura", () -> {
         return new MagicAuraEffect(MobEffectCategory.BENEFICIAL, (new Color(229, 180, 9)).getRGB());
      });
      MAGIC_DARKNESS = registry.register("magic_darkness", () -> {
         return new MagicDarkEffect(MobEffectCategory.BENEFICIAL, (new Color(190, 68, 246)).getRGB());
      });
      MAGIC_EARTH = registry.register("magic_earth", () -> {
         return new MagicEarthEffect(MobEffectCategory.BENEFICIAL, (new Color(190, 68, 246)).getRGB());
      });
      MAGIC_FLAME = registry.register("magic_flame", () -> {
         return new MagicFlameEffect(MobEffectCategory.BENEFICIAL, (new Color(246, 68, 68)).getRGB());
      });
      MAGIC_LIGHT = registry.register("magic_light", () -> {
         return new MagicLightEffect(MobEffectCategory.BENEFICIAL, (new Color(255, 192, 34)).getRGB());
      });
      MAGIC_SPACE = registry.register("magic_space", () -> {
         return new MagicSpaceEffect(MobEffectCategory.BENEFICIAL, (new Color(190, 68, 246)).getRGB());
      });
      MAGIC_WATER = registry.register("magic_water", () -> {
         return new MagicWaterEffect(MobEffectCategory.BENEFICIAL, (new Color(68, 118, 246)).getRGB());
      });
      MAGIC_WIND = registry.register("magic_wind", () -> {
         return new MagicWindEffect(MobEffectCategory.BENEFICIAL, (new Color(21, 243, 159)).getRGB());
      });
      DANGER_DETECTION = registry.register("danger_detection", () -> {
         return new SkillMobEffect(MobEffectCategory.BENEFICIAL, (new Color(63, 45, 49)).getRGB());
      });
      OGRE_BERSERKER = registry.register("ogre_berserker", () -> {
         return new OgreBerserkerEffect(MobEffectCategory.NEUTRAL, (new Color(124, 81, 175)).getRGB());
      });
      PRESENCE_CONCEALMENT = registry.register("presence_concealment", () -> {
         return new PresenceConcealmentEffect(MobEffectCategory.BENEFICIAL, (new Color(0, 0, 206)).getRGB());
      });
      PRESENCE_SENSE = registry.register("presence_sense", () -> {
         return new PresenceSenseEffect(MobEffectCategory.BENEFICIAL, (new Color(88, 176, 204)).getRGB());
      });
      PROTECTION = registry.register("protection", () -> {
         return new ProtectionEffect(MobEffectCategory.BENEFICIAL, (new Color(105, 103, 103)).getRGB());
      });
      REAPER_RECON = registry.register("reaper_recon", () -> {
         return new ReaperReconEffect(MobEffectCategory.BENEFICIAL, (new Color(86, 47, 236)).getRGB());
      });
      REST = registry.register("rest", () -> {
         return new RestEffect(MobEffectCategory.BENEFICIAL, (new Color(29, 25, 89)).getRGB());
      });
      SELF_REGENERATION = registry.register("self_regeneration", () -> {
         return new SelfRegenerationEffect(MobEffectCategory.BENEFICIAL, (new Color(248, 113, 113)).getRGB());
      });
      SHADOW_STEP = registry.register("shadow_step", () -> {
         return new ShadowStepEffect(MobEffectCategory.BENEFICIAL, (new Color(0, 0, 0)).getRGB());
      });
      SEVERANCE_BLADE = registry.register("severance_blade", () -> {
         return new SeveranceBladeEffect(MobEffectCategory.BENEFICIAL, (new Color(75, 222, 148)).getRGB());
      });
      SPEARHEAD = registry.register("spearhead", () -> {
         return new SpearheadEffect(MobEffectCategory.BENEFICIAL, (new Color(25, 93, 140)).getRGB());
      });
      STRENGTHEN = registry.register("strengthen", () -> {
         return new StrengthenEffect(MobEffectCategory.BENEFICIAL, (new Color(185, 162, 123)).getRGB());
      });
      WARPING = registry.register("warping", () -> {
         return new WarpingEffect(MobEffectCategory.NEUTRAL, (new Color(155, 32, 196)).getRGB());
      });
      ANTI_SKILL = registry.register("anti_skill", () -> {
         return new SkillMobEffect(MobEffectCategory.HARMFUL, (new Color(255, 0, 0)).getRGB());
      });
      DISINTEGRATING = registry.register("disintegrating", () -> {
         return new DisintegratingEffect(MobEffectCategory.HARMFUL, (new Color(246, 196, 16)).getRGB());
      });
      ENERGY_BLOCKADE = registry.register("energy_blockade", () -> {
         return new SkillMobEffect(MobEffectCategory.HARMFUL, (new Color(204, 7, 83)).getRGB());
      });
      INFINITE_IMPRISONMENT = registry.register("infinite_imprisonment", () -> {
         return new InfiniteImprisonmentEffect(MobEffectCategory.NEUTRAL, (new Color(112, 5, 5)).getRGB());
      });
      LUST_EMBRACEMENT = registry.register("lust_embracement", () -> {
         return new LustEmbracementEffect(MobEffectCategory.NEUTRAL, (new Color(255, 249, 0)).getRGB());
      });
      MAGIC_INTERFERENCE = registry.register("magic_interference", () -> {
         return new MagicInterferenceEffect(MobEffectCategory.HARMFUL, (new Color(97, 55, 117)).getRGB());
      });
      MIND_CONTROL = registry.register("mind_control", () -> {
         return new MindControlEffect(MobEffectCategory.HARMFUL, (new Color(222, 13, 104)).getRGB());
      });
      MOVEMENT_INTERFERENCE = registry.register("movement_interference", () -> {
         return new MovementInterferenceEffect(MobEffectCategory.HARMFUL, (new Color(66, 64, 64)).getRGB());
      });
      OPPRESSION = registry.register("oppression", () -> {
         return new OppressionEffect(MobEffectCategory.HARMFUL, (new Color(64, 58, 70)).getRGB());
      });
      RAMPAGE = registry.register("rampage", () -> {
         return new RampageEffect(MobEffectCategory.HARMFUL, (new Color(84, 29, 29)).getRGB());
      });
      SOUL_DRAIN = registry.register("soul_drain", () -> {
         return new SoulDrainEffect(MobEffectCategory.NEUTRAL, (new Color(40, 4, 75)).getRGB());
      });
      SPATIAL_BLOCKADE = registry.register("spatial_blockade", () -> {
         return new SpatialBlockadeEffect(MobEffectCategory.NEUTRAL, (new Color(106, 7, 204)).getRGB());
      });
      AURA_SWORD = registry.register("aura_sword", () -> {
         return new AuraSwordEffect(MobEffectCategory.BENEFICIAL, (new Color(246, 229, 13)).getRGB());
      });
      DIAMOND_PATH = registry.register("diamond_path", () -> {
         return new DiamondPathEffect(MobEffectCategory.BENEFICIAL, (new Color(6, 255, 255)).getRGB());
      });
      OGRE_GUILLOTINE = registry.register("ogre_guillotine", () -> {
         return new OgreGuillotineEffect(MobEffectCategory.BENEFICIAL, (new Color(96, 65, 231)).getRGB());
      });
      SHADOW_BIND = registry.register("shadow_bind", () -> {
         return new ShadowBindEffect(MobEffectCategory.NEUTRAL, (new Color(28, 28, 26)).getRGB());
      });
   }
}
