package com.github.manasmods.tensura.registry.fluids;

import com.mojang.blaze3d.shaders.FogShape;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.math.Vector3f;
import java.util.function.Consumer;
import net.minecraft.client.Camera;
import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.client.renderer.FogRenderer.FogMode;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.client.extensions.common.IClientFluidTypeExtensions;
import net.minecraftforge.fluids.FluidType;
import net.minecraftforge.fluids.FluidType.Properties;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class TensuraBaseFluidType extends FluidType {
   private final ResourceLocation sourceTexture;
   private final ResourceLocation flowingTexture;
   private final ResourceLocation overlayTexture;
   private final int color;
   private final Vector3f fog;

   public TensuraBaseFluidType(ResourceLocation sourceTexture, ResourceLocation flowingTexture, ResourceLocation overlayTexture, int color, Vector3f fog, Properties properties) {
      super(properties);
      this.sourceTexture = sourceTexture;
      this.flowingTexture = flowingTexture;
      this.overlayTexture = overlayTexture;
      this.color = color;
      this.fog = fog;
   }

   public ResourceLocation getSourceTexture() {
      return this.sourceTexture;
   }

   public ResourceLocation getFlowingTexture() {
      return this.flowingTexture;
   }

   public ResourceLocation getOverlayTexture() {
      return this.overlayTexture;
   }

   public int getColor() {
      return this.color;
   }

   public Vector3f getFog() {
      return this.fog;
   }

   public void initializeClient(Consumer<IClientFluidTypeExtensions> consumer) {
      consumer.accept(new IClientFluidTypeExtensions() {
         public ResourceLocation getStillTexture() {
            return TensuraBaseFluidType.this.sourceTexture;
         }

         public ResourceLocation getFlowingTexture() {
            return TensuraBaseFluidType.this.flowingTexture;
         }

         @Nullable
         public ResourceLocation getOverlayTexture() {
            return TensuraBaseFluidType.this.overlayTexture;
         }

         public int getTintColor() {
            return TensuraBaseFluidType.this.color;
         }

         @NotNull
         public Vector3f modifyFogColor(Camera camera, float partialTick, ClientLevel level, int renderDistance, float darkenWorldAmount, Vector3f fluidFogColor) {
            return TensuraBaseFluidType.this.fog;
         }

         public void modifyFogRender(Camera camera, FogMode mode, float renderDistance, float partialTick, float nearDistance, float farDistance, FogShape shape) {
            RenderSystem.m_157445_(1.0F);
            RenderSystem.m_157443_(20.0F);
         }
      });
   }
}
