package com.github.manasmods.tensura.registry;

import com.github.manasmods.tensura.loot.TensuraLootModifiers;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.battlewill.MeleeArts;
import com.github.manasmods.tensura.registry.battlewill.ProjectileArts;
import com.github.manasmods.tensura.registry.battlewill.UtilityArts;
import com.github.manasmods.tensura.registry.biome.TensuraBiomes;
import com.github.manasmods.tensura.registry.biome.TensuraConfiguredFeatures;
import com.github.manasmods.tensura.registry.biome.TensuraFeatures;
import com.github.manasmods.tensura.registry.biome.TensuraFoliagePlacers;
import com.github.manasmods.tensura.registry.biome.TensuraPlacedFeatures;
import com.github.manasmods.tensura.registry.biome.TensuraTrunkPlacer;
import com.github.manasmods.tensura.registry.blocks.TensuraBlockEntities;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.command.TensuraArgumentTypes;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.dimensions.TensuraNoises;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.entity.TensuraVillagers;
import com.github.manasmods.tensura.registry.event.TensuraGameEvents;
import com.github.manasmods.tensura.registry.fluids.TensuraFluidTypes;
import com.github.manasmods.tensura.registry.fluids.TensuraFluids;
import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.items.TensuraPaintingVariants;
import com.github.manasmods.tensura.registry.items.TensuraPotions;
import com.github.manasmods.tensura.registry.items.TensuraSmithingSchematicItems;
import com.github.manasmods.tensura.registry.items.TensuraSpawnEggs;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.magic.SpiritualMagics;
import com.github.manasmods.tensura.registry.magicule.TensuraBiomeMagiculeModifier;
import com.github.manasmods.tensura.registry.magicule.TensuraLevelMagiculeModifier;
import com.github.manasmods.tensura.registry.menu.TensuraMenuTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.recipe.TensuraRecipeTypes;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.registry.structure.TensuraProcessors;
import com.github.manasmods.tensura.registry.structure.TensuraStructureTypes;
import net.minecraftforge.eventbus.api.IEventBus;

public class TensuraRegistry {
   public static void register(IEventBus modEventBus) {
      TensuraToolItems.init(modEventBus);
      TensuraArmorItems.init(modEventBus);
      TensuraMobDropItems.init(modEventBus);
      TensuraConsumableItems.init(modEventBus);
      TensuraMaterialItems.init(modEventBus);
      TensuraSpawnEggs.init(modEventBus);
      TensuraPaintingVariants.init(modEventBus);
      TensuraPotions.init(modEventBus);
      TensuraSmithingSchematicItems.init(modEventBus);
      TensuraBlocks.init(modEventBus);
      TensuraBlockEntities.init(modEventBus);
      TensuraFluids.init(modEventBus);
      TensuraFluidTypes.init(modEventBus);
      TensuraFeatures.init(modEventBus);
      TensuraFoliagePlacers.init(modEventBus);
      TensuraTrunkPlacer.init(modEventBus);
      TensuraPlacedFeatures.init(modEventBus);
      TensuraConfiguredFeatures.init(modEventBus);
      TensuraBiomes.init(modEventBus);
      TensuraNoises.init(modEventBus);
      TensuraDimensions.init(modEventBus);
      TensuraMenuTypes.init(modEventBus);
      TensuraRaces.init(modEventBus);
      ResistanceSkills.init(modEventBus);
      IntrinsicSkills.init(modEventBus);
      CommonSkills.init(modEventBus);
      ExtraSkills.init(modEventBus);
      UniqueSkills.init(modEventBus);
      MeleeArts.init(modEventBus);
      ProjectileArts.init(modEventBus);
      UtilityArts.init(modEventBus);
      SpiritualMagics.init(modEventBus);
      TensuraAttributeRegistry.init(modEventBus);
      TensuraRecipeTypes.init(modEventBus);
      TensuraSoundEvents.init(modEventBus);
      TensuraEntityTypes.init(modEventBus);
      TensuraVillagers.init(modEventBus);
      TensuraMobEffects.init(modEventBus);
      TensuraParticles.init(modEventBus);
      TensuraEnchantments.init(modEventBus);
      TensuraArgumentTypes.init(modEventBus);
      TensuraLevelMagiculeModifier.init(modEventBus);
      TensuraBiomeMagiculeModifier.init(modEventBus);
      TensuraLootModifiers.init(modEventBus);
      TensuraProcessors.init(modEventBus);
      TensuraStructureTypes.init(modEventBus);
      TensuraGameEvents.init(modEventBus);
      TensuraStats.init(modEventBus);
   }
}
