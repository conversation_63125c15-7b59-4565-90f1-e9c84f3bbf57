package com.github.manasmods.tensura.registry.dimensions;

import it.unimi.dsi.fastutil.doubles.DoubleList;
import net.minecraft.data.BuiltinRegistries;
import net.minecraft.world.level.levelgen.synth.NormalNoise.NoiseParameters;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class TensuraNoises {
   public static final DeferredRegister<NoiseParameters> registry;
   public static final RegistryObject<NoiseParameters> HELL;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(BuiltinRegistries.f_194654_.m_123023_(), "tensura");
      HELL = registry.register("hell", () -> {
         return new NoiseParameters(-7, DoubleList.of(new double[]{4.0D, 10.0D, 10.0D, 1.0D}));
      });
   }
}
