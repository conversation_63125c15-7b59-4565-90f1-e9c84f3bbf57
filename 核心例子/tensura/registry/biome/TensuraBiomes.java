package com.github.manasmods.tensura.registry.biome;

import com.github.manasmods.tensura.world.biome.SakuraForestBiome;
import com.github.manasmods.tensura.world.biome.UnderworldBarrensBiome;
import com.github.manasmods.tensura.world.biome.UnderworldRedSandsBiome;
import com.github.manasmods.tensura.world.biome.UnderworldSandsBiome;
import com.github.manasmods.tensura.world.biome.UnderworldSpikesBiome;
import net.minecraft.world.level.biome.Biome;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraBiomes {
   private static final DeferredRegister<Biome> registry;
   public static final RegistryObject<Biome> SAKURA_FOREST;
   public static final RegistryObject<Biome> UNDERWORLD_BARRENS;
   public static final RegistryObject<Biome> UNDERWORLD_RED_SANDS;
   public static final RegistryObject<Biome> UNDERWORLD_SANDS;
   public static final RegistryObject<Biome> UNDERWORLD_SPIKES;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.BIOMES, "tensura");
      SAKURA_FOREST = registry.register("sakura_forest", SakuraForestBiome::create);
      UNDERWORLD_BARRENS = registry.register("underworld_barrens", UnderworldBarrensBiome::create);
      UNDERWORLD_RED_SANDS = registry.register("underworld_red_sands", UnderworldRedSandsBiome::create);
      UNDERWORLD_SANDS = registry.register("underworld_sands", UnderworldSandsBiome::create);
      UNDERWORLD_SPIKES = registry.register("underworld_spikes", UnderworldSpikesBiome::create);
   }
}
