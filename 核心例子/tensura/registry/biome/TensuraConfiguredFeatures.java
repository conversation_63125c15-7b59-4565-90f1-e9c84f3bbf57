package com.github.manasmods.tensura.registry.biome;

import com.github.manasmods.tensura.block.HipokuteGrass;
import com.github.manasmods.tensura.block.SimpleBlock;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.world.features.FloatingDebrisFeature;
import com.github.manasmods.tensura.world.features.HellBlockBlobFeature;
import com.github.manasmods.tensura.world.features.RockySpikeFeature;
import com.github.manasmods.tensura.world.tree.leaves.PalmFoliagePlacer;
import com.github.manasmods.tensura.world.tree.trunk.PalmTrunkPlacer;
import com.google.common.base.Suppliers;
import java.util.List;
import java.util.OptionalInt;
import java.util.function.Supplier;
import net.minecraft.core.Holder;
import net.minecraft.core.Registry;
import net.minecraft.data.worldgen.features.OreFeatures;
import net.minecraft.data.worldgen.placement.PlacementUtils;
import net.minecraft.util.valueproviders.ConstantInt;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.RotatedPillarBlock;
import net.minecraft.world.level.levelgen.feature.ConfiguredFeature;
import net.minecraft.world.level.levelgen.feature.Feature;
import net.minecraft.world.level.levelgen.feature.WeightedPlacedFeature;
import net.minecraft.world.level.levelgen.feature.configurations.NoneFeatureConfiguration;
import net.minecraft.world.level.levelgen.feature.configurations.OreConfiguration;
import net.minecraft.world.level.levelgen.feature.configurations.RandomFeatureConfiguration;
import net.minecraft.world.level.levelgen.feature.configurations.RandomPatchConfiguration;
import net.minecraft.world.level.levelgen.feature.configurations.SimpleBlockConfiguration;
import net.minecraft.world.level.levelgen.feature.configurations.TreeConfiguration;
import net.minecraft.world.level.levelgen.feature.configurations.OreConfiguration.TargetBlockState;
import net.minecraft.world.level.levelgen.feature.configurations.TreeConfiguration.TreeConfigurationBuilder;
import net.minecraft.world.level.levelgen.feature.featuresize.TwoLayersFeatureSize;
import net.minecraft.world.level.levelgen.feature.foliageplacers.BlobFoliagePlacer;
import net.minecraft.world.level.levelgen.feature.foliageplacers.FancyFoliagePlacer;
import net.minecraft.world.level.levelgen.feature.stateproviders.BlockStateProvider;
import net.minecraft.world.level.levelgen.feature.stateproviders.NoiseThresholdProvider;
import net.minecraft.world.level.levelgen.feature.treedecorators.BeehiveDecorator;
import net.minecraft.world.level.levelgen.feature.trunkplacers.FancyTrunkPlacer;
import net.minecraft.world.level.levelgen.feature.trunkplacers.StraightTrunkPlacer;
import net.minecraft.world.level.levelgen.synth.NormalNoise.NoiseParameters;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class TensuraConfiguredFeatures {
   private static final Supplier<List<TargetBlockState>> ORE_SILVER_TARGET_LIST = Suppliers.memoize(() -> {
      return List.of(OreConfiguration.m_161021_(OreFeatures.f_195072_, ((SimpleBlock)TensuraBlocks.SILVER_ORE.get()).m_49966_()), OreConfiguration.m_161021_(OreFeatures.f_195073_, ((RotatedPillarBlock)TensuraBlocks.DEEPSLATE_SILVER_ORE.get()).m_49966_()));
   });
   private static final Supplier<List<TargetBlockState>> ORE_MAGIC_TARGET_LIST = Suppliers.memoize(() -> {
      return List.of(OreConfiguration.m_161021_(OreFeatures.f_195072_, ((SimpleBlock)TensuraBlocks.MAGIC_ORE.get()).m_49966_()), OreConfiguration.m_161021_(OreFeatures.f_195073_, ((RotatedPillarBlock)TensuraBlocks.DEEPSLATE_MAGIC_ORE.get()).m_49966_()));
   });
   private static final BeehiveDecorator BEEHIVE = new BeehiveDecorator(0.05F);
   private static final DeferredRegister<ConfiguredFeature<?, ?>> registry;
   public static final RegistryObject<ConfiguredFeature<?, ?>> SAKURA_TREE;
   public static final RegistryObject<ConfiguredFeature<?, ?>> SAKURA_TREE_HIVE;
   public static final RegistryObject<ConfiguredFeature<?, ?>> SAKURA_TREE_LARGE;
   public static final RegistryObject<ConfiguredFeature<?, ?>> SAKURA_TREE_LARGE_HIVE;
   public static final RegistryObject<ConfiguredFeature<?, ?>> SAKURA_FOREST;
   public static final RegistryObject<ConfiguredFeature<?, ?>> PALM_TREE;
   public static final RegistryObject<ConfiguredFeature<?, ?>> ORE_SILVER_SMALL;
   public static final RegistryObject<ConfiguredFeature<?, ?>> ORE_SILVER_LARGE;
   public static final RegistryObject<ConfiguredFeature<?, ?>> ORE_SILVER_BURIED;
   public static final RegistryObject<ConfiguredFeature<?, ?>> ORE_MAGIC;
   public static final RegistryObject<ConfiguredFeature<?, ?>> ORE_MAGIC_BURIED;
   public static final RegistryObject<ConfiguredFeature<?, ?>> HIPOKUTE_GRASS;
   public static final RegistryObject<ConfiguredFeature<?, ?>> ROCKY_SPIKE;
   public static final RegistryObject<ConfiguredFeature<?, ?>> HELL_BLOCK_BLOB;
   public static final RegistryObject<ConfiguredFeature<?, ?>> FLOATING_DEBRIS;

   private static TreeConfiguration largeTree(Supplier<? extends Block> logBlock, Supplier<? extends Block> leavesBlock) {
      return largeTree((Block)logBlock.get(), (Block)leavesBlock.get());
   }

   private static TreeConfiguration largeTree(Block logBlock, Block leavesBlock) {
      return largeTree(logBlock, leavesBlock, 3, 11, 0, 4).m_68251_();
   }

   private static TreeConfiguration largeTreeWithHive(Supplier<? extends Block> logBlock, Supplier<? extends Block> leavesBlock) {
      return largeTreeWithHive((Block)logBlock.get(), (Block)leavesBlock.get());
   }

   private static TreeConfiguration largeTreeWithHive(Block logBlock, Block leavesBlock) {
      return largeTree(logBlock, leavesBlock, 3, 11, 0, 4).m_68249_(List.of(BEEHIVE)).m_68251_();
   }

   private static TreeConfiguration basicTree(Supplier<? extends Block> logBlock, Supplier<? extends Block> leavesBlock) {
      return basicTree((Block)logBlock.get(), (Block)leavesBlock.get());
   }

   private static TreeConfiguration basicTree(Block logBlock, Block leavesBlock) {
      return createStraightBlobTree(logBlock, leavesBlock, 4, 2, 0, 2).m_68244_().m_68251_();
   }

   private static TreeConfiguration basicTreeWithHive(Supplier<? extends Block> logBlock, Supplier<? extends Block> leavesBlock) {
      return basicTreeWithHive((Block)logBlock.get(), (Block)leavesBlock.get());
   }

   private static TreeConfiguration basicTreeWithHive(Block logBlock, Block leavesBlock) {
      return createStraightBlobTree(logBlock, leavesBlock, 4, 2, 0, 2).m_68244_().m_68249_(List.of(BEEHIVE)).m_68251_();
   }

   private static TreeConfigurationBuilder createStraightBlobTree(Block logBlock, Block leavesBlock, int baseHeight, int p_195150_, int p_195151_, int leavesRadius) {
      return new TreeConfigurationBuilder(BlockStateProvider.m_191382_(logBlock), new StraightTrunkPlacer(baseHeight, p_195150_, p_195151_), BlockStateProvider.m_191382_(leavesBlock), new BlobFoliagePlacer(ConstantInt.m_146483_(leavesRadius), ConstantInt.m_146483_(0), 3), new TwoLayersFeatureSize(1, 0, 1));
   }

   private static TreeConfigurationBuilder largeTree(Block logBlock, Block leavesBlock, int baseHeight, int p_195150_, int p_195151_, int leavesRadius) {
      return (new TreeConfigurationBuilder(BlockStateProvider.m_191382_(logBlock), new FancyTrunkPlacer(baseHeight, p_195150_, p_195151_), BlockStateProvider.m_191382_(leavesBlock), new FancyFoliagePlacer(ConstantInt.m_146483_(leavesRadius), ConstantInt.m_146483_(4), 4), new TwoLayersFeatureSize(0, 0, 0, OptionalInt.of(4)))).m_68244_();
   }

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(Registry.f_122881_, "tensura");
      SAKURA_TREE = registry.register("sakura_tree", () -> {
         return new ConfiguredFeature(Feature.f_65760_, basicTree((Supplier)TensuraBlocks.SAKURA_LOG, (Supplier)TensuraBlocks.SAKURA_LEAVES));
      });
      SAKURA_TREE_HIVE = registry.register("sakura_tree_hive", () -> {
         return new ConfiguredFeature(Feature.f_65760_, basicTreeWithHive((Supplier)TensuraBlocks.SAKURA_LOG, (Supplier)TensuraBlocks.SAKURA_LEAVES));
      });
      SAKURA_TREE_LARGE = registry.register("sakura_tree_large", () -> {
         return new ConfiguredFeature(Feature.f_65760_, largeTree((Supplier)TensuraBlocks.SAKURA_LOG, (Supplier)TensuraBlocks.SAKURA_LEAVES));
      });
      SAKURA_TREE_LARGE_HIVE = registry.register("sakura_tree_large_hive", () -> {
         return new ConfiguredFeature(Feature.f_65760_, largeTreeWithHive((Supplier)TensuraBlocks.SAKURA_LOG, (Supplier)TensuraBlocks.SAKURA_LEAVES));
      });
      SAKURA_FOREST = registry.register("sakura_forest_trees", () -> {
         return new ConfiguredFeature(Feature.f_65754_, new RandomFeatureConfiguration(List.of(new WeightedPlacedFeature((Holder)TensuraPlacedFeatures.SAKURA_TREE_LARGE_CHECKED.getHolder().get(), 0.15F)), (Holder)TensuraPlacedFeatures.SAKURA_TREE_CHECKED.getHolder().get()));
      });
      PALM_TREE = registry.register("palm_tree", () -> {
         return new ConfiguredFeature(Feature.f_65760_, (new TreeConfigurationBuilder(BlockStateProvider.m_191382_((Block)TensuraBlocks.PALM_LOG.get()), new PalmTrunkPlacer(11, 2, 0), BlockStateProvider.m_191382_((Block)TensuraBlocks.PALM_LEAVES.get()), new PalmFoliagePlacer(ConstantInt.m_146483_(3), ConstantInt.m_146483_(0), 3), new TwoLayersFeatureSize(0, 0, 0, OptionalInt.of(4)))).m_68251_());
      });
      ORE_SILVER_SMALL = registry.register("ore_silver_small", () -> {
         return new ConfiguredFeature(Feature.f_65731_, new OreConfiguration((List)ORE_SILVER_TARGET_LIST.get(), 9));
      });
      ORE_SILVER_LARGE = registry.register("ore_silver_large", () -> {
         return new ConfiguredFeature(Feature.f_65731_, new OreConfiguration((List)ORE_SILVER_TARGET_LIST.get(), 12, 0.7F));
      });
      ORE_SILVER_BURIED = registry.register("ore_silver_buried", () -> {
         return new ConfiguredFeature(Feature.f_65731_, new OreConfiguration((List)ORE_SILVER_TARGET_LIST.get(), 9, 0.5F));
      });
      ORE_MAGIC = registry.register("ore_magic", () -> {
         return new ConfiguredFeature(Feature.f_65731_, new OreConfiguration((List)ORE_MAGIC_TARGET_LIST.get(), 4));
      });
      ORE_MAGIC_BURIED = registry.register("ore_magic_buried", () -> {
         return new ConfiguredFeature(Feature.f_65731_, new OreConfiguration((List)ORE_MAGIC_TARGET_LIST.get(), 4, 0.5F));
      });
      HIPOKUTE_GRASS = registry.register("hipokute_grass", () -> {
         return new ConfiguredFeature(Feature.f_65761_, new RandomPatchConfiguration(40, 3, 3, PlacementUtils.m_206495_(Feature.f_65741_, new SimpleBlockConfiguration(new NoiseThresholdProvider(2345L, new NoiseParameters(0, 1.0D, new double[0]), 0.005F, -0.8F, 0.33333334F, ((HipokuteGrass)TensuraBlocks.HIPOKUTE_GRASS.get()).getStateForAge(2), List.of(((HipokuteGrass)TensuraBlocks.HIPOKUTE_GRASS.get()).getStateForAge(3)), List.of(((HipokuteGrass)TensuraBlocks.HIPOKUTE_GRASS.get()).getStateForAge(2), ((HipokuteGrass)TensuraBlocks.HIPOKUTE_GRASS.get()).getStateForAge(1)))))));
      });
      ROCKY_SPIKE = registry.register("rocky_spike", () -> {
         return new ConfiguredFeature((RockySpikeFeature)TensuraFeatures.ROCKY_SPIKE.get(), new NoneFeatureConfiguration());
      });
      HELL_BLOCK_BLOB = registry.register("hell_blob_block", () -> {
         return new ConfiguredFeature((HellBlockBlobFeature)TensuraFeatures.HELL_BLOCK_BLOB.get(), new NoneFeatureConfiguration());
      });
      FLOATING_DEBRIS = registry.register("floating_debris", () -> {
         return new ConfiguredFeature((FloatingDebrisFeature)TensuraFeatures.FLOATING_DEBRIS.get(), new NoneFeatureConfiguration());
      });
   }
}
