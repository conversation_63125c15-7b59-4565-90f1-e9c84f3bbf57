package com.github.manasmods.tensura.registry.structure;

import com.github.manasmods.tensura.world.structure.WaterloggingFixProcessor;
import net.minecraft.core.Registry;
import net.minecraft.world.level.levelgen.structure.templatesystem.StructureProcessorType;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class TensuraProcessors {
   public static final DeferredRegister<StructureProcessorType<?>> registry;
   public static final RegistryObject<StructureProcessorType<WaterloggingFixProcessor>> WATERLOGGING_FIX_PROCESSOR;

   public static void init(IEventBus bus) {
      registry.register(bus);
   }

   static {
      registry = DeferredRegister.create(Registry.f_122854_, "tensura");
      WATERLOGGING_FIX_PROCESSOR = registry.register("no_waterlog_processor", () -> {
         return () -> {
            return WaterloggingFixProcessor.CODEC;
         };
      });
   }
}
