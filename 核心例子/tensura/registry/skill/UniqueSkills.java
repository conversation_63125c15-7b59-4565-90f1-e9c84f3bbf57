package com.github.manasmods.tensura.registry.skill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.skill.unique.AbsoluteSeveranceSkill;
import com.github.manasmods.tensura.ability.skill.unique.AntiSkill;
import com.github.manasmods.tensura.ability.skill.unique.BerserkSkill;
import com.github.manasmods.tensura.ability.skill.unique.BerserkerSkill;
import com.github.manasmods.tensura.ability.skill.unique.BewilderSkill;
import com.github.manasmods.tensura.ability.skill.unique.ChefSkill;
import com.github.manasmods.tensura.ability.skill.unique.ChosenOneSkill;
import com.github.manasmods.tensura.ability.skill.unique.CommanderSkill;
import com.github.manasmods.tensura.ability.skill.unique.CookSkill;
import com.github.manasmods.tensura.ability.skill.unique.CreatorSkill;
import com.github.manasmods.tensura.ability.skill.unique.DegenerateSkill;
import com.github.manasmods.tensura.ability.skill.unique.DivineBerserkerSkill;
import com.github.manasmods.tensura.ability.skill.unique.EngorgerSkill;
import com.github.manasmods.tensura.ability.skill.unique.EnvySkill;
import com.github.manasmods.tensura.ability.skill.unique.FalsifierSkill;
import com.github.manasmods.tensura.ability.skill.unique.FighterSkill;
import com.github.manasmods.tensura.ability.skill.unique.FusionistSkill;
import com.github.manasmods.tensura.ability.skill.unique.GluttonySkill;
import com.github.manasmods.tensura.ability.skill.unique.GodlyCraftsmanSkill;
import com.github.manasmods.tensura.ability.skill.unique.GourmandSkill;
import com.github.manasmods.tensura.ability.skill.unique.GourmetSkill;
import com.github.manasmods.tensura.ability.skill.unique.GreatSageSkill;
import com.github.manasmods.tensura.ability.skill.unique.GreedSkill;
import com.github.manasmods.tensura.ability.skill.unique.GuardianSkill;
import com.github.manasmods.tensura.ability.skill.unique.HealerSkill;
import com.github.manasmods.tensura.ability.skill.unique.InfinityPrisonSkill;
import com.github.manasmods.tensura.ability.skill.unique.LustSkill;
import com.github.manasmods.tensura.ability.skill.unique.MartialMasterSkill;
import com.github.manasmods.tensura.ability.skill.unique.MathematicianSkill;
import com.github.manasmods.tensura.ability.skill.unique.MercilessSkill;
import com.github.manasmods.tensura.ability.skill.unique.MurdererSkill;
import com.github.manasmods.tensura.ability.skill.unique.MusicianSkill;
import com.github.manasmods.tensura.ability.skill.unique.ObserverSkill;
import com.github.manasmods.tensura.ability.skill.unique.OppressorSkill;
import com.github.manasmods.tensura.ability.skill.unique.PredatorSkill;
import com.github.manasmods.tensura.ability.skill.unique.PrideSkill;
import com.github.manasmods.tensura.ability.skill.unique.ReaperSkill;
import com.github.manasmods.tensura.ability.skill.unique.ReflectorSkill;
import com.github.manasmods.tensura.ability.skill.unique.ResearcherSkill;
import com.github.manasmods.tensura.ability.skill.unique.ReverserSkill;
import com.github.manasmods.tensura.ability.skill.unique.RoyalBeastSkill;
import com.github.manasmods.tensura.ability.skill.unique.SeerSkill;
import com.github.manasmods.tensura.ability.skill.unique.SevererSkill;
import com.github.manasmods.tensura.ability.skill.unique.ShadowStrikerSkill;
import com.github.manasmods.tensura.ability.skill.unique.SlothSkill;
import com.github.manasmods.tensura.ability.skill.unique.SniperSkill;
import com.github.manasmods.tensura.ability.skill.unique.SpearheadSkill;
import com.github.manasmods.tensura.ability.skill.unique.StarvedSkill;
import com.github.manasmods.tensura.ability.skill.unique.SuppressorSkill;
import com.github.manasmods.tensura.ability.skill.unique.SurvivorSkill;
import com.github.manasmods.tensura.ability.skill.unique.ThrowerSkill;
import com.github.manasmods.tensura.ability.skill.unique.TravelerSkill;
import com.github.manasmods.tensura.ability.skill.unique.TunerSkill;
import com.github.manasmods.tensura.ability.skill.unique.UnyieldingSkill;
import com.github.manasmods.tensura.ability.skill.unique.UsurperSkill;
import com.github.manasmods.tensura.ability.skill.unique.VillainSkill;
import com.github.manasmods.tensura.ability.skill.unique.WrathSkill;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class UniqueSkills {
   private static final DeferredRegister<ManasSkill> registry = DeferredRegister.create(SkillAPI.getSkillRegistryKey(), "tensura");
   public static final RegistryObject<AbsoluteSeveranceSkill> ABSOLUTE_SEVERANCE;
   public static final RegistryObject<AntiSkill> ANTI_SKILL;
   public static final RegistryObject<BerserkSkill> BERSERK;
   public static final RegistryObject<BerserkerSkill> BERSERKER;
   public static final RegistryObject<BewilderSkill> BEWILDER;
   public static final RegistryObject<ChefSkill> CHEF;
   public static final RegistryObject<ChosenOneSkill> CHOSEN_ONE;
   public static final RegistryObject<CommanderSkill> COMMANDER;
   public static final RegistryObject<CookSkill> COOK;
   public static final RegistryObject<CreatorSkill> CREATOR;
   public static final RegistryObject<DegenerateSkill> DEGENERATE;
   public static final RegistryObject<DivineBerserkerSkill> DIVINE_BERSERKER;
   public static final RegistryObject<EngorgerSkill> ENGORGER;
   public static final RegistryObject<EnvySkill> ENVY;
   public static final RegistryObject<FalsifierSkill> FALSIFIER;
   public static final RegistryObject<FighterSkill> FIGHTER;
   public static final RegistryObject<FusionistSkill> FUSIONIST;
   public static final RegistryObject<GluttonySkill> GLUTTONY;
   public static final RegistryObject<GodlyCraftsmanSkill> GODLY_CRAFTSMAN;
   public static final RegistryObject<GourmandSkill> GOURMAND;
   public static final RegistryObject<GourmetSkill> GOURMET;
   public static final RegistryObject<GreatSageSkill> GREAT_SAGE;
   public static final RegistryObject<GreedSkill> GREED;
   public static final RegistryObject<GuardianSkill> GUARDIAN;
   public static final RegistryObject<HealerSkill> HEALER;
   public static final RegistryObject<InfinityPrisonSkill> INFINITY_PRISON;
   public static final RegistryObject<LustSkill> LUST;
   public static final RegistryObject<MartialMasterSkill> MARTIAL_MASTER;
   public static final RegistryObject<MathematicianSkill> MATHEMATICIAN;
   public static final RegistryObject<MercilessSkill> MERCILESS;
   public static final RegistryObject<MurdererSkill> MURDERER;
   public static final RegistryObject<MusicianSkill> MUSICIAN;
   public static final RegistryObject<ObserverSkill> OBSERVER;
   public static final RegistryObject<OppressorSkill> OPPRESSOR;
   public static final RegistryObject<PredatorSkill> PREDATOR;
   public static final RegistryObject<PrideSkill> PRIDE;
   public static final RegistryObject<ReflectorSkill> REFLECTOR;
   public static final RegistryObject<ResearcherSkill> RESEARCHER;
   public static final RegistryObject<RoyalBeastSkill> ROYAL_BEAST;
   public static final RegistryObject<ReaperSkill> REAPER;
   public static final RegistryObject<ReverserSkill> REVERSER;
   public static final RegistryObject<SeerSkill> SEER;
   public static final RegistryObject<SevererSkill> SEVERER;
   public static final RegistryObject<ShadowStrikerSkill> SHADOW_STRIKER;
   public static final RegistryObject<SlothSkill> SLOTH;
   public static final RegistryObject<SniperSkill> SNIPER;
   public static final RegistryObject<SpearheadSkill> SPEARHEAD;
   public static final RegistryObject<StarvedSkill> STARVED;
   public static final RegistryObject<SuppressorSkill> SUPPRESSOR;
   public static final RegistryObject<SurvivorSkill> SURVIVOR;
   public static final RegistryObject<ThrowerSkill> THROWER;
   public static final RegistryObject<TravelerSkill> TRAVELER;
   public static final RegistryObject<TunerSkill> TUNER;
   public static final RegistryObject<UnyieldingSkill> UNYIELDING;
   public static final RegistryObject<UsurperSkill> USURPER;
   public static final RegistryObject<VillainSkill> VILLAIN;
   public static final RegistryObject<WrathSkill> WRATH;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      ABSOLUTE_SEVERANCE = registry.register("absolute_severance", AbsoluteSeveranceSkill::new);
      ANTI_SKILL = registry.register("anti_skill", AntiSkill::new);
      BERSERK = registry.register("berserk", BerserkSkill::new);
      BERSERKER = registry.register("berserker", BerserkerSkill::new);
      BEWILDER = registry.register("bewilder", BewilderSkill::new);
      CHEF = registry.register("chef", ChefSkill::new);
      CHOSEN_ONE = registry.register("chosen_one", ChosenOneSkill::new);
      COMMANDER = registry.register("commander", CommanderSkill::new);
      COOK = registry.register("cook", CookSkill::new);
      CREATOR = registry.register("creator", CreatorSkill::new);
      DEGENERATE = registry.register("degenerate", DegenerateSkill::new);
      DIVINE_BERSERKER = registry.register("divine_berserker", DivineBerserkerSkill::new);
      ENGORGER = registry.register("engorger", EngorgerSkill::new);
      ENVY = registry.register("envy", EnvySkill::new);
      FALSIFIER = registry.register("falsifier", FalsifierSkill::new);
      FIGHTER = registry.register("fighter", FighterSkill::new);
      FUSIONIST = registry.register("fusionist", FusionistSkill::new);
      GLUTTONY = registry.register("gluttony", GluttonySkill::new);
      GODLY_CRAFTSMAN = registry.register("godly_craftsman", GodlyCraftsmanSkill::new);
      GOURMAND = registry.register("gourmand", GourmandSkill::new);
      GOURMET = registry.register("gourmet", GourmetSkill::new);
      GREAT_SAGE = registry.register("great_sage", GreatSageSkill::new);
      GREED = registry.register("greed", GreedSkill::new);
      GUARDIAN = registry.register("guardian", GuardianSkill::new);
      HEALER = registry.register("healer", HealerSkill::new);
      INFINITY_PRISON = registry.register("infinity_prison", InfinityPrisonSkill::new);
      LUST = registry.register("lust", LustSkill::new);
      MARTIAL_MASTER = registry.register("martial_master", MartialMasterSkill::new);
      MATHEMATICIAN = registry.register("mathematician", MathematicianSkill::new);
      MERCILESS = registry.register("merciless", MercilessSkill::new);
      MURDERER = registry.register("murderer", MurdererSkill::new);
      MUSICIAN = registry.register("musician", MusicianSkill::new);
      OBSERVER = registry.register("observer", ObserverSkill::new);
      OPPRESSOR = registry.register("oppressor", OppressorSkill::new);
      PREDATOR = registry.register("predator", PredatorSkill::new);
      PRIDE = registry.register("pride", PrideSkill::new);
      REFLECTOR = registry.register("reflector", ReflectorSkill::new);
      RESEARCHER = registry.register("researcher", ResearcherSkill::new);
      ROYAL_BEAST = registry.register("royal_beast", RoyalBeastSkill::new);
      REAPER = registry.register("reaper", ReaperSkill::new);
      REVERSER = registry.register("reverser", ReverserSkill::new);
      SEER = registry.register("seer", SeerSkill::new);
      SEVERER = registry.register("severer", SevererSkill::new);
      SHADOW_STRIKER = registry.register("shadow_striker", ShadowStrikerSkill::new);
      SLOTH = registry.register("sloth", SlothSkill::new);
      SNIPER = registry.register("sniper", SniperSkill::new);
      SPEARHEAD = registry.register("spearhead", SpearheadSkill::new);
      STARVED = registry.register("starved", StarvedSkill::new);
      SUPPRESSOR = registry.register("suppressor", SuppressorSkill::new);
      SURVIVOR = registry.register("survivor", SurvivorSkill::new);
      THROWER = registry.register("thrower", ThrowerSkill::new);
      TRAVELER = registry.register("traveler", TravelerSkill::new);
      TUNER = registry.register("tuner", TunerSkill::new);
      UNYIELDING = registry.register("unyielding", UnyieldingSkill::new);
      USURPER = registry.register("usurper", UsurperSkill::new);
      VILLAIN = registry.register("villain", VillainSkill::new);
      WRATH = registry.register("wrath", WrathSkill::new);
   }
}
