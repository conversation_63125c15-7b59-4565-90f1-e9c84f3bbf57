package com.github.manasmods.tensura.client;

import com.github.manasmods.manascore.api.client.gui.widget.ImagePredicateButton;
import com.github.manasmods.manascore.api.client.gui.widget.RenderCheck;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.GUISwitchPacket;
import com.github.manasmods.tensura.network.play2server.RequestContainerButtonClickPacket;
import com.google.common.collect.ImmutableList;
import com.mojang.blaze3d.platform.Lighting;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.BufferBuilder;
import com.mojang.blaze3d.vertex.DefaultVertexFormat;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.Tesselator;
import com.mojang.blaze3d.vertex.VertexFormat.Mode;
import com.mojang.math.Quaternion;
import com.mojang.math.Vector3f;
import java.awt.Color;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.Font;
import net.minecraft.client.gui.GuiComponent;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.renderer.MultiBufferSource.BufferSource;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraft.client.resources.sounds.SimpleSoundInstance;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.FormattedText;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.util.FormattedCharSequence;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.state.BlockState;
import org.jetbrains.annotations.Nullable;

public class TensuraGUIHelper {
   public static final ResourceLocation SETTINGS_TAB = new ResourceLocation("tensura", "textures/gui/settings_tab_button.png");
   public static final ResourceLocation COMING_SOON_TAB = new ResourceLocation("tensura", "textures/gui/coming_soon_tab.png");
   public static final ResourceLocation COMING_SOON_ICON = new ResourceLocation("tensura", "textures/gui/coming_soon_tab_icon.png");

   public static void spawnMarkerParticle(Level pLevel, BlockState pState, BlockPos pos, Predicate<Item> predicate) {
      if (pLevel.m_5776_()) {
         Player player = Minecraft.m_91087_().f_91074_;
         if (player != null) {
            if (predicate.test(player.m_21205_().m_41720_()) || predicate.test(player.m_21206_().m_41720_())) {
               pLevel.m_7106_(new BlockParticleOption(ParticleTypes.f_194652_, pState), (double)pos.m_123341_() + 0.5D, (double)pos.m_123342_() + 0.5D, (double)pos.m_123343_() + 0.5D, 0.0D, 0.0D, 0.0D);
            }
         }
      }
   }

   public static void spawnMarkerParticle(Level pLevel, BlockState pState, BlockPos pos, Item item) {
      spawnMarkerParticle(pLevel, pState, pos, (pItem) -> {
         return pItem == item;
      });
   }

   public static ImmutableList<ImagePredicateButton> addMenuTabs(AbstractContainerScreen<?> screen, int currentMenu, boolean renderCheck) {
      List<ImagePredicateButton> buttons = new ArrayList();

      for(int i = 0; i < 7; ++i) {
         int j = (i + 4) % 7;
         int left = screen.getGuiLeft();
         int top = screen.getGuiTop();
         int pX;
         int pY;
         byte width;
         byte height;
         if (i < 3) {
            pX = left + 4 + i * 26;
            pY = top + 2;
            width = 24;
            height = 21;
         } else {
            pX = left + 233;
            pY = top + 30 + (i - 3) * 26;
            width = 21;
            height = 24;
         }

         if (j != currentMenu) {
            ResourceLocation texture = getTabButtonIcon(j);
            RenderCheck check = () -> {
               return renderCheck;
            };
            ImagePredicateButton button = new ImagePredicateButton(pX, pY, width, height, texture, (onPress) -> {
               TensuraNetwork.INSTANCE.sendToServer(new GUISwitchPacket(j));
            }, (button1, poseStack1, x, y) -> {
               screen.m_96602_(poseStack1, getMenuName(j), x, y);
            }, check);
            buttons.add(button);
         }
      }

      buttons.add(addSettingsButton(screen));
      return ImmutableList.copyOf(buttons);
   }

   public static ImagePredicateButton addSettingsButton(AbstractContainerScreen<?> screen) {
      int pX = screen.getGuiLeft() + 202;
      int pY = screen.getGuiTop() + 2;
      int width = 24;
      int height = 21;
      return new ImagePredicateButton(pX, pY, width, height, SETTINGS_TAB, (onPress) -> {
         TensuraNetwork.INSTANCE.sendToServer(new GUISwitchPacket(8));
      }, (button1, poseStack1, x, y) -> {
         screen.m_96602_(poseStack1, Component.m_237115_("tensura.settings"), x, y);
      }, () -> {
         return true;
      });
   }

   public static void renderTabIcon(PoseStack poseStack, AbstractContainerScreen<?> screen, int menu, int mouseX, int mouseY) {
      int guiLeft = screen.getGuiLeft();
      int guiTop = screen.getGuiTop();
      boolean top = false;
      int x;
      int y;
      int tooltipX;
      int tooltipY;
      if (menu < 4) {
         x = guiLeft + 234;
         y = guiTop + 34 + menu * 26;
         tooltipX = x - 2;
         tooltipY = y - 5;
      } else {
         x = guiLeft + 8 + (menu - 4) * 26;
         y = guiTop + 5;
         tooltipX = x - 5;
         tooltipY = guiTop - 1;
         top = true;
      }

      int width = top ? 25 : 24;
      int height = top ? 24 : 25;
      RenderSystem.m_157456_(0, getTabIcon(menu));
      GuiComponent.m_93133_(poseStack, x, y, 0.0F, 0.0F, 16, 16, 16, 16);
      if (mouseOver(mouseX, mouseY, tooltipX, tooltipX + width, tooltipY, tooltipY + height)) {
         screen.m_96602_(poseStack, getMenuName(menu), mouseX, mouseY);
      }

   }

   public static Component getMenuName(int menu) {
      Object var10000;
      switch(menu) {
      case 0:
         var10000 = Component.m_237115_("tensura.main_menu");
         break;
      case 1:
      case 2:
      case 3:
      default:
         var10000 = SkillHelper.comingSoon();
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill_menu");
         break;
      case 5:
         var10000 = Component.m_237115_("tensura.magic_menu");
         break;
      case 6:
         var10000 = Component.m_237115_("tensura.battlewill_menu");
      }

      return (Component)var10000;
   }

   public static ResourceLocation getTabIcon(int menu) {
      ResourceLocation var10000;
      switch(menu) {
      case 0:
         var10000 = new ResourceLocation("tensura", "textures/gui/main/status_icon.png");
         break;
      case 1:
      case 2:
      case 3:
      default:
         var10000 = COMING_SOON_ICON;
         break;
      case 4:
         var10000 = new ResourceLocation("tensura", "textures/gui/skill/skill_tab_icon.png");
         break;
      case 5:
         var10000 = new ResourceLocation("tensura", "textures/gui/magic/magic_tab_icon.png");
         break;
      case 6:
         var10000 = new ResourceLocation("tensura", "textures/item/battlewill_manual.png");
      }

      return var10000;
   }

   public static ResourceLocation getTabButtonIcon(int menu) {
      ResourceLocation var10000;
      switch(menu) {
      case 0:
         var10000 = new ResourceLocation("tensura", "textures/gui/main/status_tab.png");
         break;
      case 1:
      case 2:
      case 3:
      default:
         var10000 = COMING_SOON_TAB;
         break;
      case 4:
         var10000 = new ResourceLocation("tensura", "textures/gui/skill/skill_tab_button.png");
         break;
      case 5:
         var10000 = new ResourceLocation("tensura", "textures/gui/magic/magic_tab_button.png");
         break;
      case 6:
         var10000 = new ResourceLocation("tensura", "textures/gui/battlewill/battlewill_tab_button.png");
      }

      return var10000;
   }

   public static void playSound(SoundEvent event, float pitch) {
      Minecraft.m_91087_().m_91106_().m_120367_(SimpleSoundInstance.m_119752_(event, pitch));
   }

   public static boolean buttonClick(Player player, AbstractContainerScreen<?> screen, @Nullable SoundEvent soundEvent, int id) {
      screen.m_6262_().m_6366_(player, id);
      if (soundEvent != null && player.f_19853_.m_5776_()) {
         playSound(soundEvent, 1.0F);
      }

      TensuraNetwork.INSTANCE.sendToServer(new RequestContainerButtonClickPacket(screen.m_6262_().f_38840_, id));
      return true;
   }

   public static boolean mouseOver(double mouseX, double mouseY, int x1, int x2, int y1, int y2) {
      return mouseOver((int)mouseX, (int)mouseY, x1, x2, y1, y2);
   }

   public static boolean mouseOver(int mouseX, int mouseY, int x1, int x2, int y1, int y2) {
      return mouseX > x1 && mouseX < x2 && mouseY > y1 && mouseY < y2;
   }

   public static Component shortenTextComponent(Component text, int length) {
      return shortenTextComponent(text.getString(), length).m_6881_().m_130948_(text.m_7383_());
   }

   public static Component shortenTextComponent(String text, int length) {
      if (text.length() > length) {
         String shorten = text.substring(0, length);
         if (String.valueOf(shorten.charAt(length - 1)).equals(" ")) {
            shorten = text.substring(0, length - 1);
         }

         return Component.m_237113_(shorten + "...");
      } else {
         return Component.m_237113_(text);
      }
   }

   public static Component shortenNumberComponent(double number) {
      if (number < 1000000.0D) {
         return Component.m_237113_((new DecimalFormat("#")).format(number));
      } else {
         DecimalFormat decimal = new DecimalFormat("#.##");
         return number < 1.0E9D ? Component.m_237110_("tensura.main_menu.million_index", new Object[]{decimal.format(number / 1000000.0D)}) : Component.m_237110_("tensura.main_menu.billion_index", new Object[]{decimal.format(number / 1.0E9D)});
      }
   }

   public static void renderCenteredXText(Font font, PoseStack poseStack, Component text, int x, int y, int width, Color color, boolean shadow) {
      int centered = x + (width - font.m_92852_(text)) / 2;
      if (shadow) {
         font.m_92763_(poseStack, text, (float)centered, (float)y, color.getRGB());
      } else {
         font.m_92889_(poseStack, text, (float)centered, (float)y, color.getRGB());
      }

   }

   public static void renderScaledCenteredXText(Font font, PoseStack poseStack, Component text, int x, int y, int width, int height, Color color, boolean shadow) {
      renderScaledCenteredXText(poseStack, font, text, (float)x, (float)y, (float)width, (float)height, color.getRGB(), 0.0F, 0.01F, shadow);
   }

   public static void renderScaledCenteredXText(Font font, PoseStack poseStack, Component text, int x, int y, int width, int height, int color, boolean shadow) {
      renderScaledCenteredXText(poseStack, font, text, (float)x, (float)y, (float)width, (float)height, color, 0.0F, 0.01F, shadow);
   }

   private static void renderScaledCenteredXText(PoseStack poseStack, Font font, FormattedText text, float x, float y, float width, float height, int color, float spacePerLine, float scalingSteps, boolean shadow) {
      float scaling = 1.0F;

      while(true) {
         List<FormattedCharSequence> formattedCharSequences = font.m_92923_(text, Math.round(width / scaling));
         float var10000 = (float)formattedCharSequences.size();
         Objects.requireNonNull(font);
         if (!(var10000 * (9.0F + spacePerLine) * scaling > height)) {
            int centered = (int)(x + (width - (float)font.m_92852_(text)) / 2.0F);
            renderScaledShadowText(poseStack, font, scaling, font.m_92923_(text, Math.round(width / scaling)), (float)centered, y, color, spacePerLine);
            return;
         }

         scaling -= scalingSteps;
      }
   }

   public static void renderScaledShadowText(PoseStack poseStack, Font font, FormattedText text, float x, float y, float width, float height, int color, float spacePerLine, float scalingSteps) {
      float scaling = 1.0F;

      while(true) {
         List<FormattedCharSequence> formattedCharSequences = font.m_92923_(text, Math.round(width / scaling));
         float var10000 = (float)formattedCharSequences.size();
         Objects.requireNonNull(font);
         if (!(var10000 * (9.0F + spacePerLine) * scaling > height)) {
            renderScaledShadowText(poseStack, font, scaling, font.m_92923_(text, Math.round(width / scaling)), x, y, color, spacePerLine);
            return;
         }

         scaling -= scalingSteps;
      }
   }

   private static void renderScaledShadowText(PoseStack poseStack, Font font, float scaling, List<FormattedCharSequence> text, float x, float y, int color, float spacePerLine) {
      poseStack.m_85841_(scaling, scaling, scaling);

      for(Iterator iterator = text.iterator(); iterator.hasNext(); y += (9.0F + spacePerLine) * scaling) {
         FormattedCharSequence charSequence = (FormattedCharSequence)iterator.next();
         font.m_92744_(poseStack, charSequence, x / scaling, y / scaling, color);
         Objects.requireNonNull(font);
      }

      poseStack.m_85841_(1.0F / scaling, 1.0F / scaling, 1.0F / scaling);
   }

   public static int renderWrappedText(PoseStack poseStack, Font font, FormattedText text, int x, int y, int width, int color, int spacePerLine) {
      return renderWrappedText(poseStack, font, font.m_92923_(text, width), x, y, color, spacePerLine);
   }

   public static int renderCenteredWrappedText(PoseStack poseStack, Font font, FormattedText text, int x, int y, int width, int color, int spacePerLine) {
      return renderCenteredWrappedText(poseStack, font, font.m_92923_(text, width), x, y, width, color, spacePerLine);
   }

   public static int renderWrappedText(PoseStack poseStack, Font font, List<FormattedCharSequence> text, int x, int y, int color, int spacePerLine) {
      for(Iterator var8 = text.iterator(); var8.hasNext(); y += 9 + spacePerLine) {
         FormattedCharSequence charSequence = (FormattedCharSequence)var8.next();
         font.m_92877_(poseStack, charSequence, (float)x, (float)y, color);
      }

      return y;
   }

   public static int renderCenteredWrappedText(PoseStack poseStack, Font font, List<FormattedCharSequence> text, int x, int y, int width, int color, int spacePerLine) {
      for(Iterator var8 = text.iterator(); var8.hasNext(); y += 9 + spacePerLine) {
         FormattedCharSequence charSequence = (FormattedCharSequence)var8.next();
         int centered = x + (width - font.m_92724_(charSequence)) / 2;
         font.m_92877_(poseStack, charSequence, (float)centered, (float)y, color);
      }

      return y;
   }

   public static void renderTextureOverlay(ResourceLocation pTextureLocation, float pAlpha, double screenHeight, double screenWidth) {
      renderTextureOverlay(pTextureLocation, 1.0F, 1.0F, 1.0F, pAlpha, screenHeight, screenWidth);
   }

   public static void renderTextureOverlay(ResourceLocation pTextureLocation, int pColor, float pAlpha, double screenHeight, double screenWidth) {
      renderTextureOverlay(pTextureLocation, (float)(pColor >> 16 & 255) / 255.0F, (float)(pColor >> 8 & 255) / 255.0F, (float)(pColor & 255) / 255.0F, pAlpha, screenHeight, screenWidth);
   }

   public static void renderTextureOverlay(ResourceLocation pTextureLocation, float red, float green, float blue, float pAlpha, double screenHeight, double screenWidth) {
      RenderSystem.m_69465_();
      RenderSystem.m_69458_(false);
      RenderSystem.m_69453_();
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157429_(red, green, blue, pAlpha);
      RenderSystem.m_157456_(0, pTextureLocation);
      Tesselator tesselator = Tesselator.m_85913_();
      BufferBuilder bufferbuilder = tesselator.m_85915_();
      bufferbuilder.m_166779_(Mode.QUADS, DefaultVertexFormat.f_85817_);
      bufferbuilder.m_5483_(0.0D, screenHeight, -90.0D).m_7421_(0.0F, 1.0F).m_5752_();
      bufferbuilder.m_5483_(screenWidth, screenHeight, -90.0D).m_7421_(1.0F, 1.0F).m_5752_();
      bufferbuilder.m_5483_(screenWidth, 0.0D, -90.0D).m_7421_(1.0F, 0.0F).m_5752_();
      bufferbuilder.m_5483_(0.0D, 0.0D, -90.0D).m_7421_(0.0F, 0.0F).m_5752_();
      tesselator.m_85914_();
      RenderSystem.m_69458_(true);
      RenderSystem.m_69482_();
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
   }

   public static void renderFadingTextureWithDuration(int duration, int startFade, ResourceLocation pTextureLocation, double screenHeight, double screenWidth) {
      float alphaValue = 1.0F;
      if (duration < startFade) {
         alphaValue = (float)duration / 200.0F;
      }

      renderTextureOverlay(pTextureLocation, alphaValue, screenHeight, screenWidth);
   }

   public static void renderFadingTextureWithDuration(int duration, int startFade, int color, ResourceLocation pTextureLocation, double screenHeight, double screenWidth) {
      float alphaValue = 1.0F;
      if (duration < startFade) {
         alphaValue = (float)duration / 200.0F;
      }

      renderTextureOverlay(pTextureLocation, color, alphaValue, screenHeight, screenWidth);
   }

   public static void renderFadingTextureWithDuration(int duration, int startFade, float red, float green, float blue, ResourceLocation pTextureLocation, double screenHeight, double screenWidth) {
      float alphaValue = 1.0F;
      if (duration < startFade) {
         alphaValue = (float)duration / 200.0F;
      }

      renderTextureOverlay(pTextureLocation, red, green, blue, alphaValue, screenHeight, screenWidth);
   }

   public static void renderEntityOnScreen(double pPosX, double pPosY, float pScale, float angleXComponent, float angleYComponent, LivingEntity pLivingEntity) {
      angleXComponent = (float)Math.atan((double)(angleXComponent / 40.0F));
      angleYComponent = (float)Math.atan((double)(angleYComponent / 40.0F));
      PoseStack posestack = RenderSystem.m_157191_();
      posestack.m_85836_();
      posestack.m_85837_(pPosX, pPosY, 1050.0D);
      posestack.m_85841_(1.0F, 1.0F, -1.0F);
      RenderSystem.m_157182_();
      PoseStack posestack1 = new PoseStack();
      posestack1.m_85837_(0.0D, 0.0D, 1000.0D);
      posestack1.m_85841_(pScale, pScale, pScale);
      Quaternion quaternion = Vector3f.f_122227_.m_122240_(180.0F);
      Quaternion quaternion1 = Vector3f.f_122223_.m_122240_(angleYComponent * 20.0F);
      quaternion.m_80148_(quaternion1);
      posestack1.m_85845_(quaternion);
      float yBodyRot = pLivingEntity.f_20883_;
      float yRot = pLivingEntity.m_146908_();
      float xRot = pLivingEntity.m_146909_();
      float yHeadRotO = pLivingEntity.f_20886_;
      float yHeadRot = pLivingEntity.f_20885_;
      pLivingEntity.f_20883_ = 180.0F + angleXComponent * 20.0F;
      pLivingEntity.m_146922_(180.0F + angleXComponent * 40.0F);
      pLivingEntity.m_146926_(-angleYComponent * 20.0F);
      pLivingEntity.f_20885_ = pLivingEntity.m_146908_();
      pLivingEntity.f_20886_ = pLivingEntity.m_146908_();
      Lighting.m_166384_();
      EntityRenderDispatcher entityrenderdispatcher = Minecraft.m_91087_().m_91290_();
      quaternion1.m_80157_();
      entityrenderdispatcher.m_114412_(quaternion1);
      entityrenderdispatcher.m_114468_(false);
      BufferSource bufferSource = Minecraft.m_91087_().m_91269_().m_110104_();
      RenderSystem.m_69890_(() -> {
         entityrenderdispatcher.m_114384_(pLivingEntity, 0.0D, 0.0D, 0.0D, 0.0F, 1.0F, posestack1, bufferSource, 15728880);
      });
      bufferSource.m_109911_();
      entityrenderdispatcher.m_114468_(true);
      pLivingEntity.f_20883_ = yBodyRot;
      pLivingEntity.m_146922_(yRot);
      pLivingEntity.m_146926_(xRot);
      pLivingEntity.f_20886_ = yHeadRotO;
      pLivingEntity.f_20885_ = yHeadRot;
      posestack.m_85849_();
      RenderSystem.m_157182_();
      Lighting.m_84931_();
   }
}
