package com.github.manasmods.tensura.handler.client;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.resist.ResistSkill;
import com.github.manasmods.tensura.api.entity.subclass.ILivingPartEntity;
import com.github.manasmods.tensura.block.entity.CharybdisCoreBlockEntity;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.client.TensuraGUIHelper;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.client.screen.SettingsScreen;
import com.github.manasmods.tensura.config.client.DisplayConfig;
import com.github.manasmods.tensura.config.client.TensuraClientConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import java.awt.Color;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import net.minecraft.ChatFormatting;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.Font;
import net.minecraft.client.gui.GuiComponent;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.FormattedText;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.CropBlock;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.properties.IntegerProperty;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.RegisterGuiOverlaysEvent;
import net.minecraftforge.client.gui.overlay.VanillaGuiOverlay;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.MOD,
   value = {Dist.CLIENT}
)
public class HUDHandler {
   private static final ResourceLocation MAIN_HUD = new ResourceLocation("tensura", "textures/player_hud/main_hud.png");
   private static final ResourceLocation SPECTATOR_HUD = new ResourceLocation("tensura", "textures/player_hud/spectator_hud.png");
   private static final ResourceLocation CREATIVE_HUD = new ResourceLocation("tensura", "textures/player_hud/creative_hud.png");
   private static final ResourceLocation HARDCORE_HUD = new ResourceLocation("tensura", "textures/player_hud/hardcore_hud.png");
   private static final ResourceLocation MAIN_HUD_BARS = new ResourceLocation("tensura", "textures/player_hud/main_hud_bars.png");
   private static final ResourceLocation CREATIVE_HUD_BARS = new ResourceLocation("tensura", "textures/player_hud/creative_hud_bars.png");
   private static final ResourceLocation HARDCORE_HUD_BARS = new ResourceLocation("tensura", "textures/player_hud/hardcore_hud_bars.png");
   private static final ResourceLocation SEVERANCE_BAR = new ResourceLocation("tensura", "textures/player_hud/hp_bars/severance_hp_bar.png");
   private static final ResourceLocation HEALTH_BAR = new ResourceLocation("tensura", "textures/player_hud/hp_bars/hp_bar.png");
   private static final ResourceLocation INFECTION_HEALTH_BAR = new ResourceLocation("tensura", "textures/player_hud/hp_bars/infection_hp_bar.png");
   private static final ResourceLocation WITHER_HEALTH_BAR = new ResourceLocation("tensura", "textures/player_hud/hp_bars/wither_hp_bar.png");
   private static final ResourceLocation FATAL_POISON_HEALTH_BAR = new ResourceLocation("tensura", "textures/player_hud/hp_bars/fatal_poison_hp_bar.png");
   private static final ResourceLocation CORROSION_HEALTH_BAR = new ResourceLocation("tensura", "textures/player_hud/hp_bars/corrosion_hp_bar.png");
   private static final ResourceLocation POISON_HEALTH_BAR = new ResourceLocation("tensura", "textures/player_hud/hp_bars/poison_hp_bar.png");
   private static final ResourceLocation FROZEN_HEALTH_BAR = new ResourceLocation("tensura", "textures/player_hud/hp_bars/frozen_hp_bar.png");
   private static final ResourceLocation CURSE_HEALTH_BAR = new ResourceLocation("tensura", "textures/player_hud/hp_bars/curse_hp_bar.png");
   private static final ResourceLocation ABSORPTION_HEALTH_BAR = new ResourceLocation("tensura", "textures/player_hud/hp_bars/absorption_hp_bar.png");
   private static final ResourceLocation SPIRITUAL_HP_BAR = new ResourceLocation("tensura", "textures/player_hud/hp_bars/shp_bar.png");
   private static final ResourceLocation SOUL_DRAIN_SPIRITUAL_HP_BAR = new ResourceLocation("tensura", "textures/player_hud/hp_bars/soul_drain_shp_bar.png");
   private static final ResourceLocation INSANITY_SPIRITUAL_HP_BAR = new ResourceLocation("tensura", "textures/player_hud/hp_bars/insanity_shp_bar.png");
   private static final ResourceLocation HEART = new ResourceLocation("tensura", "textures/player_hud/heart.png");
   private static final ResourceLocation SPIRITUAL_HEART = new ResourceLocation("tensura", "textures/player_hud/spiritual_heart.png");
   private static final ResourceLocation MANA_BAR = new ResourceLocation("tensura", "textures/player_hud/mana_bar.png");
   private static final ResourceLocation AURA_BAR = new ResourceLocation("tensura", "textures/player_hud/aura_bar.png");
   private static final ResourceLocation BARRIER = new ResourceLocation("tensura", "textures/player_hud/barrier.png");
   private static final ResourceLocation EMPTY_HUNGER = new ResourceLocation("tensura", "textures/player_hud/food_empty_hunger.png");
   private static final ResourceLocation FULL_HUNGER = new ResourceLocation("tensura", "textures/player_hud/food_full_hunger.png");
   private static final ResourceLocation EMPTY_FOOD = new ResourceLocation("tensura", "textures/player_hud/food_empty.png");
   private static final ResourceLocation EMPTY_ARMOR = new ResourceLocation("tensura", "textures/player_hud/armor_empty.png");
   private static final ResourceLocation FULL_ARMOR = new ResourceLocation("tensura", "textures/player_hud/armor.png");
   private static final ResourceLocation SKILL_OVERLAY = new ResourceLocation("tensura", "textures/player_hud/skill_overlay.png");
   private static final ResourceLocation SKILL_OVERLAY_TEXT_BARS = new ResourceLocation("tensura", "textures/player_hud/skill_overlay_text_bars.png");
   private static final ResourceLocation SKILL_OVERLAY_MASTERY_BAR = new ResourceLocation("tensura", "textures/player_hud/mastery_bar.png");
   private static final ResourceLocation SKILL_OVERLAY_LEARNING_BAR = new ResourceLocation("tensura", "textures/player_hud/learning_bar.png");
   private static final ResourceLocation SKILL_OVERLAY_COOLDOWN_ICON = new ResourceLocation("tensura", "textures/player_hud/cooldown_icon.png");
   private static final ResourceLocation ANALYSIS_OVERLAY = new ResourceLocation("tensura", "textures/player_hud/analysis_overlay.png");
   private static final ResourceLocation ANALYSIS_OVERLAY_BLOCKS = new ResourceLocation("tensura", "textures/player_hud/analysis_overlay_blocks.png");
   private static final ResourceLocation MOUNT_HEART = new ResourceLocation("tensura", "textures/player_hud/mount_heart.png");
   private static final ResourceLocation MOUNT_SPIRITUAL_HEART = new ResourceLocation("tensura", "textures/player_hud/mount_spiritual_heart.png");
   private static DisplayConfig cfg;
   private static boolean leftSideStats;
   private static boolean flippedSkills;
   private static boolean leftSideAnalysis;
   private static int screenX;
   private static int screenY;
   private static int appraisalOpacity;
   private static double size;
   private static LocalPlayer player;
   private static Font font;
   private static PoseStack poseStack;
   public static final int[] hudPositions = new int[2];
   public static final int[] skillOverlayPositions = new int[2];
   private static int appraisalPosX;
   private static int slot;
   private static int slotSwitch;

   @SubscribeEvent
   public static void registerOverlay(RegisterGuiOverlaysEvent event) {
      event.registerBelow(VanillaGuiOverlay.SLEEP_FADE.id(), "tensura_hud", (gui, pPoseStack, partialTick, screenWidth, screenHeight) -> {
         if (TensuraClientConfig.SPEC.isLoaded()) {
            if (gui.getMinecraft().f_91074_ != null) {
               if (TensuraPlayerCapability.getRace(gui.getMinecraft().f_91074_) != null) {
                  player = gui.getMinecraft().f_91074_;
                  if (!player.m_5803_() || !player.m_21023_((MobEffect)TensuraMobEffects.INSANITY.get())) {
                     int hudVariant = (Integer)TensuraClientConfig.INSTANCE.displayConfig.hudVariant.get();
                     if (hudVariant != 0) {
                        gui.setupOverlayRenderState(true, false);
                        RenderSystem.m_157427_(GameRenderer::m_172817_);
                        RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
                        cfg = TensuraClientConfig.INSTANCE.displayConfig;
                        leftSideStats = (Boolean)cfg.leftSideStats.get();
                        flippedSkills = (Boolean)cfg.leftSideSkills.get();
                        size = (Double)cfg.size.get();
                        appraisalOpacity = (Integer)cfg.appraisalOpacity.get();
                        screenX = screenWidth;
                        screenY = screenHeight;
                        font = gui.m_93082_();
                        poseStack = pPoseStack;
                        updatePositions();
                        if (poseStack != null) {
                           boolean scaled = size != 1.0D;
                           if (scaled) {
                              poseStack.m_85836_();
                              RenderSystem.m_157182_();
                              poseStack.m_85837_(leftSideStats ? 0.0D : (double)screenX - (double)screenX * size, 0.0D, 0.0D);
                              poseStack.m_85841_((float)size, (float)size, (float)size);
                           }

                           if (hudVariant == 3) {
                              mixedHudRendering();
                              if (scaled) {
                                 poseStack.m_85849_();
                              }

                           } else if (player.m_5833_()) {
                              simpleRenderTexture(SPECTATOR_HUD, 0, 0, 256, 256);
                              renderPlayer();
                              if (scaled) {
                                 poseStack.m_85849_();
                              }

                           } else {
                              if (player.m_7500_()) {
                                 simpleRenderTexture(CREATIVE_HUD, 0, 0, 256, 256);
                              } else {
                                 simpleRenderTexture(player.m_9236_().m_6106_().m_5466_() ? HARDCORE_HUD : MAIN_HUD, 0, 0, 256, 256);
                              }

                              renderPlayer();
                              renderHPbars();
                              renderMPandAPbars(false);
                              renderMiscellaneous();
                              if (!scaled) {
                                 renderSkills();
                              } else {
                                 poseStack.m_85849_();
                                 poseStack.m_85836_();
                                 RenderSystem.m_157182_();
                                 poseStack.m_85837_(skillOverlayPositions[0] == screenX - 41 ? (double)screenX - (double)screenX * size : 0.0D, 0.0D, 0.0D);
                                 poseStack.m_85841_((float)size, (float)size, (float)size);
                                 renderSkills();
                                 poseStack.m_85849_();
                                 RenderSystem.m_157182_();
                              }
                           }
                        }
                     }
                  }
               }
            }
         }
      });
      event.registerBelow(VanillaGuiOverlay.SLEEP_FADE.id(), "tensura_analysis", (gui, pPoseStack, partialTick, screenWidth, screenHeight) -> {
         if (player != null && poseStack != null && cfg != null) {
            int level = TensuraSkillCapability.getAnalysisLevel(player);
            if (level <= 0) {
               appraisalPosX = leftSideAnalysis ? -95 : screenX;
               slot = 0;
               slotSwitch = 0;
            } else {
               int distance = TensuraSkillCapability.getAnalysisDistance(player);
               if (distance > 0) {
                  leftSideAnalysis = (Boolean)cfg.leftSideAnalysis.get();
                  if (size == 1.0D) {
                     renderAppraisal(level, distance);
                  } else {
                     poseStack.m_85836_();
                     RenderSystem.m_157182_();
                     poseStack.m_85837_(leftSideAnalysis ? 0.0D : (double)screenX - (double)screenX * size, (double)screenY / 3.0D - (double)screenY / 3.0D * size, 0.0D);
                     poseStack.m_85841_((float)size, (float)size, (float)size);
                     renderAppraisal(level, distance);
                     poseStack.m_85849_();
                     RenderSystem.m_157182_();
                  }
               }
            }
         }
      });
   }

   private static void mixedHudRendering() {
      boolean scaled = size != 1.0D;
      if (scaled) {
         poseStack.m_85836_();
         RenderSystem.m_157182_();
         poseStack.m_85841_((float)size, (float)size, (float)size);
      }

      renderMPandAPbars(true);
      if (!scaled) {
         renderSkills();
      } else {
         poseStack.m_85849_();
         poseStack.m_85836_();
         RenderSystem.m_157182_();
         poseStack.m_85837_(skillOverlayPositions[0] == screenX - 41 ? (double)screenX - (double)screenX * size : 0.0D, 0.0D, 0.0D);
         poseStack.m_85841_((float)size, (float)size, (float)size);
         renderSkills();
         poseStack.m_85849_();
         RenderSystem.m_157182_();
      }
   }

   private static void renderPlayer() {
      if (!Minecraft.m_91087_().f_91066_.f_92063_) {
         float sizeMultiplier = RaceHelper.getSizeMultiplier(player);
         sizeMultiplier = sizeMultiplier > 1.0F ? 1.0F / sizeMultiplier : 1.0F;
         double x;
         if (leftSideStats) {
            x = 22.0D * size;
         } else {
            x = (double)screenX - 22.0D * size;
         }

         double y = (double)(player.m_5833_() ? 47 : 37) * size;
         float scale = (float)(16.0D * size) * sizeMultiplier;
         float lookAngle = leftSideStats ? -((float)screenX / 2.0F) + 40.0F : (float)screenX / 2.0F;
         TensuraGUIHelper.renderEntityOnScreen(x, y, scale, lookAngle, 0.0F, player);
      }
   }

   private static void renderHPbars() {
      int maxHealthPoints = Math.round(player.m_21233_());
      int maxSpiritualHP = (int)Math.round(player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get()));
      int healthPoints = (int)Math.ceil((double)player.m_21223_());
      int spiritualHP = (int)Math.ceil(TensuraEPCapability.getSpiritualHealth(player));
      ResourceLocation HPbar = getHealthBarToRender(false);
      ResourceLocation spiritualHPbar = getHealthBarToRender(true);
      int width = dynamicResizing(0, 101, (double)healthPoints, (double)maxHealthPoints);
      renderCutTexture(HPbar, 44, 5, width, 9, 101, 9, 1.0F);
      renderSeverance();
      width = dynamicResizing(0, 85, (double)spiritualHP, (double)maxSpiritualHP);
      renderCutTexture(spiritualHPbar, 44, 19, width, 9, 85, 9, 1.0F);
      int posX = leftSideStats ? 46 : screenX - 147;
      Component text = Component.m_237113_(String.format("%s/%s", healthPoints, maxHealthPoints));
      TensuraGUIHelper.renderScaledCenteredXText(font, poseStack, text, posX, 6, 101, 10, Color.WHITE, true);
      posX = leftSideStats ? 46 : screenX - 131;
      text = Component.m_237113_(String.format("%s/%s", spiritualHP, maxSpiritualHP));
      TensuraGUIHelper.renderScaledCenteredXText(font, poseStack, text, posX, 20, 85, 10, Color.WHITE, true);
   }

   private static void renderMPandAPbars(boolean mixed) {
      int maxMana = (int)player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
      int maxAura = (int)player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get());
      int mana = (int)TensuraPlayerCapability.getMagicule(player);
      int aura = (int)TensuraPlayerCapability.getAura(player);
      int hudX;
      int hudY;
      if (mixed) {
         hudX = hudPositions[0];
         hudY = hudPositions[1];
         if (hudX == -1) {
            hudX = screenX / 2 + (leftSideStats ? -173 : 92);
         }

         if (hudY == -1) {
            hudY = screenY - 23;
         }

         RenderSystem.m_69478_();
         RenderSystem.m_69453_();
         RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
         if (player.m_7500_()) {
            RenderSystem.m_157456_(0, CREATIVE_HUD_BARS);
         } else if (player.m_9236_().m_6106_().m_5466_()) {
            RenderSystem.m_157456_(0, HARDCORE_HUD_BARS);
         } else {
            RenderSystem.m_157456_(0, MAIN_HUD_BARS);
         }

         GuiComponent.m_93160_(poseStack, hudX, hudY, 81, 23, 0.0F, 0.0F, leftSideStats ? -81 : 81, 23, 81, 23);
         hudX += leftSideStats ? 76 : 5;
         int width = dynamicResizing(0, 67, (double)mana, (double)maxMana);
         RenderSystem.m_157456_(0, MANA_BAR);
         GuiComponent.m_93160_(poseStack, leftSideStats ? hudX - width : hudX, hudY + 2, width, 9, 0.0F, 0.0F, width, 9, 67, 9);
         width = dynamicResizing(0, 67, (double)aura, (double)maxAura);
         hudY += 10;
         RenderSystem.m_157456_(0, AURA_BAR);
         GuiComponent.m_93160_(poseStack, leftSideStats ? hudX - width : hudX, hudY + 2, width, 9, 0.0F, 0.0F, width, 9, 57, 9);
         RenderSystem.m_69461_();
         if (leftSideStats) {
            hudX -= 66;
         }

         hudY -= 10;
         Component text = Component.m_237113_(String.format("%s%%", (int)((double)mana / (double)maxMana * 100.0D)));
         TensuraGUIHelper.renderScaledCenteredXText(font, poseStack, text, hudX, hudY + 3, 67, 10, Color.WHITE, true);
         hudY += 10;
         text = Component.m_237113_(String.format("%s%%", (int)((double)aura / (double)maxAura * 100.0D)));
         TensuraGUIHelper.renderScaledCenteredXText(font, poseStack, text, hudX, hudY + 3, 67, 10, Color.WHITE, true);
      } else {
         hudX = dynamicResizing(0, 67, (double)mana, (double)maxMana);
         renderCutTexture(MANA_BAR, 5, 46, hudX, 9, 67, 9, 1.0F);
         hudX = dynamicResizing(0, 57, (double)aura, (double)maxAura);
         renderCutTexture(AURA_BAR, 5, 56, hudX, 9, 57, 9, 1.0F);
         hudY = leftSideStats ? 7 : screenX - 74;
         Component text = Component.m_237113_(String.format("%s%%", (int)((double)mana / (double)maxMana * 100.0D)));
         TensuraGUIHelper.renderScaledCenteredXText(font, poseStack, text, hudY, 47, 67, 10, Color.WHITE, true);
         hudY = leftSideStats ? 7 : screenX - 64;
         text = Component.m_237113_(String.format("%s%%", (int)((double)aura / (double)maxAura * 100.0D)));
         TensuraGUIHelper.renderScaledCenteredXText(font, poseStack, text, hudY, 57, 57, 10, Color.WHITE, true);
      }
   }

   private static void renderSkills() {
      int skillOverlayX = skillOverlayPositions[0];
      int skillOverlayY = skillOverlayPositions[1];
      skillOverlayRendering(SKILL_OVERLAY, skillOverlayX, skillOverlayY, flippedSkills ? -41 : 41, 104, 41, 1.0F);
      TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
         boolean showFullInfo = TensuraKeybinds.NEXT_MODE_CHANGE.m_90857_() || TensuraKeybinds.PREVIOUS_MODE_CHANGE.m_90857_();
         if (showFullInfo) {
            skillOverlayRendering(SKILL_OVERLAY_TEXT_BARS, flippedSkills ? skillOverlayX - 117 : skillOverlayX + 41, skillOverlayY + 4, flippedSkills ? -256 : 256, 256, 117, 1.0F);
         }

         for(int i = 0; i < 3; ++i) {
            ManasSkill skill = cap.getSkillInSlot(i);
            if (skill != null) {
               ManasSkillInstance skillInstance = (ManasSkillInstance)SkillAPI.getSkillsFrom(player).getSkill(skill).orElse(skill.createDefaultInstance());
               ResourceLocation icon = skill.getSkillIcon();
               int maxMastery;
               int mastery;
               if (icon != null) {
                  maxMastery = flippedSkills ? skillOverlayX + 1 : skillOverlayX + 8;
                  mastery = skillOverlayY + 1 + i * 35;
                  skillOverlayRendering(icon, maxMastery, mastery, flippedSkills ? -32 : 32, 32, 32, 1.0F);
               }

               maxMastery = skill.getMaxMastery();
               mastery = skillInstance.getMastery();
               boolean isLearning = mastery < 0;
               int masteryBarHeight = dynamicResizing(0, 22, (double)mastery, (double)maxMastery);
               int max;
               if (isLearning) {
                  max = 100;
                  if (skill instanceof ResistSkill) {
                     ResistSkill resistSkill = (ResistSkill)skill;
                     max = resistSkill.pointRequirement();
                  }

                  masteryBarHeight = dynamicResizing(0, 22, (double)(max - Math.abs(mastery)), (double)max);
               }

               max = skillOverlayY + 6 + i * 35 + (22 - masteryBarHeight);
               skillOverlayRendering(isLearning ? SKILL_OVERLAY_LEARNING_BAR : SKILL_OVERLAY_MASTERY_BAR, flippedSkills ? skillOverlayX + 34 : skillOverlayX + 2, max, 5, masteryBarHeight, 5, 1.0F);
               boolean onCooldown = skillInstance.onCoolDown() && !skillInstance.canIgnoreCoolDown(player);
               FormattedText modeText;
               int posX;
               if (onCooldown) {
                  String cooldown = String.valueOf(skillInstance.getCoolDown());
                  int posX1 = flippedSkills ? skillOverlayX - 38 + 41 : skillOverlayX + 10;
                  int posX2 = flippedSkills ? skillOverlayX - 10 + 41 : skillOverlayX + 38;
                  int[] positions = getCenteredTextPositions(cooldown, posX1, skillOverlayY + 8 + i * 35, posX2, skillOverlayY + 32 + i * 35, 7);
                  modeText = FormattedText.m_130762_(cooldown, Style.f_131099_);
                  posX = skillOverlayY + 3 + i * 35;
                  skillOverlayRendering(SKILL_OVERLAY_COOLDOWN_ICON, flippedSkills ? skillOverlayX + 3 : skillOverlayX + 10, posX, flippedSkills ? -28 : 28, 28, 28, 0.9F);
                  TensuraGUIHelper.renderScaledShadowText(poseStack, font, modeText, (float)positions[0], (float)positions[1], 32.0F, 32.0F, Color.RED.getRGB(), 2.0F, 0.01F);
               }

               if (showFullInfo) {
                  int color = Color.GRAY.getRGB();
                  String modeName = "tensura.skill.mode.default";
                  if (skill instanceof TensuraSkill) {
                     TensuraSkill tensuraSkill = (TensuraSkill)skill;
                     if (!onCooldown && tensuraSkill.getColoredName() != null) {
                        color = tensuraSkill.getColoredName().m_7383_().m_131135_().m_131265_();
                     }

                     modeName = tensuraSkill.getModeName(skillInstance.getMode()).getString();
                  }

                  String skillName = Component.m_237115_("tensura.race.selection.skills.empty").getString();
                  if (skill.getName() != null) {
                     skillName = skill.getName().getString();
                  }

                  FormattedText nameText = FormattedText.m_130762_(skillName, Style.f_131099_);
                  modeText = FormattedText.m_130762_(modeName, Style.f_131099_);
                  posX = flippedSkills ? skillOverlayX - 111 : skillOverlayX + 44;
                  int posY = skillOverlayY + 8 + i * 35;
                  TensuraGUIHelper.renderScaledShadowText(poseStack, font, nameText, (float)posX, (float)posY, 111.0F, 11.0F, color, 2.0F, 0.01F);
                  posX = flippedSkills ? skillOverlayX - 97 : skillOverlayX + 44;
                  posY = skillOverlayY + 21 + i * 35;
                  TensuraGUIHelper.renderScaledShadowText(poseStack, font, modeText, (float)posX, (float)posY, 97.0F, 10.0F, onCooldown ? Color.DARK_GRAY.getRGB() : Color.WHITE.getRGB(), 2.0F, 0.01F);
               }
            }
         }

      });
   }

   private static void renderAppraisal(int level, int distance) {
      int mode = TensuraSkillCapability.getAnalysisMode(player);
      LivingEntity entity = SkillHelper.getTargetingEntity(player, (double)distance, false, true);
      if ((mode == 0 || mode == 1) && entity != null) {
         renderMobAnalysis(entity, level);
      } else if (mode == 0 || mode == 2) {
         BlockPos blockPos = SkillHelper.getPlayerPOVHitResult(player.f_19853_, player, Fluid.NONE, Block.OUTLINE, (double)distance).m_82425_();
         BlockState block = player.f_19853_.m_8055_(blockPos);
         if (block.m_60795_()) {
            player.f_19853_.m_8055_(SkillHelper.getPlayerPOVHitResult(player.f_19853_, player, Fluid.SOURCE_ONLY, Block.OUTLINE, (double)distance).m_82425_());
         }

         if (block.m_60795_()) {
            appraisalPosX = leftSideAnalysis ? -95 : screenX;
            slot = 0;
            slotSwitch = 0;
            return;
         }

         renderBlockAnalysis(block, blockPos, player.f_19853_);
      }

      if (leftSideAnalysis) {
         if (appraisalPosX < 0) {
            appraisalPosX += 4;
         } else if (appraisalPosX != 0) {
            appraisalPosX = 0;
         }
      } else if (appraisalPosX > screenX - 95) {
         appraisalPosX -= 4;
      } else if (appraisalPosX != screenX - 95) {
         appraisalPosX = screenX - 95;
      }

   }

   private static void renderMobAnalysis(LivingEntity target, int level) {
      if (target instanceof ILivingPartEntity) {
         ILivingPartEntity part = (ILivingPartEntity)target;
         if (part.getHeadId() == null) {
            return;
         }

         List<LivingEntity> list = target.m_9236_().m_6443_(LivingEntity.class, target.m_20191_().m_82400_(15.0D), (entity) -> {
            return Objects.equals(part.getHeadId(), entity.m_20148_());
         });
         if (list.isEmpty()) {
            return;
         }

         target = (LivingEntity)list.get(0);
      }

      Component name = target.m_7755_();
      int health = (int)Math.ceil((double)target.m_21223_());
      int spiritual = (int)Math.ceil(TensuraEPCapability.getSpiritualHealth(target));
      int armor = target.m_21230_();
      int left = appraisalPosX;
      int top = screenY / 4;
      int textY = top + 30;
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157456_(0, ANALYSIS_OVERLAY);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, (float)appraisalOpacity / 100.0F);
      RenderSystem.m_69478_();
      RenderSystem.m_69453_();
      RenderSystem.m_69482_();
      GuiComponent.m_93133_(poseStack, left, top, 0.0F, 0.0F, 95, 156, 95, 156);
      int color = TensuraEPCapability.isChaos(target) ? Color.RED.getRGB() : (TensuraEPCapability.isMajin(target) ? Color.YELLOW.getRGB() : Color.WHITE.getRGB());
      TensuraGUIHelper.renderScaledShadowText(poseStack, font, Component.m_237113_(name.getString()), (float)(left + 8), (float)(top + 12), 79.0F, 9.0F, color, 1.0F, 0.01F);
      if (target instanceof Player) {
         Player playerTarget = (Player)target;
         Race race = TensuraPlayerCapability.getRace(playerTarget);
         if (race != null) {
            String raceName;
            if (race.getName() != null) {
               raceName = race.getName().getString();
            } else {
               raceName = String.valueOf(race.getName());
            }

            textY = TensuraGUIHelper.renderCenteredWrappedText(poseStack, font, (FormattedText)FormattedText.m_130775_(raceName), left + 7, textY, 81, color, 1) + 1;
         }
      }

      String var10000 = Component.m_237115_("tensura.vanilla_attribute.health.shortened_name").getString();
      String text = var10000 + ": " + health;
      font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
      int textureX = getTexturePositionX(text, left + 7) - 4;
      RenderSystem.m_157456_(0, HEART);
      GuiComponent.m_93133_(poseStack, textureX, textY - 1, 0.0F, 0.0F, 9, 9, 9, 9);
      textY += 10;
      var10000 = Component.m_237115_("tensura.attribute.spiritual_health.shortened_name").getString();
      text = var10000 + ": " + spiritual;
      font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
      textureX = getTexturePositionX(text, left + 7) - 4;
      RenderSystem.m_157456_(0, SPIRITUAL_HEART);
      GuiComponent.m_93133_(poseStack, textureX, textY - 1, 0.0F, 0.0F, 9, 9, 9, 9);
      textY += 10;
      var10000 = Component.m_237115_("attribute.name.generic.armor").getString();
      text = var10000 + ": " + armor;
      font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
      textureX = getTexturePositionX(text, left + 7) - 4;
      RenderSystem.m_157456_(0, FULL_ARMOR);
      GuiComponent.m_93133_(poseStack, textureX, textY - 1, 0.0F, 0.0F, 9, 9, 9, 9);
      textY += 10;
      float epDifference = 1.0F + (float)level * 0.5F;
      if (!(TensuraEPCapability.getEP(target) >= (double)epDifference * TensuraEPCapability.getEP(player))) {
         int i;
         Player playerTarget;
         if (target instanceof Player) {
            playerTarget = (Player)target;
            int mana = (int)Math.floor(TensuraPlayerCapability.getMagicule(playerTarget));
            var10000 = Component.m_237115_("tensura.attribute.magicule.shortened_name").getString();
            text = var10000 + ": " + TensuraGUIHelper.shortenNumberComponent((double)mana).getString();
            font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
            textY += 10;
            i = (int)Math.floor(TensuraPlayerCapability.getAura(playerTarget));
            var10000 = Component.m_237115_("tensura.attribute.aura.shortened_name").getString();
            text = var10000 + ": " + TensuraGUIHelper.shortenNumberComponent((double)i).getString();
            font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
            textY += 10;
            var10000 = Component.m_237115_("tensura.attribute.existence_points.shortened_name").getString();
            text = var10000 + ": " + TensuraGUIHelper.shortenNumberComponent((double)((int)Math.ceil(TensuraEPCapability.getEP(target)))).getString();
            font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
         } else {
            var10000 = Component.m_237115_("tensura.attribute.existence_points.shortened_name").getString();
            text = var10000 + ": " + TensuraGUIHelper.shortenNumberComponent((double)((int)Math.ceil(TensuraEPCapability.getCurrentEP(target)))).getString();
            font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
         }

         if (target instanceof Player) {
            playerTarget = (Player)target;
            if (level >= 1) {
               textY = top + 121;
               if (!Minecraft.m_91087_().m_91104_()) {
                  ++slotSwitch;
               }

               if (slotSwitch >= 100) {
                  slot = (slot + 1) % 3;
                  slotSwitch = 0;
               }

               ManasSkill skill = null;
               i = 0;

               while(true) {
                  if (i < 3) {
                     skill = TensuraSkillCapability.getSkillInSlot(playerTarget, slot);
                     if (skill == null) {
                        slot = (slot + 1) % 3;
                        ++i;
                        continue;
                     }
                  }

                  if (skill == null) {
                     return;
                  }

                  Optional<ManasSkillInstance> instance = SkillAPI.getSkillsFrom(playerTarget).getSkill(skill);
                  if (instance.isEmpty() || ((ManasSkillInstance)instance.get()).isMastered(playerTarget)) {
                     return;
                  }

                  MutableComponent skillName = skill.getName();
                  if (skillName == null) {
                     text = String.valueOf(skill.getRegistryName());
                  } else {
                     text = skillName.getString();
                  }

                  int skillColor = Color.WHITE.getRGB();
                  if (skill instanceof Skill) {
                     Skill skill1 = (Skill)skill;
                     ChatFormatting f = skill1.getType().getChatFormatting();
                     if (f.m_126665_() != null) {
                        skillColor = f.m_126665_();
                     }
                  }

                  TensuraGUIHelper.renderCenteredWrappedText(poseStack, font, (FormattedText)FormattedText.m_130775_(text), left + 8, textY, 80, skillColor, 1);
                  break;
               }
            }
         }

      }
   }

   private static void renderBlockAnalysis(BlockState blockState, BlockPos blockPos, Level level) {
      int left = appraisalPosX;
      int top = screenY / 4;
      int textY = top + 30;
      net.minecraft.world.level.block.Block block = blockState.m_60734_();
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157456_(0, ANALYSIS_OVERLAY_BLOCKS);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, (float)appraisalOpacity / 100.0F);
      RenderSystem.m_69478_();
      RenderSystem.m_69453_();
      RenderSystem.m_69482_();
      GuiComponent.m_93133_(poseStack, left, top, 0.0F, 0.0F, 95, 156, 95, 156);
      String text = block.m_49954_().getString();
      TensuraGUIHelper.renderScaledShadowText(poseStack, font, Component.m_237113_(text), (float)(left + 8), (float)(top + 12), 79.0F, 9.0F, Color.WHITE.getRGB(), 1.0F, 0.01F);
      text = Component.m_237115_("tensura.attribute.block.hardness.name").getString();
      font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
      textY += 10;
      float hardness = block.m_155943_();
      if (hardness < 0.0F) {
         text = "-> " + Component.m_237115_("item.unbreakable").getString();
      } else {
         text = "-> " + hardness;
      }

      font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, hardness < 0.0F ? Color.RED.getRGB() : Color.WHITE.getRGB());
      textY += 10;
      boolean correct = ForgeHooks.isCorrectToolForDrops(blockState, player);
      text = Component.m_237115_("tensura.attribute.block.correct_tool.name").getString();
      font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
      textY += 10;
      if (correct) {
         text = "-> " + Component.m_237115_("gui.yes").getString();
      } else {
         text = "-> " + Component.m_237115_("gui.no").getString();
      }

      font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, correct ? Color.GREEN.getRGB() : Color.RED.getRGB());
      textY += 10;
      text = Component.m_237115_("tensura.attribute.block.explosion_resistance.name").getString();
      font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
      textY += 10;
      text = "-> " + block.m_7325_();
      font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
      textY += 10;
      text = Component.m_237115_("tensura.attribute.block.light_level.name").getString();
      font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
      textY += 10;
      text = "-> " + player.f_19853_.m_7146_(blockPos);
      font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
      textY += 10;
      BlockEntity var11 = level.m_7702_(blockPos);
      if (var11 instanceof CharybdisCoreBlockEntity) {
         CharybdisCoreBlockEntity core = (CharybdisCoreBlockEntity)var11;
         double EP = (double)((int)core.getEP());
         text = Component.m_237115_("tensura.attribute.existence_points.name").getString();
         font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
         textY += 10;
         text = "-> " + EP;
         font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
         textY += 10;
      }

      int maxAge;
      if (blockState.m_61138_(BlockStateProperties.f_61426_)) {
         maxAge = (Integer)blockState.m_61143_(BlockStateProperties.f_61426_);
         text = Component.m_237115_("tensura.attribute.block.redstone_strength.name").getString();
         font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
         textY += 10;
         text = "-> " + maxAge;
         font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
         textY += 10;
      }

      if (blockState.m_61138_(BlockStateProperties.f_61416_)) {
         maxAge = (Integer)blockState.m_61143_(BlockStateProperties.f_61416_);
         text = Component.m_237115_("tensura.attribute.block.egg_hatch.name").getString();
         font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
         textY += 10;
         text = String.format("-> %s/%s", maxAge, 2);
         font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
         textY += 10;
      }

      maxAge = getAgeForCrop(blockState, true);
      int age = getAgeForCrop(blockState, false);
      if (maxAge != -1 && age != -1) {
         text = Component.m_237115_("tensura.attribute.block.age.name").getString();
         font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, Color.WHITE.getRGB());
         textY += 10;
         int color;
         if (blockState.m_60713_((net.minecraft.world.level.block.Block)TensuraBlocks.HIPOKUTE_GRASS.get()) && (Integer)blockState.m_61143_(BlockStateProperties.f_61407_) == 2) {
            text = String.format("-> %s", Component.m_237115_("tensura.failed_hipokute_grass").getString());
            color = Color.RED.getRGB();
         } else {
            text = String.format("-> %s/%s", age, maxAge);
            color = maxAge == age ? Color.GREEN.getRGB() : Color.WHITE.getRGB();
         }

         font.m_92883_(poseStack, text, (float)(left + 7), (float)textY, color);
      }

   }

   private static int getAgeForCrop(BlockState state, boolean maxAge) {
      net.minecraft.world.level.block.Block var3 = state.m_60734_();
      if (var3 instanceof CropBlock) {
         CropBlock crop = (CropBlock)var3;
         return maxAge ? crop.m_7419_() : (Integer)state.m_61143_(crop.m_7959_());
      } else {
         Optional<Property<?>> age = state.m_61147_().stream().filter((propertyx) -> {
            return propertyx.m_61708_().equals("age") && propertyx instanceof IntegerProperty;
         }).findFirst();
         if (age.isEmpty()) {
            return -1;
         } else if (maxAge) {
            List<?> property = ((Property)age.get()).m_6908_().stream().toList();
            return (Integer)property.get(property.size() - 1);
         } else {
            return (Integer)state.m_61143_((IntegerProperty)age.get());
         }
      }
   }

   private static int getTexturePositionX(String text, int x) {
      int textWidth = 0;

      for(int i = 0; i < text.length(); ++i) {
         if (i + 1 >= text.length()) {
            textWidth += 5;
         } else {
            textWidth += 6;
         }
      }

      return x + textWidth;
   }

   private static void renderMiscellaneous() {
      int airSupply = Math.max(0, (int)((float)player.m_20146_() / (float)player.m_6062_() * 20.0F));
      int foodLevel = Math.max(0, player.m_36324_().m_38702_());
      int armorValue = player.m_21230_();
      int barrierPoints = (int)player.m_21133_((Attribute)TensuraAttributeRegistry.BARRIER.get());
      boolean hasMount = player.m_20202_() != null;
      if (player.m_21023_(MobEffects.f_19612_)) {
         if (foodLevel == 0) {
            simpleRenderTexture(EMPTY_HUNGER, 64, 34, 9, 9);
         } else {
            simpleRenderTexture(FULL_HUNGER, 64, 34, 9, 9);
         }
      } else if (foodLevel == 0) {
         simpleRenderTexture(EMPTY_FOOD, 64, 34, 9, 9);
      }

      if (armorValue <= 0) {
         simpleRenderTexture(EMPTY_ARMOR, 93, 34, 9, 9);
      }

      if (barrierPoints > 0) {
         renderBarrierPoints(String.valueOf(barrierPoints));
      }

      int posX = leftSideStats ? 62 : screenX - 62;
      int foodPosition = dynamicNumberStringPosition(String.valueOf(foodLevel), posX, 5, leftSideStats)[0];
      posX = leftSideStats ? 91 : screenX - 91;
      int armorPosition = dynamicNumberStringPosition(String.valueOf(armorValue), posX, 5, leftSideStats)[0];
      posX = leftSideStats ? 120 : screenX - 120;
      int airPosition = dynamicNumberStringPosition(String.valueOf(airSupply), posX, 5, leftSideStats)[0];
      if (hasMount) {
         Entity entity = player.m_20202_();
         if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            int healthPoints = (int)Math.ceil((double)living.m_21223_());
            int spiritualHP = (int)Math.ceil(TensuraEPCapability.getSpiritualHealth(living));
            simpleRenderTexture(MOUNT_HEART, 109, 46, 9, 9);
            simpleRenderTexture(MOUNT_SPIRITUAL_HEART, 141, 46, 9, 9);
            posX = leftSideStats ? 107 : screenX - 107;
            int healthPosition = dynamicNumberStringPosition(String.valueOf(healthPoints), posX, 5, leftSideStats)[0];
            posX = leftSideStats ? 139 : screenX - 139;
            int spiritualPosition = dynamicNumberStringPosition(String.valueOf(spiritualHP), posX, 5, leftSideStats)[0];
            font.m_92883_(poseStack, String.valueOf(healthPoints), (float)healthPosition, 47.0F, Color.WHITE.getRGB());
            font.m_92883_(poseStack, String.valueOf(spiritualHP), (float)spiritualPosition, 47.0F, Color.WHITE.getRGB());
         }
      }

      font.m_92883_(poseStack, String.valueOf(foodLevel), (float)foodPosition, 35.0F, Color.WHITE.getRGB());
      font.m_92883_(poseStack, String.valueOf(armorValue), (float)armorPosition, 35.0F, Color.WHITE.getRGB());
      font.m_92883_(poseStack, String.valueOf(airSupply), (float)airPosition, 35.0F, Color.WHITE.getRGB());
   }

   private static void renderBarrierPoints(String points) {
      int textWidth = 0;

      int i;
      for(i = 0; i < points.length(); ++i) {
         if (i + 1 >= points.length()) {
            textWidth += 5;
         } else {
            textWidth += 6;
         }
      }

      i = leftSideStats ? 86 : screenX - 85 - textWidth;
      int iconPos = leftSideStats ? i + textWidth + 2 : i - 11;
      RenderSystem.m_157456_(0, BARRIER);
      RenderSystem.m_69478_();
      RenderSystem.m_69453_();
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      GuiComponent.m_93160_(poseStack, iconPos, 47, 9, 9, 0.0F, 0.0F, 9, 9, 9, 9);
      RenderSystem.m_69461_();
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      font.m_92883_(poseStack, points, (float)i, 47.0F, Color.WHITE.getRGB());
   }

   private static void skillOverlayRendering(ResourceLocation texture, int x, int y, int width, int height, int visibleWidth, float alpha) {
      RenderSystem.m_157456_(0, texture);
      RenderSystem.m_69478_();
      RenderSystem.m_69453_();
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, alpha);
      if (width > 0) {
         GuiComponent.m_93160_(poseStack, x, y, width, height, 0.0F, 0.0F, width, height, width, height);
      } else {
         GuiComponent.m_93160_(poseStack, x + width + visibleWidth, y, -width, height, 0.0F, 0.0F, width, height, -width, height);
      }

      RenderSystem.m_69461_();
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
   }

   private static void simpleRenderTexture(ResourceLocation texture, int x, int y, int width, int height) {
      simpleRenderTexture(texture, x, y, width, height, 1.0F);
   }

   private static void simpleRenderTexture(ResourceLocation texture, int x, int y, int width, int height, float alpha) {
      RenderSystem.m_157456_(0, texture);
      RenderSystem.m_69478_();
      RenderSystem.m_69453_();
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, alpha);
      if (!leftSideStats) {
         x = screenX - x - width;
      }

      GuiComponent.m_93160_(poseStack, x, y, width, height, 0.0F, 0.0F, leftSideStats ? width : -width, height, width, height);
      RenderSystem.m_69461_();
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
   }

   private static void renderSeverance() {
      double severance = TensuraEffectsCapability.getSeverance(player);
      if (!(severance <= 0.0D)) {
         int width = 1 + dynamicResizing(0, 101, severance, (double)player.m_21233_());
         int x = 145 - width;
         int y = 5;
         int height = 9;
         if (!leftSideStats) {
            x = screenX - x - width;
         }

         RenderSystem.m_157456_(0, SEVERANCE_BAR);
         GuiComponent.m_93160_(poseStack, x, y, width, height, 0.0F, 0.0F, width, height, 101, 9);
      }
   }

   private static void renderCutTexture(ResourceLocation texture, int x, int y, int width, int height, int maxWidth, int maxHeight, float alpha) {
      RenderSystem.m_157456_(0, texture);
      RenderSystem.m_69478_();
      RenderSystem.m_69453_();
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, alpha);
      if (!leftSideStats) {
         x = screenX - x - width;
      }

      GuiComponent.m_93160_(poseStack, x, y, width, height, 0.0F, 0.0F, leftSideStats ? width : -width, height, maxWidth, maxHeight);
      RenderSystem.m_69461_();
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
   }

   private static void renderTheCutTexture(ResourceLocation texture, int x, int y, int width, int height, int maxWidth, int maxHeight, float alpha) {
      RenderSystem.m_157456_(0, texture);
      RenderSystem.m_69478_();
      RenderSystem.m_69453_();
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, alpha);
      if (leftSideStats) {
         x = screenX - x - width;
      }

      GuiComponent.m_93160_(poseStack, x, y, width, height, 0.0F, 0.0F, leftSideStats ? width : -width, height, maxWidth, maxHeight);
      RenderSystem.m_69461_();
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
   }

   private static int dynamicResizing(int minSize, int maxSize, double currentValue, double maxValue) {
      return Math.min(Math.max(minSize, (int)((double)maxSize * currentValue / maxValue)), maxSize);
   }

   private static ResourceLocation getHealthBarToRender(boolean spiritual) {
      if (spiritual) {
         if (player.m_21023_((MobEffect)TensuraMobEffects.SOUL_DRAIN.get())) {
            return SOUL_DRAIN_SPIRITUAL_HP_BAR;
         } else {
            return player.m_21023_((MobEffect)TensuraMobEffects.INSANITY.get()) ? INSANITY_SPIRITUAL_HP_BAR : SPIRITUAL_HP_BAR;
         }
      } else if (player.m_21023_((MobEffect)TensuraMobEffects.INFECTION.get())) {
         return INFECTION_HEALTH_BAR;
      } else if (player.m_21023_(MobEffects.f_19615_)) {
         return WITHER_HEALTH_BAR;
      } else if (player.m_21023_((MobEffect)TensuraMobEffects.FATAL_POISON.get())) {
         return FATAL_POISON_HEALTH_BAR;
      } else if (player.m_21023_((MobEffect)TensuraMobEffects.CORROSION.get())) {
         return CORROSION_HEALTH_BAR;
      } else if (player.m_21023_(MobEffects.f_19614_)) {
         return POISON_HEALTH_BAR;
      } else if (!player.m_21023_((MobEffect)TensuraMobEffects.FROST.get()) && !player.m_21023_((MobEffect)TensuraMobEffects.CHILL.get()) && !player.m_146890_()) {
         if (player.m_21023_((MobEffect)TensuraMobEffects.CURSE.get())) {
            return CURSE_HEALTH_BAR;
         } else {
            return player.m_21023_(MobEffects.f_19617_) ? ABSORPTION_HEALTH_BAR : HEALTH_BAR;
         }
      } else {
         return FROZEN_HEALTH_BAR;
      }
   }

   private static int[] dynamicNumberStringPosition(String string, int startValue, int digitWidth, boolean reverse) {
      int[] values = new int[2];
      int text = startValue;
      int texture = startValue;
      int length = string.length();
      int number = reverse ? -digitWidth : digitWidth;
      int i;
      if (reverse) {
         for(i = 0; i < length; ++i) {
            text += number;
            if (i < length - 1) {
               --text;
            }
         }
      } else {
         for(i = 0; i < length; ++i) {
            texture += number;
            if (i < length - 1) {
               ++texture;
            }
         }
      }

      texture += 2;
      values[0] = text;
      values[1] = texture;
      return values;
   }

   private static int[] getCenteredTextPositions(String string, int minWidth, int minHeight, int maxWidth, int maxHeight, int textHeightInPixels) {
      int[] values = new int[2];
      int textWidth = font.m_92895_(string);
      int centerX = minWidth + (maxWidth - minWidth) / 2 - textWidth / 2;
      int centerY = minHeight + (maxHeight - minHeight) / 2 - textHeightInPixels;
      values[0] = centerX;
      values[1] = centerY;
      return values;
   }

   private static void updatePositions() {
      boolean updateStats = false;
      boolean updateSkills = false;
      int x;
      int y;
      if (SettingsScreen.isEditingStatPositions()) {
         x = SettingsScreen.getHudX();
         y = SettingsScreen.getHudY();
      } else {
         x = (Integer)cfg.hudX.get();
         y = (Integer)cfg.hudY.get();
      }

      int x1;
      int y1;
      if (SettingsScreen.isEditingSkillPositions()) {
         x1 = SettingsScreen.getSkillsX();
         y1 = SettingsScreen.getSkillsY();
      } else {
         x1 = (Integer)cfg.skillsX.get();
         y1 = (Integer)cfg.skillsY.get();
      }

      if (x == -1 || y == -1) {
         updateStats = true;
      }

      if (x1 == -1 || y1 == -1 || x1 == -2 || y1 == -2) {
         updateSkills = true;
      }

      if (x == -1) {
         x = 0;
      }

      if (y == -1) {
         y = 0;
      }

      if (x1 == -1) {
         x1 = 0;
      } else if (x1 == -2) {
         x1 = screenX - 41;
      }

      if (y1 == -1) {
         y1 = screenY / 3;
      } else if (y1 == -2) {
         y1 = screenY - 104;
      }

      hudPositions[0] = x;
      hudPositions[1] = y;
      skillOverlayPositions[0] = x1;
      skillOverlayPositions[1] = y1;
      if (updateStats) {
         cfg.hudX.set(x);
         cfg.hudY.set(y);
      }

      if (updateSkills) {
         cfg.skillsX.set(x1);
         cfg.skillsY.set(y1);
      }

   }

   public static int getScreenX() {
      return screenX;
   }

   public static int getScreenY() {
      return screenY;
   }

   public static int getAppraisalOpacity() {
      return appraisalOpacity;
   }

   static {
      appraisalPosX = screenX;
      slot = 0;
      slotSwitch = 0;
   }
}
