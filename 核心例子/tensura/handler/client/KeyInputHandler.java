package com.github.manasmods.tensura.handler.client;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillClientUtils;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.effect.InsanityEffect;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.RequestRaceAndMountPacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestChangeModeButtonPacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestSkillNumberKeyPressPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import net.minecraft.client.Minecraft;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.MovementInputUpdateEvent;
import net.minecraftforge.client.event.InputEvent.Key;
import net.minecraftforge.client.event.InputEvent.MouseScrollingEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE,
   value = {Dist.CLIENT}
)
public class KeyInputHandler {
   @SubscribeEvent
   public static void checkKeybindings(Key e) {
      if (e.getAction() == 1) {
         int key = e.getKey();
         if (key >= 48 && key <= 57) {
            DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> {
               return () -> {
                  Minecraft minecraft = Minecraft.m_91087_();
                  Player player = minecraft.f_91074_;
                  if (player != null) {
                     List<ResourceLocation> packetSkills = new ArrayList();
                     Iterator var4 = SkillAPI.getSkillsFrom(player).getLearnedSkills().iterator();

                     while(var4.hasNext()) {
                        ManasSkillInstance instance = (ManasSkillInstance)var4.next();
                        if (SkillClientUtils.isSkillHeldClient(player, instance.getSkill())) {
                           packetSkills.add(instance.getSkillId());
                        }
                     }

                     int numberKey = key - 48;
                     if (!packetSkills.isEmpty()) {
                        TensuraNetwork.INSTANCE.sendToServer(new RequestSkillNumberKeyPressPacket(packetSkills, numberKey));
                     } else if (TensuraKeybinds.NEXT_MODE_CHANGE.m_90857_()) {
                        TensuraNetwork.INSTANCE.sendToServer(new RequestChangeModeButtonPacket((double)numberKey, false));
                     }
                  }

               };
            });
         }
      }
   }

   @SubscribeEvent
   public static void clientMouseScrolled(MouseScrollingEvent event) {
      Minecraft minecraft = Minecraft.m_91087_();
      Player player = minecraft.f_91074_;
      if (player != null) {
         if (TensuraKeybinds.NEXT_MODE_CHANGE.m_90857_()) {
            TensuraNetwork.INSTANCE.sendToServer(new RequestChangeModeButtonPacket(event.getScrollDelta(), true));
            event.setCanceled(true);
         } else if (minecraft.f_91066_.f_92091_.m_90857_()) {
            Entity entity = player.m_20202_();
            if (entity instanceof ITensuraMount) {
               ITensuraMount mount = (ITensuraMount)entity;
               if (mount.hasScrollAbility()) {
                  TensuraNetwork.INSTANCE.sendToServer(new RequestRaceAndMountPacket(event.getScrollDelta()));
                  event.setCanceled(true);
               }
            }
         }

      }
   }

   @SubscribeEvent
   public static void movementInputUpdateEvent(MovementInputUpdateEvent event) {
      Player player = event.getEntity();
      boolean cannotJump = cannotJump(player);
      if (cannotJump) {
         event.getInput().f_108572_ = false;
         event.getInput().f_108573_ = false;
      }
   }

   private static boolean cannotJump(Player player) {
      if (InsanityEffect.havingNightmare(player)) {
         return true;
      } else if (player.m_21023_((MobEffect)TensuraMobEffects.WEBBED.get())) {
         return true;
      } else if (player.m_21023_((MobEffect)TensuraMobEffects.FROST.get())) {
         return true;
      } else {
         MobEffectInstance paralysis = player.m_21124_((MobEffect)TensuraMobEffects.PARALYSIS.get());
         if (paralysis != null && paralysis.m_19564_() >= 2) {
            return true;
         } else {
            MobEffectInstance effect = player.m_21124_((MobEffect)TensuraMobEffects.MOVEMENT_INTERFERENCE.get());
            if (effect != null && effect.m_19564_() >= 5) {
               return true;
            } else if (player.m_21023_((MobEffect)TensuraMobEffects.LUST_EMBRACEMENT.get())) {
               return true;
            } else if (player.m_21023_((MobEffect)TensuraMobEffects.INFINITE_IMPRISONMENT.get())) {
               return true;
            } else {
               return player.m_21023_((MobEffect)TensuraMobEffects.REST.get()) ? true : player.m_21023_((MobEffect)TensuraMobEffects.SLEEP_MODE.get());
            }
         }
      }
   }
}
