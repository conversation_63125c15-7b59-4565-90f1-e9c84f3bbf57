package com.github.manasmods.tensura.handler;

import com.github.manasmods.tensura.registry.dimensions.TensuraDimensionEffects;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.client.event.RegisterDimensionSpecialEffectsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.MOD
)
public class ServerHandler {
   @SubscribeEvent
   public static void registerDimensionEffects(RegisterDimensionSpecialEffectsEvent event) {
      event.register(dimensionEffectLocation("hell"), TensuraDimensionEffects.HELL);
   }

   private static ResourceLocation dimensionEffectLocation(String dimensionName) {
      return new ResourceLocation("tensura", String.format("%s_effects", dimensionName));
   }
}
