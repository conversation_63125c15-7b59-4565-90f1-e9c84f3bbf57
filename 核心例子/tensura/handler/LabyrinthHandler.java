package com.github.manasmods.tensura.handler;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.ElementalColossusEntity;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.world.savedata.LabyrinthSaveData;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.GameType;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.levelgen.structure.templatesystem.StructurePlaceSettings;
import net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate;
import net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplateManager;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.event.entity.ProjectileImpactEvent;
import net.minecraftforge.event.entity.item.ItemTossEvent;
import net.minecraftforge.event.entity.player.PlayerEvent.PlayerChangedDimensionEvent;
import net.minecraftforge.event.level.LevelEvent.Load;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class LabyrinthHandler {
   @SubscribeEvent
   public static void projectileImpactEvent(ProjectileImpactEvent event) {
      HitResult hitResult = event.getRayTraceResult();
      if (hitResult.m_6662_() == Type.BLOCK) {
         BlockHitResult blockHitResult = (BlockHitResult)hitResult;
         Projectile projectile = event.getProjectile();
         BlockPos location = blockHitResult.m_82425_();
         Level level = projectile.m_9236_();
         if (level.m_8055_(location).m_60734_() == TensuraBlocks.LABYRINTH_BARRIER_BLOCK.get()) {
            projectile.m_146884_(blockHitResult.m_82450_());
            projectile.m_20256_(projectile.m_20184_().m_82548_().m_82490_(0.5D));
            event.setCanceled(true);
         }
      }
   }

   @SubscribeEvent(
      priority = EventPriority.LOWEST
   )
   public static void onThrowPureMagisteel(ItemTossEvent event) {
      Player player = event.getPlayer();
      Level var3 = player.m_9236_();
      if (var3 instanceof ServerLevel) {
         ServerLevel level = (ServerLevel)var3;
         if (level.m_46472_() == TensuraDimensions.LABYRINTH) {
            MinecraftServer server = level.m_7654_();
            LabyrinthSaveData saveData = LabyrinthSaveData.get(server.m_129783_());
            Vec3 pos = saveData.getColossusPos();
            if (!(player.m_20238_(pos) >= 100.0D)) {
               if (!saveData.isHavingColossus()) {
                  ItemEntity item = event.getEntity();
                  ItemStack stack = item.m_32055_();
                  if (stack.m_150930_((Item)TensuraBlocks.Items.PURE_MAGISTEEL_BLOCK.get())) {
                     if (stack.m_41613_() == 1) {
                        item.m_146870_();
                     } else {
                        stack.m_41774_(1);
                     }

                     ElementalColossusEntity colossus = new ElementalColossusEntity(level, pos, MobSpawnType.MOB_SUMMONED);
                     level.m_7967_(colossus);
                     saveData.setHavingColossus(true);
                     SkillHelper.knockBack(colossus, player, 5.0F);
                     TensuraParticleHelper.addServerParticlesAroundSelf(colossus, ParticleTypes.f_123812_);
                     TensuraParticleHelper.addServerParticlesAroundSelf(colossus, ParticleTypes.f_123767_);
                     TensuraParticleHelper.addServerParticlesAroundSelf(colossus, ParticleTypes.f_123767_, 2.0D);
                     colossus.m_9236_().m_6263_((Player)null, colossus.m_20185_(), colossus.m_20186_(), colossus.m_20189_(), SoundEvents.f_12513_, SoundSource.PLAYERS, 2.0F, 1.0F);
                     colossus.m_9236_().m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_12513_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  }
               }
            }
         }
      }
   }

   @SubscribeEvent
   public static void loadLabyrinth(Load event) {
      LevelAccessor var2 = event.getLevel();
      if (var2 instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)var2;
         if (serverLevel.m_46472_() == TensuraDimensions.LABYRINTH) {
            MinecraftServer server = serverLevel.m_7654_();
            LabyrinthSaveData saveData = LabyrinthSaveData.get(server.m_129783_());
            if (!saveData.isLoaded()) {
               ServerLevel level = server.m_129880_(TensuraDimensions.LABYRINTH);
               if (level != null) {
                  StructureTemplateManager manager = level.m_215082_();
                  StructurePlaceSettings settings = new StructurePlaceSettings();
                  List<StructureTemplate> templates = loadTemplates(manager);
                  Iterator var8 = templates.iterator();

                  while(var8.hasNext()) {
                     StructureTemplate template = (StructureTemplate)var8.next();
                     int i = templates.indexOf(template);
                     BlockPos var10000;
                     switch(i) {
                     case 0:
                        var10000 = new BlockPos(26, 41, 9);
                        break;
                     case 1:
                        var10000 = new BlockPos(26, 41, 72);
                        break;
                     case 2:
                        var10000 = new BlockPos(26, 41, 136);
                        break;
                     case 3:
                        var10000 = new BlockPos(26, 41, 199);
                        break;
                     case 4:
                        var10000 = new BlockPos(26, 41, 263);
                        break;
                     case 5:
                        var10000 = new BlockPos(33, -6, 324);
                        break;
                     case 6:
                        var10000 = new BlockPos(33, -6, 369);
                        break;
                     case 7:
                        var10000 = new BlockPos(33, -6, 414);
                        break;
                     case 8:
                        var10000 = new BlockPos(33, -6, 459);
                        break;
                     case 9:
                        var10000 = new BlockPos(1, 77, 505);
                        break;
                     case 10:
                        var10000 = new BlockPos(33, -6, 587);
                        break;
                     case 11:
                        var10000 = new BlockPos(-33, -6, 587);
                        break;
                     case 12:
                        var10000 = new BlockPos(-143, -37, 587);
                        break;
                     case 13:
                        var10000 = new BlockPos(-143, 11, 587);
                        break;
                     case 14:
                        var10000 = new BlockPos(-143, 59, 586);
                        break;
                     case 15:
                        var10000 = new BlockPos(-143, 107, 586);
                        break;
                     case 16:
                        var10000 = new BlockPos(-143, 155, 586);
                        break;
                     case 17:
                        var10000 = new BlockPos(-143, 203, 586);
                        break;
                     default:
                        var10000 = new BlockPos(0, 0, 0);
                     }

                     BlockPos placePos = var10000;
                     template.m_230328_(level, placePos, placePos, settings, level.f_46441_, 3);
                  }

                  saveData.setLoaded(true);
                  ElementalColossusEntity colossus = new ElementalColossusEntity(level, saveData.getColossusPos(), MobSpawnType.NATURAL);
                  if (level.m_7967_(colossus)) {
                     saveData.setHavingColossus(true);
                  }

               }
            }
         }
      }
   }

   @SubscribeEvent
   public static void changedDimension(PlayerChangedDimensionEvent event) {
      Player player = event.getEntity();
      if (!player.m_9236_().m_5776_()) {
         LabyrinthSaveData.removeStartedEntity(player);
         if (event.getFrom() == TensuraDimensions.LABYRINTH) {
            handleGameMode(player, true);
         } else if (event.getTo() == TensuraDimensions.LABYRINTH) {
            handleGameMode(player, false);
         }

      }
   }

   private static List<StructureTemplate> loadTemplates(StructureTemplateManager manager) {
      List<StructureTemplate> templates = new ArrayList();

      for(int i = 0; i < 18; ++i) {
         String var10000;
         switch(i) {
         case 0:
            var10000 = "labyrinth/labyrinth_entrance_hallway_1";
            break;
         case 1:
            var10000 = "labyrinth/labyrinth_entrance_hallway_2";
            break;
         case 2:
            var10000 = "labyrinth/labyrinth_entrance_hallway_3";
            break;
         case 3:
            var10000 = "labyrinth/labyrinth_entrance_hallway_4";
            break;
         case 4:
            var10000 = "labyrinth/labyrinth_entrance_hallway_5";
            break;
         case 5:
            var10000 = "labyrinth/labyrinth_light_hallway_entrance_1";
            break;
         case 6:
            var10000 = "labyrinth/labyrinth_light_hallway_entrance_2";
            break;
         case 7:
            var10000 = "labyrinth/labyrinth_light_hallway_entrance_3";
            break;
         case 8:
            var10000 = "labyrinth/labyrinth_light_hallway_entrance_4";
            break;
         case 9:
            var10000 = "labyrinth/labyrinth_arena";
            break;
         case 10:
            var10000 = "labyrinth/labyrinth_light_hallway_1";
            break;
         case 11:
            var10000 = "labyrinth/labyrinth_light_hallway_2";
            break;
         case 12:
            var10000 = "labyrinth/labyrinth_spirit_dwelling_1";
            break;
         case 13:
            var10000 = "labyrinth/labyrinth_spirit_dwelling_2";
            break;
         case 14:
            var10000 = "labyrinth/labyrinth_spirit_dwelling_3";
            break;
         case 15:
            var10000 = "labyrinth/labyrinth_spirit_dwelling_4";
            break;
         case 16:
            var10000 = "labyrinth/labyrinth_spirit_dwelling_5";
            break;
         case 17:
            var10000 = "labyrinth/labyrinth_spirit_dwelling_6";
            break;
         default:
            var10000 = "";
         }

         String path = var10000;
         ResourceLocation resourceLocation = new ResourceLocation("tensura", path);
         templates.add(manager.m_230359_(resourceLocation));
      }

      return templates;
   }

   public static void handleGameMode(LivingEntity entity, boolean leaving) {
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         GameType current = player.f_8941_.m_9290_();
         if (leaving) {
            if (current != GameType.ADVENTURE) {
               return;
            }

            MinecraftServer server = player.f_19853_.m_7654_();
            if (server == null) {
               player.m_143403_(GameType.f_151492_);
               return;
            }

            GameType type = server.m_130008_();
            if (type.m_46409_() && type != current) {
               player.m_143403_(type);
            } else {
               player.m_143403_(GameType.f_151492_);
            }
         } else {
            if (current != GameType.SURVIVAL) {
               return;
            }

            player.m_143403_(GameType.ADVENTURE);
         }

      }
   }
}
