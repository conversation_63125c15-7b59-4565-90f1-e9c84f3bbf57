package com.github.manasmods.tensura.block;

import com.github.manasmods.tensura.entity.HellCaterpillarEntity;
import com.github.manasmods.tensura.entity.HellMothEntity;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.BlockTags;
import net.minecraft.util.RandomSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ambient.Bat;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.block.state.StateDefinition.Builder;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.properties.IntegerProperty;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.level.gameevent.GameEvent.Context;
import net.minecraft.world.level.material.FluidState;
import net.minecraft.world.phys.shapes.CollisionContext;
import net.minecraft.world.phys.shapes.VoxelShape;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.ForgeEventFactory;
import net.minecraftforge.event.level.BlockEvent.BreakEvent;

public class MothEggBlock extends Block {
   private static final VoxelShape ONE_EGG_AABB = Block.m_49796_(3.0D, 0.0D, 3.0D, 12.0D, 7.0D, 12.0D);
   private static final VoxelShape MULTIPLE_EGGS_AABB = Block.m_49796_(1.0D, 0.0D, 1.0D, 15.0D, 7.0D, 15.0D);
   public static final IntegerProperty HATCH;
   public static final IntegerProperty EGGS;

   public MothEggBlock(Properties pProperties) {
      super(pProperties);
      this.m_49959_((BlockState)((BlockState)((BlockState)this.f_49792_.m_61090_()).m_61124_(HATCH, 0)).m_61124_(EGGS, 1));
   }

   public void m_141947_(Level pLevel, BlockPos pPos, BlockState pState, Entity pEntity) {
      if (!pEntity.m_20161_() && !(pEntity instanceof HellMothEntity) && !(pEntity instanceof HellCaterpillarEntity)) {
         this.destroyEgg(pLevel, pState, pPos, pEntity);
      }

      super.m_141947_(pLevel, pPos, pState, pEntity);
   }

   private void destroyEgg(Level pLevel, BlockState pState, BlockPos pPos, Entity pEntity) {
      if (this.canDestroyEgg(pLevel, pEntity)) {
         if (pLevel.f_46443_ || pLevel.f_46441_.m_188503_(100) != 0 || !pState.m_60713_((Block)TensuraBlocks.MOTH_EGG.get())) {
            if (pEntity instanceof Player) {
               Player player = (Player)pEntity;
               if (player.m_150110_().f_35937_) {
                  return;
               }

               BreakEvent preGrief = new BreakEvent(pLevel, pPos, pState, player);
               if (MinecraftForge.EVENT_BUS.post(preGrief)) {
                  return;
               }
            } else if (ForgeEventFactory.getMobGriefingEvent(pLevel, pEntity)) {
               return;
            }

            this.decreaseEggs(pLevel, pPos, pState);
            Block.m_49840_(pLevel, pPos, ((Item)TensuraMobDropItems.HELL_MOTH_SILK.get()).m_7968_());
         }
      }
   }

   private void decreaseEggs(Level pLevel, BlockPos pPos, BlockState pState) {
      pLevel.m_5594_((Player)null, pPos, SoundEvents.f_12533_, SoundSource.BLOCKS, 0.7F, 0.9F + pLevel.f_46441_.m_188501_() * 0.2F);
      int i = (Integer)pState.m_61143_(EGGS);
      if (i <= 1) {
         pLevel.m_46961_(pPos, false);
      } else {
         pLevel.m_7731_(pPos, (BlockState)pState.m_61124_(EGGS, i - 1), 2);
         pLevel.m_220407_(GameEvent.f_157794_, pPos, Context.m_223722_(pState));
         pLevel.m_46796_(2001, pPos, Block.m_49956_(pState));
      }

   }

   public void m_213898_(BlockState pState, ServerLevel pLevel, BlockPos pPos, RandomSource pRandom) {
      if (this.shouldUpdateHatchLevel(pLevel) && onEggCanPlace(pLevel, pPos)) {
         int i = (Integer)pState.m_61143_(HATCH);
         if (i < 2) {
            pLevel.m_5594_((Player)null, pPos, SoundEvents.f_12534_, SoundSource.BLOCKS, 0.7F, 0.9F + pRandom.m_188501_() * 0.2F);
            pLevel.m_7731_(pPos, (BlockState)pState.m_61124_(HATCH, i + 1), 2);
         } else {
            pLevel.m_5594_((Player)null, pPos, SoundEvents.f_12535_, SoundSource.BLOCKS, 0.7F, 0.9F + pRandom.m_188501_() * 0.2F);
            pLevel.m_7471_(pPos, false);

            for(int j = 0; j < (Integer)pState.m_61143_(EGGS); ++j) {
               pLevel.m_46796_(2001, pPos, Block.m_49956_(pState));
               HellCaterpillarEntity caterpillar = (HellCaterpillarEntity)((EntityType)TensuraEntityTypes.HELL_CATERPILLAR.get()).m_20615_(pLevel);
               if (caterpillar == null) {
                  break;
               }

               caterpillar.m_146762_(-24000);
               caterpillar.m_7678_((double)pPos.m_123341_() + 0.3D + (double)j * 0.2D, (double)pPos.m_123342_(), (double)pPos.m_123343_() + 0.3D, 0.0F, 0.0F);
               caterpillar.m_6518_(pLevel, pLevel.m_6436_(caterpillar.m_20183_()), MobSpawnType.BREEDING, (SpawnGroupData)null, (CompoundTag)null);
               pLevel.m_7967_(caterpillar);
            }
         }
      }

   }

   public static boolean onEggCanPlace(BlockGetter pLevel, BlockPos pPos) {
      return canPlaceEgg(pLevel, pPos.m_7495_());
   }

   public static boolean canPlaceEgg(BlockGetter pReader, BlockPos pPos) {
      return pReader.m_8055_(pPos).m_204336_(BlockTags.f_13035_) || pReader.m_8055_(pPos).m_204336_(BlockTags.f_13089_);
   }

   public void m_6807_(BlockState pState, Level pLevel, BlockPos pPos, BlockState pOldState, boolean pIsMoving) {
      if (onEggCanPlace(pLevel, pPos) && !pLevel.f_46443_) {
         pLevel.m_46796_(2005, pPos, 0);
      }

   }

   private boolean shouldUpdateHatchLevel(Level pLevel) {
      float f = pLevel.m_46942_(1.0F);
      if ((double)f < 0.69D && (double)f > 0.65D) {
         return true;
      } else {
         return pLevel.f_46441_.m_188503_(300) == 0;
      }
   }

   public void m_6240_(Level pLevel, Player pPlayer, BlockPos pPos, BlockState pState, @Nullable BlockEntity pTe, ItemStack pStack) {
      super.m_6240_(pLevel, pPlayer, pPos, pState, pTe, pStack);
      this.decreaseEggs(pLevel, pPos, pState);
   }

   public boolean m_6864_(BlockState pState, BlockPlaceContext pUseContext) {
      return !pUseContext.m_7078_() && pUseContext.m_43722_().m_150930_(this.m_5456_()) && (Integer)pState.m_61143_(EGGS) < 4 || super.m_6864_(pState, pUseContext);
   }

   @Nullable
   public BlockState m_5573_(BlockPlaceContext pContext) {
      FluidState fluidstate = pContext.m_43725_().m_6425_(pContext.m_8083_());
      if (!fluidstate.m_76178_()) {
         return null;
      } else {
         BlockState blockstate = pContext.m_43725_().m_8055_(pContext.m_8083_());
         return blockstate.m_60713_(this) ? (BlockState)blockstate.m_61124_(EGGS, Math.min(4, (Integer)blockstate.m_61143_(EGGS) + 1)) : super.m_5573_(pContext);
      }
   }

   public VoxelShape m_5940_(BlockState pState, BlockGetter pLevel, BlockPos pPos, CollisionContext pContext) {
      return (Integer)pState.m_61143_(EGGS) > 1 ? MULTIPLE_EGGS_AABB : ONE_EGG_AABB;
   }

   protected void m_7926_(Builder<Block, BlockState> pBuilder) {
      pBuilder.m_61104_(new Property[]{HATCH, EGGS});
   }

   private boolean canDestroyEgg(Level pLevel, Entity pEntity) {
      if (pEntity instanceof HellMothEntity) {
         return false;
      } else if (pEntity instanceof HellCaterpillarEntity) {
         return false;
      } else if (pEntity instanceof Bat) {
         return false;
      } else if (!(pEntity instanceof LivingEntity)) {
         return false;
      } else {
         return pEntity instanceof Player || ForgeEventFactory.getMobGriefingEvent(pLevel, pEntity);
      }
   }

   static {
      HATCH = BlockStateProperties.f_61416_;
      EGGS = BlockStateProperties.f_61415_;
   }
}
