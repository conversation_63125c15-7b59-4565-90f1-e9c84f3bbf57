package com.github.manasmods.tensura.integration.jei;

import com.github.manasmods.tensura.data.recipe.GreatSageRefiningRecipe;
import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import mezz.jei.api.constants.VanillaTypes;
import mezz.jei.api.gui.builder.IRecipeLayoutBuilder;
import mezz.jei.api.gui.drawable.IDrawable;
import mezz.jei.api.helpers.IGuiHelper;
import mezz.jei.api.recipe.IFocusGroup;
import mezz.jei.api.recipe.RecipeIngredientRole;
import mezz.jei.api.recipe.RecipeType;
import mezz.jei.api.recipe.category.IRecipeCategory;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.crafting.Ingredient;

public class GreatSageRefiningRecipeCategory implements IRecipeCategory<GreatSageRefiningRecipe> {
   private static final ResourceLocation TEXTURE = new ResourceLocation("tensura", "textures/gui/great_sage/jei_refining.png");
   private static final Component TITLE = Component.m_237115_("tooltip.tensura.great_sage_menu.refining");
   static final ResourceLocation UID = new ResourceLocation("tensura", "refining");
   private final IDrawable background;
   private final IDrawable icon;

   public GreatSageRefiningRecipeCategory(IGuiHelper guiHelper) {
      this.icon = guiHelper.createDrawableIngredient(VanillaTypes.ITEM_STACK, ((Item)TensuraConsumableItems.FULL_POTION.get()).m_7968_());
      this.background = guiHelper.createDrawable(TEXTURE, 0, 0, 249, 80);
   }

   public RecipeType<GreatSageRefiningRecipe> getRecipeType() {
      return TensuraJeiPlugin.REFINING_RECIPE;
   }

   public Component getTitle() {
      return TITLE;
   }

   public IDrawable getBackground() {
      return this.background;
   }

   public IDrawable getIcon() {
      return this.icon;
   }

   public void setRecipe(IRecipeLayoutBuilder builder, GreatSageRefiningRecipe recipe, IFocusGroup focuses) {
      builder.addSlot(RecipeIngredientRole.OUTPUT, 219, 32).addItemStack(recipe.m_8043_());
      builder.addSlot(RecipeIngredientRole.INPUT, 10, 32).addItemStack(recipe.getInputItem());
      List<ItemStack> ingredient1 = new ArrayList(Arrays.stream(((Ingredient)recipe.m_7527_().get(0)).m_43908_()).toList());
      builder.addSlot(RecipeIngredientRole.INPUT, 41, 32).addItemStacks(ingredient1);
      List<ItemStack> ingredient2 = new ArrayList(Arrays.stream(((Ingredient)recipe.m_7527_().get(1)).m_43908_()).toList());
      builder.addSlot(RecipeIngredientRole.INPUT, 69, 32).addItemStacks(ingredient2);
      List<ItemStack> ingredient3 = new ArrayList(Arrays.stream(((Ingredient)recipe.m_7527_().get(2)).m_43908_()).toList());
      builder.addSlot(RecipeIngredientRole.INPUT, 97, 32).addItemStacks(ingredient3);
      List<ItemStack> ingredient4 = new ArrayList(Arrays.stream(((Ingredient)recipe.m_7527_().get(3)).m_43908_()).toList());
      builder.addSlot(RecipeIngredientRole.INPUT, 125, 32).addItemStacks(ingredient4);
      List<ItemStack> ingredient5 = new ArrayList(Arrays.stream(((Ingredient)recipe.m_7527_().get(4)).m_43908_()).toList());
      builder.addSlot(RecipeIngredientRole.INPUT, 153, 32).addItemStacks(ingredient5);
   }
}
