package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.navigator.NoSpinFlightPathNavigator;
import com.github.manasmods.tensura.api.entity.subclass.IElementalSpirit;
import com.github.manasmods.tensura.api.entity.subclass.IFollower;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.projectile.FireBoltProjectile;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Collection;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.AreaEffectCloud;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.control.MoveControl;
import net.minecraft.world.entity.ai.control.MoveControl.Operation;
import net.minecraft.world.entity.ai.goal.FollowBoatGoal;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.navigation.GroundPathNavigation;
import net.minecraft.world.entity.ai.util.LandRandomPos;
import net.minecraft.world.entity.animal.FlyingAnimal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.AxeItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.SwordItem;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.Explosion.BlockInteraction;
import net.minecraft.world.level.pathfinder.BlockPathTypes;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.ForgeEventFactory;
import org.jetbrains.annotations.NotNull;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class SalamanderEntity extends HumanoidNPCEntity implements IAnimatable, IElementalSpirit, FlyingAnimal, IFollower {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Integer> SUMMONING_TICK;
   protected static final EntityDataAccessor<Optional<UUID>> SUMMONER_UUID;
   protected static final EntityDataAccessor<Boolean> FLYING;
   protected static final EntityDataAccessor<Boolean> CAN_SELF_DESTRUCT;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public float prevFlyProgress;
   public float flyProgress;
   protected boolean wasFlying;
   public int timeFlying = 0;
   public int miscAnimationTicks = 0;

   public SalamanderEntity(EntityType<? extends SalamanderEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.m_21441_(BlockPathTypes.LAVA, 0.0F);
      this.m_21441_(BlockPathTypes.WATER, 0.0F);
      this.m_21441_(BlockPathTypes.WATER_BORDER, 0.0F);
      this.switchNavigator(false);
      this.f_21364_ = 40;
      this.f_19793_ = 1.0F;
   }

   protected void switchNavigator(boolean onLand) {
      if (onLand) {
         this.f_21342_ = new MoveControl(this) {
            public void m_8126_() {
               if (!SalamanderEntity.this.isSelfDestruct()) {
                  super.m_8126_();
               }

            }
         };
         this.f_21344_ = new GroundPathNavigation(this, this.f_19853_);
         this.wasFlying = false;
      } else {
         this.f_21342_ = new SalamanderEntity.SalamanderMoveControl() {
            public void m_8126_() {
               if (!SalamanderEntity.this.isSelfDestruct()) {
                  super.m_8126_();
               }

            }
         };
         this.f_21344_ = new NoSpinFlightPathNavigator(this, this.f_19853_);
         this.wasFlying = true;
      }

   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 6.0D).m_22268_(Attributes.f_22276_, 30.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22278_, 0.20000000298023224D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 1.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.NPCMeleeAttackGoal(this, 2.0D, true) {
         public boolean m_8036_() {
            return !super.m_8036_() ? false : SalamanderEntity.this.usingMeleeWeapon();
         }
      });
      this.f_21345_.m_25352_(4, new SalamanderEntity.SalamanderAttackGoal(this));
      this.f_21345_.m_25352_(5, new IElementalSpirit.FollowGreaterSpiritGoal(this, 1.0D, IfritEntity.class));
      this.f_21345_.m_25352_(5, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false));
      this.f_21345_.m_25352_(6, new TamableFollowParentGoal(this, 1.5D));
      this.f_21345_.m_25352_(7, new SalamanderEntity.WalkGoal(this));
      this.f_21345_.m_25352_(7, new FollowBoatGoal(this));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.FlyingWanderAroundPosGoal(this));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{IfritEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(SUMMONING_TICK, -1);
      this.f_19804_.m_135372_(SUMMONER_UUID, Optional.empty());
      this.f_19804_.m_135372_(FLYING, false);
      this.f_19804_.m_135372_(CAN_SELF_DESTRUCT, false);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      if (this.getSummonerUUID() != null) {
         compound.m_128362_("Summoner", this.getSummonerUUID());
      }

      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128405_("SummoningTick", this.getSummoningTick());
      compound.m_128379_("Flying", this.m_29443_());
      compound.m_128379_("CanSelfDestruct", this.canSelfDestruct());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      if (compound.m_128403_("Summoner")) {
         this.setSummonerUUID(compound.m_128342_("Summoner"));
      }

      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setSummoningTick(compound.m_128451_("SummoningTick"));
      this.setFlying(compound.m_128471_("Flying"));
      this.setCanSelfDestruct(compound.m_128471_("CanSelfDestruct"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int tick) {
      this.f_19804_.m_135381_(MISC_ANIMATION, tick);
   }

   public int getSummoningTick() {
      return (Integer)this.f_19804_.m_135370_(SUMMONING_TICK);
   }

   public void setSummoningTick(int tick) {
      this.f_19804_.m_135381_(SUMMONING_TICK, tick);
   }

   @Nullable
   public UUID getSummonerUUID() {
      return (UUID)((Optional)this.f_19804_.m_135370_(SUMMONER_UUID)).orElse((Object)null);
   }

   public void setSummonerUUID(@Nullable UUID pUuid) {
      this.f_19804_.m_135381_(SUMMONER_UUID, Optional.ofNullable(pUuid));
   }

   @Nullable
   public LivingEntity m_21826_() {
      return this.getSummonerUUID() != null ? null : super.m_21826_();
   }

   public boolean m_29443_() {
      return (Boolean)this.f_19804_.m_135370_(FLYING);
   }

   public void setFlying(boolean flying) {
      this.f_19804_.m_135381_(FLYING, flying);
   }

   public boolean canSelfDestruct() {
      return (Boolean)this.f_19804_.m_135370_(CAN_SELF_DESTRUCT);
   }

   public void setCanSelfDestruct(boolean selfDestruct) {
      this.f_19804_.m_135381_(CAN_SELF_DESTRUCT, selfDestruct);
   }

   protected boolean isSelfDestruct() {
      return this.getMiscAnimation() == 3 || this.getMiscAnimation() == 5;
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else {
         if (!this.m_21824_()) {
            Entity var4 = pSource.m_7639_();
            if (var4 instanceof SalamanderEntity) {
               SalamanderEntity salamander = (SalamanderEntity)var4;
               if (!salamander.m_21824_()) {
                  return false;
               }
            }

            var4 = pSource.m_7639_();
            if (var4 instanceof IfritCloneEntity) {
               IfritCloneEntity ifrit = (IfritCloneEntity)var4;
               if (!ifrit.m_21824_()) {
                  return false;
               }
            }

            var4 = pSource.m_7639_();
            if (var4 instanceof IfritEntity) {
               IfritEntity ifrit = (IfritEntity)var4;
               if (!ifrit.m_21824_()) {
                  return false;
               }
            }
         }

         pAmount *= this.getPhysicalAttackInput(pSource);
         return super.m_6469_(pSource, pAmount);
      }
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (this.getSummonerUUID() != null) {
         if (entity instanceof IElementalSpirit) {
            IElementalSpirit spirit = (IElementalSpirit)entity;
            return Objects.equals(spirit.getSummonerUUID(), this.getSummonerUUID());
         } else {
            return Objects.equals(entity.m_20148_(), this.getSummonerUUID());
         }
      } else if (entity instanceof SalamanderEntity) {
         SalamanderEntity salamander = (SalamanderEntity)entity;
         return salamander.m_21824_() == this.m_21824_();
      } else if (entity instanceof IfritEntity) {
         IfritEntity ifrit = (IfritEntity)entity;
         return ifrit.m_21824_() == this.m_21824_();
      } else {
         return false;
      }
   }

   public boolean m_6779_(LivingEntity pTarget) {
      return this.m_7307_(pTarget) ? false : super.m_6779_(pTarget);
   }

   public boolean m_6060_() {
      return TensuraEffectsCapability.hasSyncedEffect(this, (MobEffect)TensuraMobEffects.BLACK_BURN.get());
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      return false;
   }

   public boolean shouldFollow() {
      return !this.m_21827_() && !this.isWandering() && (this.m_5448_() == null || !this.m_5448_().m_6084_());
   }

   public boolean m_7243_(ItemStack pStack) {
      return !this.m_21824_() ? false : super.m_7243_(pStack);
   }

   public boolean canEquipSlots(EquipmentSlot slot) {
      if (!super.canEquipSlots(slot)) {
         return false;
      } else {
         return !slot.m_20743_().equals(Type.ARMOR);
      }
   }

   private boolean usingMeleeWeapon() {
      Item item = this.m_21205_().m_41720_();
      return item instanceof AxeItem ? true : item instanceof SwordItem;
   }

   public void m_8119_() {
      super.m_8119_();
      this.handleFlying();
      this.miscAnimationHandler();
      if (!this.f_19853_.m_5776_()) {
         this.summoningTicking(this);
      } else if (!this.m_20069_()) {
         Vec3 vec3 = this.m_20252_(0.0F);
         float f = Mth.m_14089_(this.f_20883_ * 0.017453292F) * 0.3F;
         float f1 = Mth.m_14031_(this.f_20883_ * 0.017453292F) * 0.3F;
         double yPos = this.m_20188_() + 0.20000000298023224D;
         float angle = 0.017453292F * this.f_20883_;
         float radius = 0.3F;
         double extraX = (double)Mth.m_14031_((float)(3.141592653589793D + (double)angle));
         double extraZ = (double)Mth.m_14089_(angle);
         if (this.m_21825_()) {
            radius = 0.0F;
            f *= 0.8F;
            f1 *= 0.8F;
            yPos -= 0.5D;
         } else if (this.m_20096_()) {
            radius = 0.2F;
         } else if (this.m_20184_().m_82556_() > 0.03D) {
            f *= 0.6F;
            f1 *= 0.6F;
            yPos -= 0.10000000149011612D;
         }

         this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.RED_FIRE.get(), this.m_20185_() - vec3.f_82479_ * 0.1D + (double)f + extraX * (double)radius, yPos, this.m_20189_() - vec3.f_82481_ * 0.1D + (double)f1 + extraZ * (double)radius, 0.0D, 0.0D, 0.0D);
         this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.RED_FIRE.get(), this.m_20185_() - vec3.f_82479_ * 0.1D - (double)f + extraX * (double)radius, yPos, this.m_20189_() - vec3.f_82481_ * 0.1D - (double)f1 + extraZ * (double)radius, 0.0D, 0.0D, 0.0D);
      }

   }

   protected void handleFlying() {
      this.prevFlyProgress = this.flyProgress;
      if (this.m_29443_()) {
         if (this.flyProgress < 5.0F) {
            ++this.flyProgress;
         }
      } else if (this.flyProgress > 0.0F) {
         --this.flyProgress;
      }

      if (!this.f_19853_.m_5776_()) {
         boolean isFlying = this.m_29443_();
         if (isFlying != this.wasFlying) {
            this.switchNavigator(!isFlying);
         }

         if (isFlying) {
            ++this.timeFlying;
            this.m_20242_(true);
            if (this.m_21827_() || this.m_20159_() || this.m_27593_() || this.m_5803_()) {
               this.setFlying(false);
            }
         } else {
            this.timeFlying = 0;
            this.m_20242_(false);
         }
      }

   }

   protected void miscAnimationHandler() {
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         boolean exploding = this.getMiscAnimation() == 3 && this.miscAnimationTicks == 20 || this.getMiscAnimation() == 5 && this.miscAnimationTicks == 30;
         LivingEntity target = this.m_5448_();
         if (exploding && target != null && target.m_20270_(this) <= 5.0F) {
            this.explode();
         }

         if (this.miscAnimationTicks > this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   private int getAnimationTick(int miscAnimation) {
      byte var10000;
      switch(miscAnimation) {
      case 1:
         var10000 = 17;
         break;
      case 2:
         var10000 = 12;
         break;
      case 3:
      default:
         var10000 = 20;
         break;
      case 4:
         var10000 = 40;
         break;
      case 5:
         var10000 = 30;
      }

      return var10000;
   }

   public void m_6504_(@NotNull LivingEntity target, float v) {
      FireBoltProjectile spit = new FireBoltProjectile(this.f_19853_, this);
      spit.setSkill(SkillUtils.getSkillOrNull(this, (ManasSkill)ExtraSkills.FLAME_MANIPULATION.get()));
      float angle = 0.017453292F * this.f_20883_;
      double xOffset = (double)Mth.m_14031_((float)(3.141592653589793D + (double)angle));
      double zOffset = (double)Mth.m_14089_(angle);
      spit.m_7678_(this.m_20185_() + xOffset, this.m_20188_() - 0.2D, this.m_20189_() + zOffset, this.m_146908_(), this.m_146909_());
      double d0 = target.m_20186_() + (double)(target.m_20206_() / 2.0F);
      double d1 = target.m_20185_() - this.m_20185_();
      double d2 = d0 - spit.m_20186_();
      double d3 = target.m_20189_() - this.m_20189_();
      double f = Math.sqrt(d1 * d1 + d3 * d3) * 0.20000000298023224D;
      spit.setDamage((float)(this.m_21133_(Attributes.f_22281_) + 1.0D));
      spit.setBurnTicks(100);
      spit.setSpiritAttack(true);
      spit.m_6686_(d1, d2 + f, d3, v, 0.0F);
      this.f_19853_.m_7967_(spit);
   }

   private void explode() {
      if (!this.f_19853_.f_46443_) {
         BlockInteraction interaction = ForgeEventFactory.getMobGriefingEvent(this.f_19853_, this) ? BlockInteraction.DESTROY : BlockInteraction.NONE;
         this.f_19853_.m_46511_(this, this.m_20185_(), this.m_20186_(), this.m_20189_(), 6.0F, interaction);
         Collection<MobEffectInstance> collection = this.m_21220_();
         if (!collection.isEmpty()) {
            AreaEffectCloud areaeffectcloud = new AreaEffectCloud(this.f_19853_, this.m_20185_(), this.m_20186_(), this.m_20189_());
            areaeffectcloud.m_19712_(2.5F);
            areaeffectcloud.m_19732_(-0.5F);
            areaeffectcloud.m_19740_(10);
            areaeffectcloud.m_19734_(areaeffectcloud.m_19748_() / 2);
            areaeffectcloud.m_19738_(-areaeffectcloud.m_19743_() / (float)areaeffectcloud.m_19748_());
            Iterator var4 = collection.iterator();

            while(var4.hasNext()) {
               MobEffectInstance effectInstance = (MobEffectInstance)var4.next();
               if (!effectInstance.m_19544_().getCurativeItems().isEmpty()) {
                  areaeffectcloud.m_19716_(new MobEffectInstance(effectInstance));
               }
            }

            this.f_19853_.m_7967_(areaeffectcloud);
         }

         DamageSource source = DamageSource.m_19373_((LivingEntity)null).m_19381_().m_19380_().m_238403_().m_19382_();
         this.m_6469_(source, this.m_21233_());
      }
   }

   public void m_7023_(Vec3 vec3d) {
      if (this.m_20069_() && this.m_20184_().f_82480_ > 0.0D) {
         this.m_20256_(this.m_20184_().m_82542_(1.0D, 0.5D, 1.0D));
      }

      super.m_7023_(vec3d);
   }

   public void followEntity(TamableAnimal animal, LivingEntity owner, double followSpeed) {
      if (this.m_20270_(owner) > 5.0F) {
         this.setFlying(true);
         this.m_21566_().m_6849_(owner.m_20185_(), owner.m_20186_() + (double)owner.m_20206_(), owner.m_20189_(), followSpeed);
      } else {
         if (this.f_19861_) {
            this.setFlying(false);
         }

         if (this.m_29443_()) {
            BlockPos vec = this.getGround(this, this.m_20183_());
            this.m_21566_().m_6849_((double)vec.m_123341_(), (double)vec.m_123342_(), (double)vec.m_123343_(), followSpeed);
         } else {
            this.m_21573_().m_5624_(owner, followSpeed);
         }
      }

   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_204117_(TensuraTags.Items.SPIRIT_FOOD);
   }

   public boolean isTamingFood(ItemStack pStack) {
      return pStack.m_150930_((Item)TensuraMaterialItems.ELEMENT_CORE_FIRE.get());
   }

   public void m_21828_(Player pPlayer) {
      super.m_21828_(pPlayer);
      if (this.canSelfDestruct()) {
         this.setCanSelfDestruct(false);
      }

   }

   public MagicElemental getElemental() {
      return MagicElemental.FLAME;
   }

   public SpiritualMagic.SpiritLevel getSpiritLevel() {
      return SpiritualMagic.SpiritLevel.MEDIUM;
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_() || this.isTamingFood(itemstack);
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else {
            if (this.m_21824_()) {
               if (this.convertElementalCore(this, player, hand, (Item)TensuraMaterialItems.ELEMENT_CORE_FIRE.get())) {
                  return InteractionResult.SUCCESS;
               }

               if (!super.m_6071_(player, hand).m_19077_() && this.m_21830_(player)) {
                  this.commanding(player);
                  return InteractionResult.SUCCESS;
               }
            } else if (this.isTamingFood(itemstack)) {
               if (!player.m_7500_()) {
                  itemstack.m_41774_(1);
               }

               if (this.f_19796_.m_188503_(10) == 7 && !ForgeEventFactory.onAnimalTame(this, player)) {
                  this.m_21828_(player);
                  this.f_21344_.m_26573_();
                  this.m_6710_((LivingEntity)null);
                  this.m_21839_(true);
                  this.f_19853_.m_7605_(this, (byte)7);
               } else {
                  if (this.f_19796_.m_188503_(20) == 0) {
                     this.m_6710_(player);
                  }

                  this.f_19853_.m_7605_(this, (byte)6);
               }

               return InteractionResult.SUCCESS;
            }

            return InteractionResult.PASS;
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack) && this.m_21223_() < this.m_21233_()) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         this.m_8035_();
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.m_5634_(3.0F);
      if (this.m_20096_() && !this.m_21566_().m_24995_()) {
         this.setMiscAnimation(4);
      } else {
         this.setMiscAnimation(1);
      }

   }

   public void commanding(Player player) {
      boolean isSelfDestruct = this.canSelfDestruct();
      MutableComponent message;
      if (isSelfDestruct) {
         message = Component.m_237110_("tensura.message.pet.follow", new Object[]{this.m_5446_()});
         this.m_21839_(false);
         this.setWandering(false);
         this.setCanSelfDestruct(false);
      } else if (!this.isWandering() && !this.m_21827_()) {
         message = Component.m_237110_("tensura.message.pet.wander", new Object[]{this.m_5446_()});
         this.m_21839_(false);
         this.m_6710_((LivingEntity)null);
         this.setCanSelfDestruct(false);
         this.setWandering(true);
         this.setWanderPos(player.m_20097_().m_7494_());
      } else if (this.isWandering()) {
         message = Component.m_237110_("tensura.message.pet.stay", new Object[]{this.m_5446_()});
         this.m_21573_().m_26573_();
         this.m_21839_(true);
         this.setWandering(false);
         this.m_6710_((LivingEntity)null);
         this.setCanSelfDestruct(false);
      } else {
         message = Component.m_237110_("tensura.message.pet.self_destruct", new Object[]{this.m_5446_()});
         this.m_21839_(false);
         this.setWandering(false);
         this.m_6710_((LivingEntity)null);
         this.setCanSelfDestruct(true);
      }

      player.m_5661_(message.m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.salamanderSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (!this.m_20096_()) {
         this.setFlying(true);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   protected void m_6668_(DamageSource pDamageSource) {
      if (this.getSummoningTick() >= 0) {
         this.m_5907_();
      } else {
         super.m_6668_(pDamageSource);
      }

   }

   protected void m_7472_(DamageSource pSource, int pLooting, boolean pRecentlyHit) {
      super.m_7472_(pSource, pLooting, pRecentlyHit);
      if (!((double)this.f_19796_.m_188501_() > 0.1D)) {
         this.m_19998_((ItemLike)TensuraMobDropItems.ELEMENTAL_ESSENCE.get());
      }
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_11701_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_11704_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_11703_;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.getMiscAnimation() != 4 && this.getMiscAnimation() != 5) {
         if (this.m_21825_()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.salamander.stay", EDefaultLoopTypes.LOOP));
            return PlayState.CONTINUE;
         } else {
            if (event.isMoving() && !this.m_21525_()) {
               if (this.m_20096_()) {
                  event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.salamander.walk", EDefaultLoopTypes.LOOP));
               } else {
                  event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.salamander.fly", EDefaultLoopTypes.LOOP));
               }
            } else if (!this.m_20096_() && !this.m_21525_()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.salamander.idle_fly", EDefaultLoopTypes.LOOP));
            } else {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.salamander.idle", EDefaultLoopTypes.LOOP));
            }

            return PlayState.CONTINUE;
         }
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.salamander.still", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      }
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.salamander.eat", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.salamander.fire_ball", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.salamander.self_destruct", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.salamander.eat_still", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 5) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.salamander.self_destruct_still", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(SalamanderEntity.class, EntityDataSerializers.f_135028_);
      SUMMONING_TICK = SynchedEntityData.m_135353_(SalamanderEntity.class, EntityDataSerializers.f_135028_);
      SUMMONER_UUID = SynchedEntityData.m_135353_(SalamanderEntity.class, EntityDataSerializers.f_135041_);
      FLYING = SynchedEntityData.m_135353_(SalamanderEntity.class, EntityDataSerializers.f_135035_);
      CAN_SELF_DESTRUCT = SynchedEntityData.m_135353_(SalamanderEntity.class, EntityDataSerializers.f_135035_);
   }

   static class SalamanderAttackGoal extends Goal {
      private final SalamanderEntity salamander;
      private int fireTrailTime = 40;
      private Vec3 startOrbitFrom;
      private int orbitTime;
      private int fireTrailCoolDown;
      private int fireballCoolDown = 40;

      public SalamanderAttackGoal(SalamanderEntity entity) {
         this.m_7021_(EnumSet.of(Flag.MOVE));
         this.salamander = entity;
      }

      public boolean m_8036_() {
         if (this.salamander.m_21827_()) {
            return false;
         } else if (this.salamander.usingMeleeWeapon()) {
            return false;
         } else {
            LivingEntity target = this.salamander.m_5448_();
            if (target != null && target.m_6084_()) {
               this.startOrbitFrom = target.m_146892_();
               return true;
            } else {
               return false;
            }
         }
      }

      private boolean shouldSelfDestruct() {
         if (!this.salamander.m_21824_()) {
            if (this.salamander.m_21223_() >= this.salamander.m_21233_() * 0.1F) {
               return false;
            }

            if (this.salamander.m_21023_((MobEffect)TensuraMobEffects.RAMPAGE.get())) {
               return true;
            }
         }

         return this.salamander.canSelfDestruct();
      }

      public void m_8037_() {
         LivingEntity target = this.salamander.m_5448_();
         if (target != null && target.m_6084_()) {
            double distance = (double)this.salamander.m_20270_(target);
            if (this.shouldSelfDestruct()) {
               this.salamander.m_21573_().m_5624_(target, 1.5D);
               this.salamander.m_7618_(Anchor.EYES, target.m_146892_());
               if (distance <= 5.0D) {
                  if (this.salamander.m_20096_()) {
                     this.salamander.setMiscAnimation(5);
                  } else {
                     this.salamander.setMiscAnimation(3);
                  }

                  this.salamander.f_21344_.m_26573_();
               } else {
                  this.salamander.setFlying(true);
               }

            } else {
               this.salamander.setFlying(true);
               if (this.orbitTime < this.fireTrailCoolDown) {
                  ++this.orbitTime;
                  --this.fireballCoolDown;
                  float zoomIn = 1.0F - (float)this.orbitTime / (float)this.fireTrailCoolDown;
                  Vec3 orbitPos = this.orbitAroundPos(5.5F).m_82520_(0.0D, (double)(4.0F + zoomIn * 3.0F), 0.0D);
                  this.salamander.m_21573_().m_26519_(orbitPos.f_82479_, orbitPos.f_82480_, orbitPos.f_82481_, 1.2D);
                  if (this.salamander.m_29443_() && this.fireballCoolDown <= 15 && distance < 12.0D) {
                     if (this.fireballCoolDown <= 10) {
                        this.salamander.setMiscAnimation(2);
                     }

                     this.salamander.m_7618_(Anchor.EYES, target.m_146892_());
                  } else {
                     this.salamander.m_7618_(Anchor.EYES, orbitPos);
                  }
               } else {
                  this.orbitTime = 0;
                  this.fireTrailTime = 60;
               }

               if (this.salamander.getMiscAnimation() == 2 && this.fireballCoolDown <= 0) {
                  this.salamander.m_7618_(Anchor.EYES, target.m_146892_());
                  this.fireballCoolDown = 40;
                  this.salamander.m_6504_(target, 1.0F);
                  this.salamander.setMiscAnimation(0);
                  this.salamander.f_19853_.m_6263_((Player)null, this.salamander.m_20185_(), this.salamander.m_20186_(), this.salamander.m_20189_(), SoundEvents.f_11705_, SoundSource.NEUTRAL, 1.0F, 1.0F);
               } else if (this.fireTrailTime-- > 0) {
                  this.fireTrail();
                  AABB aabb = new AABB(this.salamander.m_20183_().m_6625_(2), this.salamander.m_20183_().m_6625_(8));
                  List<LivingEntity> list = this.salamander.f_19853_.m_6443_(LivingEntity.class, aabb.m_82400_(2.0D), (entityx) -> {
                     return !entityx.m_7307_(this.salamander) && !this.salamander.m_21830_(entityx);
                  });
                  if (list.isEmpty()) {
                     return;
                  }

                  Iterator var6 = list.iterator();

                  while(var6.hasNext()) {
                     LivingEntity entity = (LivingEntity)var6.next();
                     DamageSource damageSource = DamageSourceHelper.turnTensura(TensuraDamageSources.burn(this.salamander)).setSkill(SkillUtils.getSkillOrNull(this.salamander, (ManasSkill)IntrinsicSkills.FLAME_BREATH.get()));
                     if (entity.m_6469_(damageSource, 4.0F)) {
                        entity.m_20254_(3);
                     }
                  }
               }

            }
         }
      }

      public void m_8056_() {
         this.orbitTime = 0;
         this.fireTrailCoolDown = 60 + this.salamander.m_217043_().m_188503_(80);
      }

      public Vec3 orbitAroundPos(float circleDistance) {
         float angle = 3.0F * (float)(Math.toRadians((double)this.orbitTime) * 3.0D);
         double extraX = (double)(circleDistance * Mth.m_14031_(angle));
         double extraZ = (double)(circleDistance * Mth.m_14089_(angle));
         return this.startOrbitFrom.m_82520_(extraX, 0.0D, extraZ);
      }

      private void fireTrail() {
         Level level = this.salamander.f_19853_;
         float radius = this.salamander.m_20205_();
         double x = this.salamander.m_20185_() + (level.f_46441_.m_188500_() - 0.5D) * (double)radius;
         double y = this.salamander.m_20186_() - 1.0D + (level.f_46441_.m_188500_() - 0.5D) * (double)radius * 0.75D;
         double z = this.salamander.m_20189_() + (level.f_46441_.m_188500_() - 0.5D) * (double)radius;

         for(int i = 0; i < 6; ++i) {
            for(int j = 0; j < 5; ++j) {
               double newX = x + this.salamander.m_217043_().m_188583_() / 2.0D;
               double newY = y + this.salamander.m_217043_().m_188583_() / 2.0D;
               double newZ = z + this.salamander.m_217043_().m_188583_() / 2.0D;
               TensuraParticleHelper.spawnServerParticles(level, (ParticleOptions)TensuraParticles.RED_FIRE.get(), newX, newY - (double)i, newZ, 0, 0.0D, -0.1D, 0.0D, 0.0D, false);
            }
         }

      }
   }

   public class WalkGoal extends Goal {
      protected final SalamanderEntity entity;
      protected double x;
      protected double y;
      protected double z;
      private boolean flightTarget = false;

      public WalkGoal(SalamanderEntity entity) {
         this.m_7021_(EnumSet.of(Flag.MOVE));
         this.entity = entity;
      }

      public boolean m_8036_() {
         if (!this.entity.m_20160_() && (this.entity.m_5448_() == null || !this.entity.m_5448_().m_6084_()) && !this.entity.m_20159_() && !this.entity.m_21827_()) {
            if (this.entity.m_217043_().m_188503_(30) != 0 && !this.entity.m_29443_()) {
               return false;
            } else {
               if (this.entity.m_20096_()) {
                  this.flightTarget = SalamanderEntity.this.f_19796_.m_188499_();
               } else {
                  this.flightTarget = SalamanderEntity.this.f_19796_.m_188503_(5) > 0 && this.entity.timeFlying < 200;
               }

               Vec3 position = this.getPosition();
               if (position == null) {
                  return false;
               } else {
                  this.x = position.f_82479_;
                  this.y = position.f_82480_;
                  this.z = position.f_82481_;
                  return true;
               }
            }
         } else {
            return false;
         }
      }

      public void m_8037_() {
         if (this.flightTarget) {
            this.entity.m_21566_().m_6849_(this.x, this.y, this.z, 1.0D);
         } else {
            this.entity.m_21573_().m_26519_(this.x, this.y, this.z, 1.0D);
            if (SalamanderEntity.this.m_29443_() && this.entity.f_19861_) {
               this.entity.setFlying(false);
            }
         }

         if (SalamanderEntity.this.m_29443_() && this.entity.f_19861_ && this.entity.timeFlying > 10) {
            this.entity.setFlying(false);
         }

      }

      @Nullable
      protected Vec3 getPosition() {
         Vec3 vector3d = this.entity.m_20182_();
         if (SalamanderEntity.this.isOverWater(this.entity)) {
            this.flightTarget = true;
         }

         Vec3 pos = Vec3.m_82512_(this.entity.getWanderPos());
         double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
         if (this.entity.isWandering() && this.entity.m_20238_(pos) >= distance * distance) {
            return pos;
         } else if (this.flightTarget) {
            return this.entity.timeFlying < 50 ? SalamanderEntity.this.getBlockInViewAway(this.entity, vector3d, 0.0F) : SalamanderEntity.this.getBlockGrounding(this.entity, vector3d);
         } else {
            return LandRandomPos.m_148488_(this.entity, 10, 7);
         }
      }

      public boolean m_8045_() {
         if (this.entity.m_21827_()) {
            return false;
         } else if (this.flightTarget) {
            return this.entity.m_29443_() && this.entity.m_20275_(this.x, this.y, this.z) > 2.0D;
         } else {
            return !this.entity.m_21573_().m_26571_() && !this.entity.m_20160_();
         }
      }

      public void m_8056_() {
         if (this.flightTarget) {
            this.entity.setFlying(true);
            this.entity.m_21566_().m_6849_(this.x, this.y, this.z, 1.0D);
         } else {
            this.entity.m_21573_().m_26519_(this.x, this.y, this.z, 1.0D);
         }

      }

      public void m_8041_() {
         this.entity.m_21573_().m_26573_();
         super.m_8041_();
      }
   }

   class SalamanderMoveControl extends MoveControl {
      private final Mob parentEntity = SalamanderEntity.this;

      public SalamanderMoveControl() {
         super(SalamanderEntity.this);
      }

      public void m_8126_() {
         if (this.f_24981_ == Operation.MOVE_TO) {
            Vec3 vector3d = new Vec3(this.f_24975_ - this.parentEntity.m_20185_(), this.f_24976_ - this.parentEntity.m_20186_(), this.f_24977_ - this.parentEntity.m_20189_());
            double d0 = vector3d.m_82553_();
            double width = this.parentEntity.m_20191_().m_82309_();
            Vec3 vector3d1 = vector3d.m_82490_(this.f_24978_ * 0.05D / d0);
            this.parentEntity.m_20256_(this.parentEntity.m_20184_().m_82549_(vector3d1).m_82490_(0.95D).m_82520_(0.0D, -0.01D, 0.0D));
            if (d0 < width) {
               this.f_24981_ = Operation.WAIT;
            } else if (d0 >= width) {
               float yaw = -((float)Mth.m_14136_(vector3d1.f_82479_, vector3d1.f_82481_)) * 57.295776F;
               this.parentEntity.m_146922_(Mth.m_14148_(this.parentEntity.m_146908_(), yaw, 8.0F));
            }
         }

      }
   }
}
