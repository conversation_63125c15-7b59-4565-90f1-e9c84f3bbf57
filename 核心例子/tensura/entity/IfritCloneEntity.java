package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.ai.UndergroundTargetingEntitiesGoal;
import com.github.manasmods.tensura.api.entity.navigator.NoSpinFlightPathNavigator;
import com.github.manasmods.tensura.api.entity.subclass.IFollower;
import com.github.manasmods.tensura.api.entity.subclass.IGiantMob;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierEntity;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierPart;
import com.github.manasmods.tensura.entity.magic.barrier.FlareCircleEntity;
import com.github.manasmods.tensura.entity.magic.skill.FlameOrbProjectile;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.magic.SpiritualMagics;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.FluidTags;
import net.minecraft.util.Mth;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.control.MoveControl;
import net.minecraft.world.entity.ai.control.MoveControl.Operation;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.navigation.GroundPathNavigation;
import net.minecraft.world.entity.ai.targeting.TargetingConditions;
import net.minecraft.world.entity.ai.util.LandRandomPos;
import net.minecraft.world.entity.animal.FlyingAnimal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.vehicle.AbstractMinecart;
import net.minecraft.world.entity.vehicle.Boat;
import net.minecraft.world.item.AxeItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.SwordItem;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.pathfinder.BlockPathTypes;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.ForgeEventFactory;
import net.minecraftforge.network.PacketDistributor;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class IfritCloneEntity extends HumanoidNPCEntity implements IAnimatable, FlyingAnimal, IFollower, IGiantMob {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   protected static final EntityDataAccessor<Boolean> FLYING;
   protected static final EntityDataAccessor<BlockPos> FLARE_POS;
   private static final EntityDataAccessor<Integer> FLARE_ID;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public float prevFlyProgress;
   public float flyProgress;
   protected boolean wasFlying;
   public int timeFlying = 0;
   public int miscAnimationTicks = 0;

   public IfritCloneEntity(EntityType<? extends IfritCloneEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.m_21441_(BlockPathTypes.LAVA, 0.0F);
      this.m_21441_(BlockPathTypes.WATER, 0.0F);
      this.m_21441_(BlockPathTypes.WATER_BORDER, 0.0F);
      this.switchNavigator(false);
      this.f_19793_ = 2.0F;
   }

   protected void switchNavigator(boolean onLand) {
      if (!onLand && !this.m_5803_()) {
         this.f_21342_ = new IfritCloneEntity.IfritMoveControl();
         this.f_21344_ = new NoSpinFlightPathNavigator(this, this.f_19853_);
         this.wasFlying = true;
      } else {
         this.f_21342_ = new TensuraTamableEntity.SleepMoveControl() {
            public void m_8126_() {
               if (IfritCloneEntity.this.getMiscAnimation() < 5) {
                  super.m_8126_();
               }
            }
         };
         this.f_21344_ = new GroundPathNavigation(this, this.f_19853_);
         this.wasFlying = false;
      }

   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 5.0D).m_22268_(Attributes.f_22281_, 10.0D).m_22268_(Attributes.f_22276_, 50.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 64.0D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 3.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.NPCMeleeAttackGoal(this, 2.0D, true) {
         public boolean m_8036_() {
            return !super.m_8036_() ? false : IfritCloneEntity.this.usingMeleeWeapon();
         }
      });
      this.f_21345_.m_25352_(4, new IfritCloneEntity.IfritAttackGoal(this));
      this.f_21345_.m_25352_(5, new IfritCloneEntity.FollowIfritGoal(this, 1.0D));
      this.f_21345_.m_25352_(5, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false));
      this.f_21345_.m_25352_(6, new TamableFollowParentGoal(this, 1.5D));
      this.f_21345_.m_25352_(7, new IfritCloneEntity.WalkGoal(this));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.FlyingWanderAroundPosGoal(this));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{IfritEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new UndergroundTargetingEntitiesGoal(this, LivingEntity.class, false, 8.0F, this::shouldAttack));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   public boolean shouldAttack(LivingEntity entity) {
      if (entity == this) {
         return false;
      } else if (!entity.m_6084_()) {
         return false;
      } else if (this.m_21826_() != null) {
         if (entity instanceof Mob) {
            Mob mob = (Mob)entity;
            return mob.m_5448_() == this.m_21826_();
         } else {
            return this.m_21826_().m_21214_() == entity || this.m_21826_().m_21188_() == entity;
         }
      } else {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (!player.m_7500_() && !player.m_5833_()) {
               return true;
            }
         }

         boolean var10000;
         if (entity instanceof SalamanderEntity) {
            SalamanderEntity salamander = (SalamanderEntity)entity;
            if (!salamander.m_21824_()) {
               var10000 = true;
               return !var10000;
            }
         }

         var10000 = false;
         return !var10000;
      }
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(FLYING, false);
      this.f_19804_.m_135372_(FLARE_ID, 0);
      this.f_19804_.m_135372_(FLARE_POS, BlockPos.f_121853_);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128379_("Flying", this.m_29443_());
      compound.m_128405_("FlareID", this.getFlareID());
      compound.m_128347_("FlareX", (double)this.getFlarePos().m_123341_());
      compound.m_128347_("FlareY", (double)this.getFlarePos().m_123342_());
      compound.m_128347_("FlareZ", (double)this.getFlarePos().m_123343_());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setFlying(compound.m_128471_("Flying"));
      this.setFlareID(compound.m_128451_("FlareID"));
      this.setFlarePos(compound.m_128459_("FlareX"), compound.m_128459_("FlareY"), compound.m_128459_("FlareZ"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int tick) {
      this.f_19804_.m_135381_(MISC_ANIMATION, tick);
   }

   public boolean m_29443_() {
      return (Boolean)this.f_19804_.m_135370_(FLYING);
   }

   public void setFlying(boolean flying) {
      this.f_19804_.m_135381_(FLYING, flying);
   }

   public int getFlareID() {
      return (Integer)this.f_19804_.m_135370_(FLARE_ID);
   }

   public void setFlareID(int id) {
      this.f_19804_.m_135381_(FLARE_ID, id);
   }

   public BlockPos getFlarePos() {
      return (BlockPos)this.f_19804_.m_135370_(FLARE_POS);
   }

   public void setFlarePos(double x, double y, double z) {
      this.f_19804_.m_135381_(FLARE_POS, new BlockPos(x, y, z));
   }

   public void m_21839_(boolean pOrderedToSit) {
      super.m_21839_(pOrderedToSit);
      if (pOrderedToSit && this.m_217043_().m_188503_(4) == 1) {
         this.setMiscAnimation(-1);
      }

   }

   public boolean m_6060_() {
      return TensuraEffectsCapability.hasSyncedEffect(this, (MobEffect)TensuraMobEffects.BLACK_BURN.get());
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      return false;
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      return this.m_5803_() ? entitydimensions.m_20390_(1.0F, 2.0F) : entitydimensions;
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else {
         if (!this.m_21824_()) {
            Entity var4 = pSource.m_7639_();
            if (var4 instanceof SalamanderEntity) {
               SalamanderEntity salamander = (SalamanderEntity)var4;
               if (!salamander.m_21824_()) {
                  return false;
               }
            }

            var4 = pSource.m_7639_();
            if (var4 instanceof IfritCloneEntity) {
               IfritCloneEntity ifrit = (IfritCloneEntity)var4;
               if (!ifrit.m_21824_()) {
                  return false;
               }
            }

            var4 = pSource.m_7639_();
            if (var4 instanceof IfritEntity) {
               IfritEntity ifrit = (IfritEntity)var4;
               if (!ifrit.m_21824_()) {
                  return false;
               }
            }
         }

         boolean var10000;
         label43: {
            Entity var5 = pSource.m_7640_();
            if (var5 instanceof LivingEntity) {
               LivingEntity living = (LivingEntity)var5;
               if (RaceHelper.isSpiritual(living)) {
                  var10000 = true;
                  break label43;
               }
            }

            var10000 = false;
         }

         boolean spiritualAttacker = var10000;
         if (DamageSourceHelper.isPhysicalAttack(pSource) && !spiritualAttacker) {
            pAmount *= 0.05F;
         }

         if (IfritEntity.shouldEvaporateProjectile(pSource.m_7640_())) {
            this.m_5496_(SoundEvents.f_12031_, 1.0F, 0.8F);
            pSource.m_7640_().m_142687_(RemovalReason.KILLED);
            return false;
         } else {
            return super.m_6469_(pSource, pAmount);
         }
      }
   }

   public boolean m_6779_(LivingEntity pTarget) {
      if (!this.m_21824_()) {
         if (pTarget instanceof SalamanderEntity) {
            SalamanderEntity salamander = (SalamanderEntity)pTarget;
            if (!salamander.m_21824_()) {
               return false;
            }
         }

         if (pTarget instanceof IfritCloneEntity) {
            IfritCloneEntity ifrit = (IfritCloneEntity)pTarget;
            if (!ifrit.m_21824_()) {
               return false;
            }
         }

         if (pTarget instanceof IfritEntity) {
            IfritEntity ifrit = (IfritEntity)pTarget;
            if (!ifrit.m_21824_()) {
               return false;
            }
         }
      }

      return super.m_6779_(pTarget);
   }

   public void copySkills(LivingEntity owner) {
      this.m_6593_(owner.m_7755_());
      Iterator var2 = owner.m_21220_().iterator();

      while(var2.hasNext()) {
         MobEffectInstance instance = (MobEffectInstance)var2.next();
         this.m_7292_(instance);
      }

      List<ManasSkillInstance> list = List.copyOf(SkillAPI.getSkillsFrom(owner).getLearnedSkills());
      Iterator var7 = list.iterator();

      while(var7.hasNext()) {
         ManasSkillInstance instance = (ManasSkillInstance)var7.next();
         ManasSkillInstance skillInstance = TensuraSkillInstance.fromNBT(instance.toNBT());
         skillInstance.setToggled(true);
         SkillAPI.getSkillsFrom(this).learnSkill(skillInstance);
      }

   }

   public boolean shouldFollow() {
      return !this.m_21827_() && !this.isWandering() && (this.m_5448_() == null || !this.m_5448_().m_6084_());
   }

   public boolean shouldSwim() {
      return false;
   }

   public boolean m_7243_(ItemStack pStack) {
      return false;
   }

   public boolean canEquipSlots(EquipmentSlot slot) {
      if (!super.canEquipSlots(slot)) {
         return false;
      } else {
         return !slot.m_20743_().equals(Type.ARMOR);
      }
   }

   private boolean usingMeleeWeapon() {
      Item item = this.m_21205_().m_41720_();
      return item instanceof AxeItem ? true : item instanceof SwordItem;
   }

   protected void m_8024_() {
      super.m_8024_();
      if (this.isColliding(this, false)) {
         if (this.f_19797_ % 20 == 0) {
            List<BarrierPart> list = this.f_19853_.m_45976_(BarrierPart.class, this.m_20191_().m_82400_(1.0D));
            if (!list.isEmpty()) {
               Iterator var2 = list.iterator();

               while(var2.hasNext()) {
                  BarrierPart barrier = (BarrierPart)var2.next();
                  this.m_7327_(barrier);
               }
            }
         }

         if (!this.m_21824_() && this.m_29443_() && this.m_5448_() != null) {
            this.breakBlocks(this, 2.0F, true, 0, (SimpleContainer)null, true);
         }
      }

      if (this.m_20072_() && this.f_19797_ % 10 == 0 && this.removeFluid(this, FluidTags.f_13131_, 3.0F, true, 0)) {
         this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12031_, SoundSource.NEUTRAL, 1.0F, 1.0F);
      }

   }

   public boolean breakableBlocks(LivingEntity entity, BlockPos pos, BlockState state) {
      return state.m_204336_(TensuraTags.Blocks.BOSS_IMMUNE) ? false : ForgeEventFactory.onEntityDestroyBlock(entity, pos, state);
   }

   public boolean dropBlockLoot(LivingEntity entity, BlockState state) {
      return !state.m_204336_(TensuraTags.Blocks.SKILL_BREAK_EASY);
   }

   public void m_8119_() {
      super.m_8119_();
      this.handleFlying();
      this.miscAnimationHandler();
      if (!this.f_19853_.m_5776_()) {
         if (this.m_20202_() instanceof Boat || this.m_20202_() instanceof AbstractMinecart) {
            this.m_20202_().m_6469_(DamageSource.m_19370_(this), 40.0F);
         }

         if (this.f_19797_ % 100 == 0) {
            List<IfritEntity> list = this.f_19853_.m_45971_(IfritEntity.class, TargetingConditions.m_148353_().m_26883_(32.0D).m_148355_().m_26893_(), this, this.m_20191_().m_82400_(32.0D));
            if (list.isEmpty()) {
               this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
               TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123756_, 1.0D);
               TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.RED_FIRE.get(), 2.0D);
               this.m_146870_();
            }
         }
      } else if (this.m_6084_()) {
         if (this.f_19797_ % 10 == 0) {
            TensuraParticleHelper.addParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.RED_FIRE.get(), 2.0D);
         }

         Vec3 vec3 = this.m_20252_(1.0F).m_82541_();
         float radius = -0.15F;
         double yPos = this.m_20188_() + 0.20000000298023224D;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)Mth.m_14031_((float)(3.141592653589793D + (double)angle));
         double extraZ = (double)Mth.m_14089_(angle);
         if (this.m_5803_()) {
            yPos -= 0.20000000298023224D;
            radius = -0.5F;
         } else if (this.getMiscAnimation() == -1) {
            --yPos;
            radius = 0.6F;
         } else if (this.getMiscAnimation() == 7) {
            radius = -0.3F;
            yPos -= 0.10000000149011612D;
         } else if (this.m_20184_().m_82556_() > 0.03D && this.getMiscAnimation() < 5 && this.getMiscAnimation() != 3) {
            if (!this.m_20096_()) {
               yPos -= 0.20000000298023224D;
               radius = 0.3F;
            } else if (this.m_21660_()) {
               radius = 0.0F;
            }
         }

         for(int i = 0; i < 15; ++i) {
            double ox = Math.random() * 0.3D - 0.15D;
            double oy = Math.random() * 0.3D - 0.15D;
            double oz = Math.random() * 0.3D - 0.15D;
            Vec3 randomVec = (new Vec3(Math.random() - 0.5D, Math.random() - 0.5D, Math.random() - 0.5D)).m_82541_();
            Vec3 result = vec3.m_82490_(-1.0D).m_82549_(randomVec).m_82541_().m_82490_(0.1D);
            this.m_9236_().m_7106_((ParticleOptions)TensuraParticles.HEAT_EFFECT.get(), this.m_20185_() - vec3.f_82479_ * 0.1D + ox + extraX * (double)radius, yPos + oy, this.m_20189_() - vec3.f_82481_ * 0.1D + oz + extraZ * (double)radius, result.f_82479_, result.f_82480_, result.f_82481_);
            this.m_9236_().m_7106_(ParticleTypes.f_123744_, this.m_20185_() - vec3.f_82479_ * 0.1D + ox + extraX * (double)radius, yPos + oy, this.m_20189_() - vec3.f_82481_ * 0.1D + oz + extraZ * (double)radius, result.f_82479_, result.f_82480_, result.f_82481_);
         }
      }

   }

   protected void handleFlying() {
      this.prevFlyProgress = this.flyProgress;
      if (this.m_29443_()) {
         if (this.flyProgress < 5.0F) {
            ++this.flyProgress;
         }
      } else if (this.flyProgress > 0.0F) {
         --this.flyProgress;
      }

      if (!this.f_19853_.m_5776_()) {
         boolean isFlying = this.m_29443_();
         if (isFlying != this.wasFlying) {
            this.switchNavigator(!isFlying);
         }

         if (isFlying) {
            ++this.timeFlying;
            this.m_20242_(true);
            if (this.m_21827_() || this.m_20159_() || this.m_27593_() || this.m_5803_()) {
               this.setFlying(false);
            }
         } else {
            this.timeFlying = 0;
            this.m_20242_(false);
         }
      }

   }

   protected void miscAnimationHandler() {
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (!this.m_9236_().m_5776_()) {
            LivingEntity target;
            if ((this.getMiscAnimation() == 1 || this.getMiscAnimation() == 2) && this.miscAnimationTicks == 10) {
               target = this.m_5448_();
               if (target != null) {
                  this.m_7618_(Anchor.EYES, target.m_146892_());
               }

               IfritEntity.shootFireBolt(this);
               this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11705_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            } else if (this.getMiscAnimation() == 3) {
               this.m_21573_().m_26573_();
               if (this.miscAnimationTicks >= 10 && this.miscAnimationTicks <= 25) {
                  this.flameOrb();
               }

               target = this.m_5448_();
               if (target != null) {
                  this.m_7618_(Anchor.EYES, target.m_146892_());
               }

               this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11705_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            } else if (this.getMiscAnimation() == 4 && this.miscAnimationTicks == 25) {
               IfritEntity.hellFire(this);
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
            } else if (this.getMiscAnimation() == 5) {
               this.m_21573_().m_26573_();
               if (this.miscAnimationTicks == 1) {
                  LivingEntity target = this.m_5448_();
                  Vec3 pos;
                  if (target != null) {
                     pos = target.m_20182_();
                  } else {
                     BlockHitResult result = SkillHelper.getPlayerPOVHitResult(this.f_19853_, this, Fluid.NONE, 20.0D);
                     pos = result.m_82450_();
                  }

                  this.setFlarePos(pos.m_7096_(), pos.m_7098_(), pos.m_7094_());
                  this.setFlareID(0);
               } else {
                  this.flareCircle(this.miscAnimationTicks);
               }
            } else if (this.getMiscAnimation() == 6) {
               this.m_21573_().m_26573_();
               if (this.miscAnimationTicks == 15) {
                  IfritEntity.combust(this, this::shouldAttack);
                  this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11913_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }
         }

         if (this.miscAnimationTicks > this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   private int getAnimationTick(int miscAnimation) {
      short var10000;
      switch(miscAnimation) {
      case -1:
         var10000 = 200;
         break;
      case 0:
      case 1:
      case 2:
      default:
         var10000 = 15;
         break;
      case 3:
      case 7:
      case 8:
         var10000 = 40;
         break;
      case 4:
      case 6:
         var10000 = 30;
         break;
      case 5:
         var10000 = 65;
      }

      return var10000;
   }

   private void flameOrb() {
      int orbID = this.getFlareID();
      if (orbID == 0) {
         FlameOrbProjectile orb = new FlameOrbProjectile(this.f_19853_, this);
         orb.setDamage((float)(this.m_21133_(Attributes.f_22281_) * 3.0D));
         orb.setBurnTicks(100);
         orb.setSpiritAttack(true);
         orb.setMpCost(500.0D);
         orb.setSkill((ManasSkillInstance)SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)ExtraSkills.FLAME_MANIPULATION.get()).orElse((Object)null));
         orb.setExplosionRadius(4.0F);
         orb.m_146884_(this.m_146892_().m_82520_(0.0D, 4.0D, 0.0D));
         orb.setOwnerOffset(new Vec3(0.0D, 4.0D, 0.0D));
         orb.setLookDistance(30.0F);
         orb.setDelayTick(15);
         orb.m_20242_(true);
         this.m_9236_().m_7967_(orb);
         this.setFlareID(orb.m_19879_());
      } else {
         Entity entity = this.m_9236_().m_6815_(orbID);
         if (entity instanceof FlameOrbProjectile) {
            FlameOrbProjectile orb = (FlameOrbProjectile)entity;
            if (orb.getDelayTick() > 0) {
               orb.setSize(orb.getSize() + 0.1F);
            }
         } else {
            this.setFlareID(0);
            this.flameOrb();
         }
      }

      this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }

   private void flareCircle(int heldTicks) {
      BlockPos pos = this.getFlarePos();
      if (heldTicks >= 20) {
         int flareID = this.getFlareID();
         if (flareID == 0) {
            FlareCircleEntity barrier = new FlareCircleEntity((EntityType)TensuraEntityTypes.FLARE_CIRCLE.get(), this.f_19853_);
            barrier.m_5602_(this);
            barrier.setDamage((float)(this.m_21133_(Attributes.f_22281_) * 2.0D));
            barrier.setRadius(5.0F);
            barrier.setHeight(7.0F);
            barrier.setLife(30);
            barrier.setHealth(200.0F);
            barrier.m_146884_(new Vec3((double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_()));
            barrier.setMpCost(2000.0D);
            barrier.setSkill((ManasSkillInstance)SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)SpiritualMagics.FLARE_CIRCLE.get()).orElse((Object)null));
            this.m_9236_().m_7967_(barrier);
            this.setFlareID(barrier.m_19879_());
         } else {
            Entity entity = this.m_9236_().m_6815_(flareID);
            if (entity instanceof BarrierEntity) {
               BarrierEntity barrier = (BarrierEntity)entity;
               barrier.increaseLife(2);
            } else {
               this.setFlareID(0);
            }
         }

         this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
      } else {
         TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
            return this;
         }), new RequestFxSpawningPacket(new ResourceLocation("tensura:flare_circle_circle"), pos, 0.0D, 0.0D, 0.0D, 0, true));
      }

   }

   public void m_7023_(Vec3 vec3d) {
      if (this.m_20069_() && this.m_20184_().f_82480_ > 0.0D) {
         this.m_20256_(this.m_20184_().m_82542_(1.0D, 0.5D, 1.0D));
      }

      super.m_7023_(vec3d);
   }

   public void followEntity(TamableAnimal animal, LivingEntity owner, double followSpeed) {
      if (this.m_20270_(owner) > 5.0F) {
         this.setFlying(true);
         this.m_21566_().m_6849_(owner.m_20185_(), owner.m_20186_() + (double)owner.m_20206_(), owner.m_20189_(), followSpeed);
      } else {
         if (this.f_19861_) {
            this.setFlying(false);
         }

         if (this.m_29443_()) {
            BlockPos vec = this.getGround(this, this.m_20183_());
            this.m_21566_().m_6849_((double)vec.m_123341_(), (double)vec.m_123342_(), (double)vec.m_123343_(), followSpeed);
         } else {
            this.m_21573_().m_5624_(owner, followSpeed);
         }
      }

   }

   public void m_6667_(DamageSource source) {
      super.m_6667_(source);
      if (!this.m_6084_()) {
         TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.RED_FIRE.get());
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.HEAT_EFFECT.get(), 2.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_, 2.0D);
      }
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_11701_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_11704_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_11703_;
   }

   public boolean shouldStand() {
      if (this.getMiscAnimation() == 3) {
         return true;
      } else {
         return this.getMiscAnimation() >= 5 ? true : this.m_20096_();
      }
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_5803_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.sleep", EDefaultLoopTypes.LOOP));
      } else if (this.m_21825_() && this.getMiscAnimation() == -1) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.idle_train", EDefaultLoopTypes.LOOP));
      } else if (event.isMoving() && this.getMiscAnimation() < 5 && this.getMiscAnimation() != 3) {
         if (this.m_20096_()) {
            if (this.m_21660_()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.run", EDefaultLoopTypes.LOOP));
            } else {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.walk", EDefaultLoopTypes.LOOP));
            }
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.fly", EDefaultLoopTypes.LOOP));
         }
      } else if (this.shouldStand()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.idle", EDefaultLoopTypes.LOOP));
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.idle_fly", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.fire_ball_right", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.fire_ball_left", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.fire_ball_massive", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.fire_wall", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 5) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.flare_circle", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 6) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.burst", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 7 || this.getMiscAnimation() == 8) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.summon", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(IfritCloneEntity.class, EntityDataSerializers.f_135028_);
      FLYING = SynchedEntityData.m_135353_(IfritCloneEntity.class, EntityDataSerializers.f_135035_);
      FLARE_POS = SynchedEntityData.m_135353_(IfritCloneEntity.class, EntityDataSerializers.f_135038_);
      FLARE_ID = SynchedEntityData.m_135353_(IfritCloneEntity.class, EntityDataSerializers.f_135028_);
   }

   class IfritMoveControl extends MoveControl {
      private final Mob parentEntity = IfritCloneEntity.this;

      public IfritMoveControl() {
         super(IfritCloneEntity.this);
      }

      public void m_8126_() {
         if (IfritCloneEntity.this.getMiscAnimation() < 5) {
            if (this.f_24981_ == Operation.MOVE_TO) {
               Vec3 vector3d = new Vec3(this.f_24975_ - this.parentEntity.m_20185_(), this.f_24976_ - this.parentEntity.m_20186_(), this.f_24977_ - this.parentEntity.m_20189_());
               double d0 = vector3d.m_82553_();
               double width = this.parentEntity.m_20191_().m_82309_();
               Vec3 vector3d1 = vector3d.m_82490_(this.f_24978_ * 0.05D / d0);
               this.parentEntity.m_20256_(this.parentEntity.m_20184_().m_82549_(vector3d1).m_82490_(0.95D).m_82520_(0.0D, -0.01D, 0.0D));
               if (d0 < width) {
                  this.f_24981_ = Operation.WAIT;
               } else if (d0 >= width) {
                  float yaw = -((float)Mth.m_14136_(vector3d1.f_82479_, vector3d1.f_82481_)) * 57.295776F;
                  this.parentEntity.m_146922_(Mth.m_14148_(this.parentEntity.m_146908_(), yaw, 8.0F));
               }
            }

         }
      }
   }

   static class IfritAttackGoal extends Goal {
      private final IfritCloneEntity ifrit;
      private Vec3 startOrbitFrom;
      private int orbitTime;
      private int maxOrbitTime;
      private int attackCooldown;

      public IfritAttackGoal(IfritCloneEntity entity) {
         this.m_7021_(EnumSet.of(Flag.MOVE));
         this.ifrit = entity;
      }

      public boolean m_8036_() {
         if (this.ifrit.m_21827_()) {
            return false;
         } else if (this.ifrit.usingMeleeWeapon()) {
            return false;
         } else {
            LivingEntity target = this.ifrit.m_5448_();
            if (target != null && target.m_6084_()) {
               this.startOrbitFrom = target.m_146892_();
               return true;
            } else {
               return false;
            }
         }
      }

      public void m_8037_() {
         LivingEntity target = this.ifrit.m_5448_();
         if (target != null && target.m_6084_()) {
            this.ifrit.setFlying(true);
            if (this.orbitTime < this.maxOrbitTime) {
               if (this.ifrit.getMiscAnimation() == 0) {
                  ++this.orbitTime;
                  --this.attackCooldown;
               }

               float zoomIn = 1.0F - (float)this.orbitTime / (float)this.maxOrbitTime;
               Vec3 orbitPos = this.orbitAroundPos(15.0F).m_82520_(0.0D, (double)(4.0F + zoomIn * 3.0F), 0.0D);
               this.ifrit.m_21573_().m_26519_(orbitPos.f_82479_, orbitPos.f_82480_, orbitPos.f_82481_, 1.2D);
               if (this.isTimeToAttack()) {
                  this.resetAttackCooldown();
                  this.ifrit.setMiscAnimation(this.randomAttack((double)this.ifrit.m_20270_(target)));
                  this.ifrit.m_7618_(Anchor.EYES, target.m_146892_());
               }
            } else {
               this.orbitTime = 0;
            }

         }
      }

      public void m_8056_() {
         this.orbitTime = 0;
         this.maxOrbitTime = 100 + this.ifrit.m_217043_().m_188503_(80);
         this.ifrit.m_21561_(true);
         this.attackCooldown = 0;
      }

      public Vec3 orbitAroundPos(float circleDistance) {
         float angle = 3.0F * (float)(Math.toRadians((double)this.orbitTime) * 3.0D);
         double extraX = (double)(circleDistance * Mth.m_14031_(angle));
         double extraZ = (double)(circleDistance * Mth.m_14089_(angle));
         return this.startOrbitFrom.m_82520_(extraX, 0.0D, extraZ);
      }

      private void resetAttackCooldown() {
         this.attackCooldown = this.m_183277_(30);
      }

      private boolean isTimeToAttack() {
         return this.attackCooldown <= 0;
      }

      private int randomAttack(double distance) {
         if (distance < 10.0D && (double)this.ifrit.f_19796_.m_188501_() <= 0.2D) {
            return 6;
         } else {
            if (distance < 15.0D) {
               if ((double)this.ifrit.f_19796_.m_188501_() <= 0.05D) {
                  return 4;
               }

               if ((double)this.ifrit.f_19796_.m_188501_() <= 0.1D) {
                  return 5;
               }
            }

            if ((double)this.ifrit.f_19796_.m_188501_() <= 0.4D) {
               return 3;
            } else {
               return this.ifrit.m_217043_().m_188499_() ? 1 : 2;
            }
         }
      }
   }

   static class FollowIfritGoal extends Goal {
      private final IfritCloneEntity clone;
      @Nullable
      private IfritEntity ifrit;
      private final double speedModifier;
      private int timeToRecalcPath;

      public FollowIfritGoal(IfritCloneEntity ifritClone, double pSpeedModifier) {
         this.clone = ifritClone;
         this.speedModifier = pSpeedModifier;
      }

      private boolean hasTarget() {
         if (this.clone.m_5448_() == null) {
            return false;
         } else if (this.clone.m_5448_() instanceof SalamanderEntity) {
            return false;
         } else if (this.clone.m_5448_() instanceof IfritCloneEntity) {
            return false;
         } else {
            return !(this.clone.m_5448_() instanceof IfritEntity);
         }
      }

      public boolean m_8036_() {
         if (this.clone.m_21824_()) {
            return false;
         } else if (!this.clone.m_21523_() && !this.clone.m_20159_()) {
            List<? extends IfritEntity> list = this.clone.f_19853_.m_45976_(IfritEntity.class, this.clone.m_20191_().m_82377_(32.0D, 16.0D, 32.0D));
            IfritEntity lord = null;
            double d0 = Double.MAX_VALUE;
            Iterator var5 = list.iterator();

            while(var5.hasNext()) {
               IfritEntity parent = (IfritEntity)var5.next();
               double distance = this.clone.m_20280_(parent);
               if (distance <= d0) {
                  d0 = distance;
                  lord = parent;
               }
            }

            if (lord == null) {
               return false;
            } else if (this.hasTarget()) {
               return false;
            } else if (d0 > 256.0D) {
               this.ifrit = lord;
               return true;
            } else {
               return false;
            }
         } else {
            return false;
         }
      }

      public boolean m_8045_() {
         if (this.clone.m_21824_()) {
            return false;
         } else if (!this.clone.m_21523_() && !this.clone.m_20159_()) {
            if (this.hasTarget()) {
               return false;
            } else if (this.ifrit == null) {
               return false;
            } else if (!this.ifrit.m_6084_()) {
               return false;
            } else {
               double d0 = this.clone.m_20280_(this.ifrit);
               return d0 >= 49.0D && d0 <= 256.0D;
            }
         } else {
            return false;
         }
      }

      public void m_8056_() {
         this.timeToRecalcPath = 0;
         if (this.clone.m_5803_()) {
            this.clone.setSleeping(false);
         }

      }

      public void m_8041_() {
         this.ifrit = null;
         this.clone.m_21573_().m_26573_();
      }

      public void m_8037_() {
         if (--this.timeToRecalcPath <= 0) {
            if (this.ifrit != null) {
               if (this.ifrit.m_5448_() != null && !this.hasTarget() && this.ifrit.m_5448_() != this.clone && this.ifrit.m_5448_().m_20270_(this.clone) <= 30.0F) {
                  this.clone.m_6710_(this.ifrit.m_5448_());
               }

               this.timeToRecalcPath = this.m_183277_(10);
               this.clone.m_21573_().m_5624_(this.ifrit, this.speedModifier);
            }
         }
      }
   }

   public class WalkGoal extends Goal {
      protected final IfritCloneEntity entity;
      protected double x;
      protected double y;
      protected double z;
      private boolean flightTarget = false;

      public WalkGoal(IfritCloneEntity entity) {
         this.m_7021_(EnumSet.of(Flag.MOVE));
         this.entity = entity;
      }

      public boolean m_8036_() {
         if (!this.entity.m_20160_() && (this.entity.m_5448_() == null || !this.entity.m_5448_().m_6084_()) && !this.entity.m_20159_() && !this.entity.m_21827_()) {
            if (this.entity.m_217043_().m_188503_(30) != 0 && !this.entity.m_29443_()) {
               return false;
            } else {
               if (this.entity.m_20096_()) {
                  this.flightTarget = IfritCloneEntity.this.f_19796_.m_188499_();
               } else {
                  this.flightTarget = IfritCloneEntity.this.f_19796_.m_188503_(5) > 0 && this.entity.timeFlying < 200;
               }

               Vec3 position = this.getPosition();
               if (position == null) {
                  return false;
               } else {
                  this.x = position.f_82479_;
                  this.y = position.f_82480_;
                  this.z = position.f_82481_;
                  return true;
               }
            }
         } else {
            return false;
         }
      }

      public void m_8037_() {
         if (this.flightTarget) {
            this.entity.m_21566_().m_6849_(this.x, this.y, this.z, 1.0D);
         } else {
            this.entity.m_21573_().m_26519_(this.x, this.y, this.z, 1.0D);
            if (IfritCloneEntity.this.m_29443_() && this.entity.f_19861_) {
               this.entity.setFlying(false);
            }
         }

         if (IfritCloneEntity.this.m_29443_() && this.entity.f_19861_ && this.entity.timeFlying > 10) {
            this.entity.setFlying(false);
         }

      }

      @Nullable
      protected Vec3 getPosition() {
         Vec3 vector3d = this.entity.m_20182_();
         if (IfritCloneEntity.this.isOverWater(this.entity)) {
            this.flightTarget = true;
         }

         Vec3 pos = Vec3.m_82512_(this.entity.getWanderPos());
         double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
         if (this.entity.isWandering() && this.entity.m_20238_(pos) >= distance * distance) {
            return pos;
         } else if (this.flightTarget) {
            return this.entity.timeFlying < 50 ? IfritCloneEntity.this.getBlockInViewAway(this.entity, vector3d, 0.0F) : IfritCloneEntity.this.getBlockGrounding(this.entity, vector3d);
         } else {
            return LandRandomPos.m_148488_(this.entity, 10, 7);
         }
      }

      public boolean m_8045_() {
         if (this.entity.m_21827_()) {
            return false;
         } else if (this.flightTarget) {
            return this.entity.m_29443_() && this.entity.m_20275_(this.x, this.y, this.z) > 2.0D;
         } else {
            return !this.entity.m_21573_().m_26571_() && !this.entity.m_20160_();
         }
      }

      public void m_8056_() {
         if (this.flightTarget) {
            this.entity.setFlying(true);
            this.entity.m_21566_().m_6849_(this.x, this.y, this.z, 1.0D);
         } else {
            this.entity.m_21573_().m_26519_(this.x, this.y, this.z, 1.0D);
         }

      }

      public void m_8041_() {
         this.entity.m_21573_().m_26573_();
         super.m_8041_();
      }
   }
}
