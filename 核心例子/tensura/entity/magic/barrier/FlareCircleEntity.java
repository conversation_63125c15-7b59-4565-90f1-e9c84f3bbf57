package com.github.manasmods.tensura.entity.magic.barrier;

import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.entity.IfritEntity;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.network.PacketDistributor;

public class FlareCircleEntity extends BarrierEntity {
   public FlareCircleEntity(Level level, LivingEntity entity) {
      this((EntityType)TensuraEntityTypes.FLARE_CIRCLE.get(), level);
      this.m_5602_(entity);
   }

   public FlareCircleEntity(EntityType<? extends FlareCircleEntity> entityType, Level level) {
      super(entityType, level);
   }

   public boolean canWalkThrough() {
      return this.m_37282_() instanceof IfritEntity;
   }

   public boolean blockBuilding() {
      return false;
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      return false;
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.m_5776_()) {
         if (this.getMpCost() != 0.0D) {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return this;
            }), new RequestFxSpawningPacket(new ResourceLocation("tensura:flare_circle_circle"), this.m_19879_(), 0.0D, 0.5D, 0.0D, true));
         }

         TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
            return this;
         }), new RequestFxSpawningPacket(new ResourceLocation("tensura:flare_circle_flare"), this.m_19879_(), 0.0D, 5.5D, 0.0D, true));
      }
   }

   public void applyEffect(LivingEntity entity) {
      if (!this.isAlly(entity)) {
         entity.m_20254_(5);
         entity.m_6469_(this.m_37282_() == null ? DamageSource.f_19307_ : TensuraDamageSources.burn(this.m_37282_()), this.getDamage());
         if (this.shouldConsumeAir(entity)) {
            entity.m_20301_(entity.m_20146_() - 30);
            if (entity.m_20146_() == -20) {
               entity.m_20301_(0);
               entity.m_6469_(TensuraDamageSources.SUFFOCATE, 2.0F);
            }
         }

      }
   }

   private boolean isAlly(LivingEntity target) {
      Entity owner = this.m_37282_();
      if (target == owner) {
         return true;
      } else {
         if (owner instanceof LivingEntity) {
            LivingEntity entity = (LivingEntity)owner;
            if (target.m_7307_(entity)) {
               return true;
            }

            if (owner instanceof IfritEntity) {
               IfritEntity ifrit = (IfritEntity)owner;
               return !ifrit.shouldAttack(target);
            }
         }

         return false;
      }
   }

   private boolean shouldConsumeAir(LivingEntity entity) {
      if (TensuraEPCapability.isMajin(entity)) {
         return false;
      } else {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (player.m_7500_()) {
               return false;
            }

            if (RaceHelper.isSpiritualLifeForm(player)) {
               return false;
            }
         }

         return !entity.m_5833_();
      }
   }
}
