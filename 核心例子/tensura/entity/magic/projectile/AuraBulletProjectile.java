package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Optional;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;

public class AuraBulletProjectile extends TensuraProjectile {
   private static final EntityDataAccessor<Integer> COLOR_ID;

   public AuraBulletProjectile(EntityType<? extends AuraBulletProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public AuraBulletProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.AURA_BULLET.get(), levelIn);
      this.m_5602_(shooter);
   }

   protected void dealDamage(Entity target) {
      if (this.damage > 0.0F) {
         target.f_19802_ = 0;
         target.m_6469_(TensuraDamageSources.auraBullet(this, this.m_37282_(), this.getSkill(), this.getApCost()), this.damage);
      }

   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(COLOR_ID, 0);
   }

   public void m_7380_(CompoundTag pCompound) {
      super.m_7380_(pCompound);
      pCompound.m_128405_("Color", this.getColor());
   }

   public void m_7378_(CompoundTag pCompound) {
      super.m_7378_(pCompound);
      this.f_19804_.m_135381_(COLOR_ID, pCompound.m_128451_("Color"));
   }

   public int getColor() {
      return (Integer)this.f_19804_.m_135370_(COLOR_ID);
   }

   public AuraBulletProjectile.AuraColor getAuraColor() {
      return AuraBulletProjectile.AuraColor.byId((Integer)this.f_19804_.m_135370_(COLOR_ID));
   }

   public void setColor(AuraBulletProjectile.AuraColor color) {
      this.f_19804_.m_135381_(COLOR_ID, color.getId() & 255);
   }

   public AuraBulletProjectile.AuraColor getColorBySize(float size) {
      if ((double)size <= 2.5D) {
         return AuraBulletProjectile.AuraColor.BLUE;
      } else if ((double)size <= 4.5D) {
         return AuraBulletProjectile.AuraColor.PURPLE;
      } else if ((double)size <= 6.5D) {
         return AuraBulletProjectile.AuraColor.YELLOW;
      } else {
         return (double)size <= 8.5D ? AuraBulletProjectile.AuraColor.PINK : AuraBulletProjectile.AuraColor.RED;
      }
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   protected boolean shouldGrief() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/aura_bullet/" + AuraBulletProjectile.AuraColor.byId(this.getColor()).getName() + "_aura_bullet.png")};
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123747_, x, y, z, 5, 0.1D, 0.1D, 0.1D, 0.25D, true);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123762_, x, y, z, 5, 0.1D, 0.1D, 0.1D, 0.25D, true);
   }

   public void flyingParticles() {
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11892_);
   }

   static {
      COLOR_ID = SynchedEntityData.m_135353_(AuraBulletProjectile.class, EntityDataSerializers.f_135028_);
   }

   public static enum AuraColor {
      BLUE(0, "blue"),
      PURPLE(1, "purple"),
      YELLOW(2, "yellow"),
      PINK(3, "pink"),
      RED(4, "red");

      private static final AuraBulletProjectile.AuraColor[] BY_ID = (AuraBulletProjectile.AuraColor[])Arrays.stream(values()).sorted(Comparator.comparingInt(AuraBulletProjectile.AuraColor::getId)).toArray((x$0) -> {
         return new AuraBulletProjectile.AuraColor[x$0];
      });
      private final int id;
      private final String name;

      private AuraColor(int id, String location) {
         this.id = id;
         this.name = location;
      }

      public static AuraBulletProjectile.AuraColor byId(int id) {
         return BY_ID[id % BY_ID.length];
      }

      public int getId() {
         return this.id;
      }

      public String getName() {
         return this.name;
      }

      // $FF: synthetic method
      private static AuraBulletProjectile.AuraColor[] $values() {
         return new AuraBulletProjectile.AuraColor[]{BLUE, PURPLE, YELLOW, PINK, RED};
      }
   }
}
