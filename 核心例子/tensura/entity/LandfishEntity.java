package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.api.entity.ai.FindWaterGoal;
import com.github.manasmods.tensura.api.entity.ai.LeaveWaterGoal;
import com.github.manasmods.tensura.api.entity.ai.SemiAquaticFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.SemiAquaticRandomSwimmingGoal;
import com.github.manasmods.tensura.api.entity.controller.AquaticMoveController;
import com.github.manasmods.tensura.api.entity.navigator.SemiAquaticNavigator;
import com.github.manasmods.tensura.api.entity.subclass.IFollower;
import com.github.manasmods.tensura.api.entity.subclass.IRanking;
import com.github.manasmods.tensura.api.entity.subclass.ISemiAquatic;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.entity.variant.LandfishVariant;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.UUID;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.BiomeTags;
import net.minecraft.tags.FluidTags;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LightningBolt;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.MoverType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.control.MoveControl;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.TemptGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.navigation.GroundPathNavigation;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.animal.Dolphin;
import net.minecraft.world.entity.monster.Guardian;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.crafting.Ingredient;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.LevelReader;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.pathfinder.BlockPathTypes;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.ForgeEventFactory;
import net.minecraftforge.fluids.FluidType;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class LandfishEntity extends TensuraTamableEntity implements IAnimatable, ISemiAquatic, IFollower, IRanking {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Integer> EVOLUTION_STATE;
   private static final EntityDataAccessor<Integer> DATA_ID_TYPE_VARIANT;
   private static final EntityDataAccessor<Integer> MOISTNESS_LEVEL;
   public int miscAnimationTicks = 0;
   public float prevSwimProgress;
   public float swimProgress;
   private int swimTimer = -1000;
   private boolean isLandNavigator;
   @Nullable
   private UUID lastLightningBoltUUID;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public LandfishEntity(EntityType<? extends LandfishEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.m_21441_(BlockPathTypes.WATER, 0.0F);
      this.m_21441_(BlockPathTypes.WATER_BORDER, 0.0F);
      this.switchNavigator(false);
      this.f_19793_ = 1.0F;
      this.f_21364_ = 5;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 6.0D).m_22268_(Attributes.f_22276_, 16.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22278_, 0.009999999776482582D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(1, new BreedGoal(this, 1.2D));
      this.f_21345_.m_25352_(2, new SemiAquaticFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false, false));
      this.f_21345_.m_25352_(3, new MeleeAttackGoal(this, 1.5D, false));
      this.f_21345_.m_25352_(4, new FindWaterGoal(this));
      this.f_21345_.m_25352_(5, new LeaveWaterGoal(this));
      this.f_21345_.m_25352_(6, new TemptGoal(this, 1.2D, Ingredient.m_204132_(TensuraTags.Items.FISHES), false));
      this.f_21345_.m_25352_(7, new SemiAquaticRandomSwimmingGoal(this, 1.2D, 30));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.WanderAroundPosGoal(this, 60, 1.0D, 10, 7));
      this.f_21345_.m_25352_(9, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(10, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Dolphin.class, false, (Predicate)null));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, Guardian.class, false, (Predicate)null));
      this.f_21346_.m_25352_(7, new ResetUniversalAngerTargetGoal(this, true));
   }

   private void switchNavigator(boolean onLand) {
      if (onLand) {
         this.f_21342_ = new MoveControl(this);
         this.f_21344_ = new GroundPathNavigation(this, this.f_19853_);
         this.isLandNavigator = true;
      } else {
         this.f_21342_ = new AquaticMoveController(this, 1.1F);
         this.f_21344_ = new SemiAquaticNavigator(this, this.f_19853_);
         this.isLandNavigator = false;
      }

   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(EVOLUTION_STATE, 0);
      this.f_19804_.m_135372_(DATA_ID_TYPE_VARIANT, 0);
      this.f_19804_.m_135372_(MOISTNESS_LEVEL, 3000);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128405_("EvoState", this.getCurrentEvolutionState());
      compound.m_128405_("Variant", this.getTypeVariant());
      compound.m_128405_("SwimTimer", this.swimTimer);
      compound.m_128405_("Moistness", this.getMoistnessLevel());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setCurrentEvolutionState(compound.m_128451_("EvoState"));
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, compound.m_128451_("Variant"));
      this.swimTimer = compound.m_128451_("SwimTimer");
      this.setMoistnessLevel(compound.m_128451_("Moistness"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      this.f_19804_.m_135381_(MISC_ANIMATION, animation);
   }

   public LandfishVariant getVariant() {
      return LandfishVariant.byId(this.getTypeVariant() & 255);
   }

   private int getTypeVariant() {
      return (Integer)this.f_19804_.m_135370_(DATA_ID_TYPE_VARIANT);
   }

   public void setVariant(LandfishVariant variant) {
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, variant.getId() & 255);
   }

   public int getMoistnessLevel() {
      return (Integer)this.f_19804_.m_135370_(MOISTNESS_LEVEL);
   }

   public void setMoistnessLevel(int pMoistnessLevel) {
      this.f_19804_.m_135381_(MOISTNESS_LEVEL, pMoistnessLevel);
   }

   public boolean shouldEnterWater() {
      if (this.getMoistnessLevel() <= 300 && !this.m_21827_()) {
         if (this.m_5448_() != null) {
            this.m_6710_((LivingEntity)null);
         }

         return true;
      } else {
         return !this.shouldLeaveWater() && this.swimTimer <= -1000;
      }
   }

   public boolean shouldLeaveWater() {
      if (this.m_5448_() != null && !this.m_5448_().m_20069_()) {
         return true;
      } else {
         return this.swimTimer > 600;
      }
   }

   public boolean shouldStopMoving() {
      return this.m_21827_();
   }

   public int getWaterSearchRange() {
      return 45;
   }

   public boolean isPushedByFluid(FluidType type) {
      return type != ForgeMod.WATER_TYPE.get();
   }

   public boolean m_6914_(LevelReader worldIn) {
      return worldIn.m_45784_(this);
   }

   public boolean canDrownInFluidType(FluidType type) {
      return type != ForgeMod.WATER_TYPE.get();
   }

   public boolean shouldFollow() {
      return !this.m_21827_() && !this.isWandering() && (this.m_5448_() == null || !this.m_5448_().m_6084_()) && this.getMoistnessLevel() >= 300;
   }

   public boolean m_7327_(Entity pEntity) {
      boolean flag = super.m_7327_(pEntity);
      if (flag) {
         if (this.getMiscAnimation() == 0) {
            this.setMiscAnimation(1);
         }

         if (this.m_217043_().m_188503_(4) == 1 && pEntity instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)pEntity;
            switch(this.getVariant()) {
            case ELDER_GUARDIAN:
               target.m_147207_(new MobEffectInstance(MobEffects.f_19599_, 400, 1, false, false, true), this);
               break;
            case PUFFER:
               target.m_147207_(new MobEffectInstance(MobEffects.f_19614_, 100, 1, false, false, true), this);
            }
         }
      }

      return flag;
   }

   public int getCurrentEvolutionState() {
      return (Integer)this.f_19804_.m_135370_(EVOLUTION_STATE);
   }

   public void setCurrentEvolutionState(int state) {
      this.f_19804_.m_135381_(EVOLUTION_STATE, state);
   }

   public void evolve() {
      int current = this.getCurrentEvolutionState();
      if (current < this.getMaxEvolutionState()) {
         this.setCurrentEvolutionState(current + 1);
         if (this.getVariant().equals(LandfishVariant.GUARDIAN)) {
            this.setVariant(LandfishVariant.ELDER_GUARDIAN);
         }
      }

   }

   public void m_8038_(ServerLevel pLevel, LightningBolt pLightning) {
      if (this.getVariant().equals(LandfishVariant.GUARDIAN)) {
         UUID uuid = pLightning.m_20148_();
         if (!uuid.equals(this.lastLightningBoltUUID)) {
            this.setVariant(LandfishVariant.ELDER_GUARDIAN);
            AttributeInstance health = this.m_21051_(Attributes.f_22276_);
            if (health != null) {
               health.m_22100_(health.m_22115_() + 10.0D);
            }

            this.m_5634_(this.m_21233_());
            this.lastLightningBoltUUID = uuid;
            this.m_5496_(SoundEvents.f_11880_, 2.0F, 1.0F);
         }

      }
   }

   private boolean needWater() {
      if (this.getVariant().getId() >= 5) {
         return false;
      } else {
         return !this.m_21023_(MobEffects.f_19608_);
      }
   }

   public void m_8119_() {
      super.m_8119_();
      this.prevSwimProgress = this.swimProgress;
      boolean ground = !this.m_20072_();
      if (!ground && this.isLandNavigator) {
         this.switchNavigator(false);
      }

      if (ground && !this.isLandNavigator) {
         this.switchNavigator(true);
      }

      if (ground && this.swimProgress > 0.0F) {
         --this.swimProgress;
      }

      if (!ground && this.swimProgress < 5.0F) {
         ++this.swimProgress;
      }

      if (!this.f_19853_.f_46443_) {
         if (this.m_20071_()) {
            this.setMoistnessLevel(3000);
         } else if (this.needWater()) {
            this.setMoistnessLevel(this.getMoistnessLevel() - 1);
            if (this.getMoistnessLevel() <= 0) {
               this.m_6469_(DamageSource.f_19324_, 1.0F);
            }
         }

         if (this.m_20069_()) {
            ++this.swimTimer;
         } else {
            --this.swimTimer;
         }
      } else if (this.m_20069_() && this.m_20184_().m_82556_() > 0.01D) {
         Vec3 vec3 = this.m_20252_(0.0F);
         float f = Mth.m_14089_(this.m_146908_() * 0.017453292F) * 0.3F;
         float f1 = Mth.m_14031_(this.m_146908_() * 0.017453292F) * 0.3F;
         float f2 = 1.2F - this.f_19796_.m_188501_() * 0.7F;

         for(int i = 0; i < 2; ++i) {
            this.f_19853_.m_7106_(ParticleTypes.f_123776_, this.m_20185_() - vec3.f_82479_ * (double)f2 + (double)f, this.m_20186_() - vec3.f_82480_, this.m_20189_() - vec3.f_82481_ * (double)f2 + (double)f1, 0.0D, 0.0D, 0.0D);
            this.f_19853_.m_7106_(ParticleTypes.f_123776_, this.m_20185_() - vec3.f_82479_ * (double)f2 - (double)f, this.m_20186_() - vec3.f_82480_, this.m_20189_() - vec3.f_82481_ * (double)f2 - (double)f1, 0.0D, 0.0D, 0.0D);
         }
      }

      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   private int getAnimationTick(int miscAnimation) {
      byte var10000;
      switch(miscAnimation) {
      case 1:
         var10000 = 9;
         break;
      case 2:
         var10000 = 31;
         break;
      default:
         var10000 = 10;
      }

      return var10000;
   }

   public void m_7023_(Vec3 travelVector) {
      if (this.m_6142_() && this.m_20069_()) {
         this.m_19920_(this.m_6113_(), travelVector);
         this.m_6478_(MoverType.SELF, this.m_20184_());
         this.m_20256_(this.m_20184_().m_82490_(0.9D));
         if (this.m_5448_() == null) {
            this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.005D, 0.0D));
         }
      } else {
         super.m_7023_(travelVector);
      }

   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_11758_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_11761_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_11759_;
   }

   public void m_27563_(ServerLevel pLevel, Animal pMate) {
      super.m_27563_(pLevel, pMate);
      this.setMiscAnimation(3);
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      LandfishEntity fish = (LandfishEntity)((EntityType)TensuraEntityTypes.LANDFISH.get()).m_20615_(pLevel);
      if (fish == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            fish.m_21816_(uuid);
            fish.m_7105_(true);
         }

         int i = this.f_19796_.m_188503_(9);
         LandfishVariant variant;
         if (i < 3) {
            variant = this.getVariant();
         } else if (i < 6 && pOtherParent instanceof LandfishEntity) {
            LandfishEntity entity = (LandfishEntity)pOtherParent;
            variant = entity.getVariant();
         } else {
            variant = this.randomVariant(pLevel);
         }

         fish.setVariant(variant);
         return fish;
      }
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_204117_(TensuraTags.Items.COOKED_FISHES);
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         if (this.m_6898_(itemstack)) {
            if (this.m_21223_() < this.m_21233_()) {
               if (!player.m_7500_()) {
                  itemstack.m_41774_(1);
               }

               this.m_5634_(3.0F);
               this.setMiscAnimation(2);
               this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
               return InteractionResult.SUCCESS;
            }

            int i = this.m_146764_();
            if (!this.f_19853_.m_5776_() && i == 0 && this.m_5957_()) {
               this.m_142075_(player, hand, itemstack);
               this.m_27595_(player);
               this.setMiscAnimation(1);
               return InteractionResult.SUCCESS;
            }

            if (this.m_6162_()) {
               this.m_142075_(player, hand, itemstack);
               this.m_146740_(m_216967_(-i), true);
               this.setMiscAnimation(2);
               this.m_9236_().m_6269_(player, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
               return InteractionResult.m_19078_(this.f_19853_.f_46443_);
            }
         } else if (itemstack.m_150930_(Items.f_42447_)) {
            if (!player.m_7500_()) {
               itemstack.m_41774_(1);
            }

            if (this.m_21223_() < this.m_21233_()) {
               this.m_5634_(2.0F);
            }

            this.setMoistnessLevel(3000);
            this.setMiscAnimation(3);
            if (this.f_19796_.m_188503_(5) == 0) {
               this.m_9236_().m_6269_((Player)null, this, SoundEvents.f_12018_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            } else {
               ItemStack stack = new ItemStack(Items.f_42446_);
               if (!player.m_36356_(stack)) {
                  player.m_36176_(stack, false);
               }

               this.m_9236_().m_6269_((Player)null, this, SoundEvents.f_11911_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            }

            return InteractionResult.SUCCESS;
         }

         if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_() || itemstack.m_204117_(TensuraTags.Items.FISHES);
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else if (this.m_21824_()) {
            if (!super.m_6071_(player, hand).m_19077_() && this.m_21830_(player) && !this.m_6162_()) {
               this.commanding(player);
               return InteractionResult.SUCCESS;
            } else {
               return InteractionResult.PASS;
            }
         } else if (itemstack.m_204117_(TensuraTags.Items.FISHES)) {
            if (!player.m_7500_()) {
               itemstack.m_41774_(1);
            }

            if (this.f_19796_.m_188503_(3) == 0 && !ForgeEventFactory.onAnimalTame(this, player)) {
               this.setMiscAnimation(3);
               this.m_21828_(player);
               this.f_21344_.m_26573_();
               this.m_6710_((LivingEntity)null);
               this.m_21839_(true);
               this.f_19853_.m_7605_(this, (byte)7);
            } else {
               this.f_19853_.m_7605_(this, (byte)6);
            }

            return InteractionResult.SUCCESS;
         } else {
            return super.m_6071_(player, hand);
         }
      }
   }

   public static boolean checkLandfishSpawnRules(EntityType<LandfishEntity> fish, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      int i = pLevel.m_5736_();
      int j = i - 13;
      return pPos.m_123342_() >= j && pPos.m_123342_() <= i && pLevel.m_6425_(pPos.m_7495_()).m_205070_(FluidTags.f_13131_) && pLevel.m_8055_(pPos.m_7494_()).m_60713_(Blocks.f_49990_);
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.landfishSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      this.setVariant(this.randomVariant(pLevel));
      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   private LandfishVariant randomVariant(ServerLevelAccessor pLevel) {
      if (pLevel.m_204166_(this.m_20183_()).m_203656_(BiomeTags.f_207602_)) {
         return LandfishVariant.byId(this.m_217043_().m_188503_(8));
      } else {
         return pLevel.m_204166_(this.m_20183_()).m_203656_(BiomeTags.f_207603_) ? LandfishVariant.byId(this.m_217043_().m_188503_(7)) : LandfishVariant.byId(this.m_217043_().m_188503_(5));
      }
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (event.isMoving()) {
         if (this.isInFluidType()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.landfish.swim", EDefaultLoopTypes.LOOP));
         } else if (this.m_21223_() < this.m_21233_() / 4.0F) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.landfish.walk_hurt", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.landfish.walk", EDefaultLoopTypes.LOOP));
         }
      } else if (this.isInFluidType()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.landfish.idle_swim", EDefaultLoopTypes.LOOP));
      } else if (this.m_21825_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.landfish.stay", EDefaultLoopTypes.LOOP));
      } else if (this.m_21223_() < this.m_21233_() / 4.0F) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.landfish.idle_hurt", EDefaultLoopTypes.LOOP));
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.landfish.idle", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.landfish.slap", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2 && !this.m_21825_()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.landfish.eat", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.landfish.jump", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(LandfishEntity.class, EntityDataSerializers.f_135028_);
      EVOLUTION_STATE = SynchedEntityData.m_135353_(LandfishEntity.class, EntityDataSerializers.f_135028_);
      DATA_ID_TYPE_VARIANT = SynchedEntityData.m_135353_(LandfishEntity.class, EntityDataSerializers.f_135028_);
      MOISTNESS_LEVEL = SynchedEntityData.m_135353_(LandfishEntity.class, EntityDataSerializers.f_135028_);
   }
}
