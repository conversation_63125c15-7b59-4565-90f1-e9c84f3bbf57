package com.github.manasmods.tensura.entity.projectile;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.HellCaterpillarEntity;
import com.github.manasmods.tensura.entity.HellMothEntity;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.item.custom.WebCartridgeItem;
import com.github.manasmods.tensura.item.custom.WebGunItem;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.protocol.game.ClientboundAddEntityPacket;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;

public class WebBulletProjectile extends AbstractArrow {
   private static final EntityDataAccessor<Boolean> SLINGER;
   private static final EntityDataAccessor<ItemStack> SOURCE_ITEM;
   private static final EntityDataAccessor<ItemStack> AMMO;

   public WebBulletProjectile(EntityType<? extends WebBulletProjectile> type, Level level) {
      super(type, level);
   }

   public WebBulletProjectile(Level pLevel, double pX, double pY, double pZ) {
      super((EntityType)TensuraEntityTypes.WEB_BULLET.get(), pLevel);
      this.m_6034_(pX, pY, pZ);
   }

   public WebBulletProjectile(Level worldIn, LivingEntity shooter, boolean right, ItemStack sourceItem, ItemStack ammo) {
      this((EntityType)TensuraEntityTypes.WEB_BULLET.get(), worldIn);
      this.m_5602_(shooter);
      this.setSourceItem(sourceItem.m_41777_());
      this.setAmmo(ammo);
      float rot = shooter.f_20885_ + (float)(right ? 60 : -60);
      this.m_6034_(shooter.m_20185_() - (double)shooter.m_20205_() * 0.5D * (double)Mth.m_14031_(rot * 0.017453292F), shooter.m_20188_() - 0.20000000298023224D, shooter.m_20189_() + (double)shooter.m_20205_() * 0.5D * (double)Mth.m_14089_(rot * 0.017453292F));
   }

   public WebBulletProjectile(Level worldIn, LivingEntity shooter, boolean right, ItemStack ammo) {
      this((EntityType)TensuraEntityTypes.WEB_BULLET.get(), worldIn);
      this.m_5602_(shooter);
      this.setAmmo(ammo);
      float rot = shooter.f_20885_ + (float)(right ? 60 : -60);
      this.m_6034_(shooter.m_20185_() - (double)shooter.m_20205_() * 0.5D * (double)Mth.m_14031_(rot * 0.017453292F), shooter.m_20188_() - 0.20000000298023224D, shooter.m_20189_() + (double)shooter.m_20205_() * 0.5D * (double)Mth.m_14089_(rot * 0.017453292F));
   }

   public void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(SLINGER, false);
      this.f_19804_.m_135372_(SOURCE_ITEM, ((WebGunItem)TensuraToolItems.WEB_GUN.get()).m_7968_());
      this.f_19804_.m_135372_(AMMO, ((Item)TensuraToolItems.STICKY_WEB_CARTRIDGE.get()).m_7968_());
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128379_("Slinger", this.isSlinger());
      compound.m_128365_("SourceItem", this.getSourceItem().m_41739_(new CompoundTag()));
      compound.m_128365_("Ammo", this.getAmmo().m_41739_(new CompoundTag()));
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.setSlinger(compound.m_128471_("Slinger"));
      if (compound.m_128425_("SourceItem", 10)) {
         this.setSourceItem(ItemStack.m_41712_(compound.m_128469_("SourceItem")));
      }

      if (compound.m_128425_("Ammo", 10)) {
         this.setAmmo(ItemStack.m_41712_(compound.m_128469_("Ammo")));
      }

   }

   public ItemStack getSourceItem() {
      return (ItemStack)this.f_19804_.m_135370_(SOURCE_ITEM);
   }

   public void setSourceItem(ItemStack pStack) {
      this.f_19804_.m_135381_(SOURCE_ITEM, pStack);
   }

   public ItemStack getAmmo() {
      return (ItemStack)this.f_19804_.m_135370_(AMMO);
   }

   public void setAmmo(ItemStack pStack) {
      this.f_19804_.m_135381_(AMMO, pStack);
   }

   public boolean isSlinger() {
      return (Boolean)this.f_19804_.m_135370_(SLINGER);
   }

   public void setSlinger(boolean saddled) {
      this.f_19804_.m_135381_(SLINGER, saddled);
   }

   public ItemStack m_7941_() {
      return ItemStack.f_41583_;
   }

   public boolean isInGround() {
      return this.f_36703_;
   }

   public void m_6686_(double x, double y, double z, float velocity, float inaccuracy) {
      Vec3 vector3d = (new Vec3(x, y, z)).m_82541_().m_82520_(this.f_19796_.m_188583_() * 0.007499999832361937D * (double)inaccuracy, this.f_19796_.m_188583_() * 0.007499999832361937D * (double)inaccuracy, this.f_19796_.m_188583_() * 0.007499999832361937D * (double)inaccuracy).m_82490_((double)velocity);
      this.m_20256_(vector3d);
      float f = Mth.m_14116_((float)(vector3d.f_82479_ * vector3d.f_82479_ + vector3d.f_82481_ * vector3d.f_82481_));
      this.m_146922_((float)(Mth.m_14136_(vector3d.f_82479_, vector3d.f_82481_) * 57.2957763671875D));
      this.m_146926_((float)(Mth.m_14136_(vector3d.f_82480_, (double)f) * 57.2957763671875D));
      this.f_19859_ = this.m_146908_();
      this.f_19860_ = this.m_146909_();
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.m_20077_()) {
         this.m_142687_(RemovalReason.DISCARDED);
      }

      if (this.isSlinger()) {
         Entity entity = this.m_37282_();
         if (entity instanceof LivingEntity) {
            LivingEntity owner = (LivingEntity)entity;
            if (!entity.m_213877_() && entity.m_6084_()) {
               Entity vehicle = this.m_20202_();
               double f;
               double d0;
               double d1;
               double d2;
               if (vehicle != null) {
                  f = (double)vehicle.m_20270_(owner);
                  if (f > 30.0D) {
                     this.m_146870_();
                  }

                  if (this.canPull(vehicle, owner)) {
                     if (owner.m_20186_() + 5.0D > vehicle.m_20186_()) {
                        vehicle.m_183634_();
                     }

                     if (f > 10.0D || owner.m_6144_() && f > 2.0D) {
                        d0 = (entity.m_20185_() - vehicle.m_20185_()) / f;
                        d1 = (entity.m_20186_() - vehicle.m_20186_()) / f;
                        d2 = (entity.m_20189_() - vehicle.m_20189_()) / f;
                        vehicle.m_20256_(vehicle.m_20184_().m_82520_(Math.copySign(d0 * d0 * 0.2D, d0), Math.copySign(d1 * d1 * 0.2D, d1), Math.copySign(d2 * d2 * 0.2D, d2)));
                        vehicle.f_19864_ = true;
                        return;
                     }
                  } else {
                     if (owner.m_20186_() <= vehicle.m_20186_()) {
                        owner.m_183634_();
                     }

                     if (f > 10.0D || owner.m_6144_() && f > 2.0D) {
                        d0 = (vehicle.m_20185_() - owner.m_20185_()) / f;
                        d1 = (vehicle.m_20186_() - owner.m_20186_()) / f;
                        d2 = (vehicle.m_20189_() - owner.m_20189_()) / f;
                        owner.m_20256_(owner.m_20184_().m_82520_(Math.copySign(d0 * d0 * 0.2D, d0), Math.copySign(d1 * d1 * 0.2D, d1), Math.copySign(d2 * d2 * 0.2D, d2)));
                        owner.f_19864_ = true;
                        return;
                     }
                  }

                  return;
               } else {
                  if (this.isInGround()) {
                     f = (double)owner.m_20270_(this);
                     if (owner.m_6144_()) {
                        d0 = (this.m_20185_() - entity.m_20185_()) / f;
                        d1 = (this.m_20186_() - entity.m_20186_()) / f;
                        d2 = (this.m_20189_() - entity.m_20189_()) / f;
                        entity.m_20256_(entity.m_20184_().m_82520_(Math.copySign(d0 * d0 * 0.25D, d0), Math.copySign(d1 * d1 * 0.25D, d1), Math.copySign(d2 * d2 * 0.25D, d2)));
                        entity.f_19864_ = true;
                     }

                     if (f > 50.0D || f <= 1.0D && owner.m_20186_() > this.m_20186_()) {
                        this.m_146870_();
                     }

                     if (owner.m_20186_() < this.m_20186_() - 5.0D) {
                        owner.m_183634_();
                     }

                     return;
                  }

                  return;
               }
            }
         }

         this.m_146870_();
      }
   }

   private boolean canPull(Entity entity, LivingEntity owner) {
      if (owner instanceof Player) {
         Player player = (Player)owner;
         if (player.m_7500_()) {
            return true;
         }
      }

      if (entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         if (TensuraEPCapability.getEP(living) > TensuraEPCapability.getEP(owner)) {
            return false;
         }
      }

      float entitySize = Math.max(entity.m_20206_(), entity.m_20205_());
      float ownerSize = Math.max(owner.m_20206_(), owner.m_20205_());
      return entitySize < ownerSize * 3.0F;
   }

   public void m_6123_(Player pEntity) {
   }

   public void m_141965_(ClientboundAddEntityPacket pPacket) {
      super.m_141965_(pPacket);
      double d0 = pPacket.m_131503_();
      double d1 = pPacket.m_131504_();
      double d2 = pPacket.m_131505_();

      for(int i = 0; i < 12; ++i) {
         double d3 = 0.4D + 0.1D * (double)i;
         this.f_19853_.m_7106_(ParticleTypes.f_123764_, this.m_20185_(), this.m_20186_(), this.m_20189_(), d0 * d3, d1, d2 * d3);
      }

      this.m_20334_(d0, d1, d2);
   }

   protected void m_5790_(EntityHitResult pResult) {
      Entity entity = pResult.m_82443_();
      LivingEntity livingEntity;
      if (this.isSlinger() && entity instanceof LivingEntity) {
         livingEntity = (LivingEntity)entity;
         DamageSourceHelper.markHurt(livingEntity, this.m_37282_());
         this.f_36703_ = true;
         this.m_7998_(entity, true);
      } else {
         if (entity instanceof LivingEntity) {
            livingEntity = (LivingEntity)entity;
            DamageSourceHelper.markHurt(livingEntity, this.m_37282_());
            Item var5 = this.getAmmo().m_41720_();
            if (!(var5 instanceof WebCartridgeItem)) {
               return;
            }

            WebCartridgeItem webCartridgeItem = (WebCartridgeItem)var5;
            if (livingEntity.m_20206_() <= 3.0F || livingEntity.m_20205_() <= 3.0F) {
               boolean var10000;
               label31: {
                  Entity var7 = this.m_37282_();
                  if (var7 instanceof LivingEntity) {
                     LivingEntity owner = (LivingEntity)var7;
                     if (TensuraEPCapability.getEP(livingEntity) > TensuraEPCapability.getEP(owner)) {
                        var10000 = true;
                        break label31;
                     }
                  }

                  var10000 = false;
               }

               boolean epHigh = var10000;
               if (!epHigh) {
                  livingEntity.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.WEBBED.get(), webCartridgeItem.getWebbedDuration(), 0, false, false, true), this.m_37282_());
               }

               if ((double)livingEntity.m_217043_().m_188501_() <= webCartridgeItem.getSilenceChance()) {
                  livingEntity.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.SILENCE.get(), webCartridgeItem.getSilenceDuration(), 0, false, false, true), this.m_37282_());
               }
            }
         }

         this.m_146870_();
         this.m_5496_(SoundEvents.f_12639_, 0.5F, 0.75F);
      }
   }

   protected void m_8060_(BlockHitResult pResult) {
      super.m_8060_(pResult);
      if (!this.f_19853_.f_46443_) {
         if (this.shouldPlaceWeb()) {
            this.placeWeb(this);
            this.m_142687_(RemovalReason.DISCARDED);
            this.m_5496_(SoundEvents.f_12639_, 0.1F, 0.1F);
         } else if (!this.isSlinger()) {
            this.m_142687_(RemovalReason.DISCARDED);
         }
      } else {
         this.m_9236_().m_7106_(ParticleTypes.f_123790_, this.m_20185_(), this.m_20186_(), this.m_20189_(), 0.0D, 0.05D, 0.0D);
      }

   }

   private boolean shouldPlaceWeb() {
      if (this.m_37282_() instanceof HellCaterpillarEntity) {
         return false;
      } else if (this.m_37282_() instanceof HellMothEntity) {
         return false;
      } else {
         return !this.isSlinger();
      }
   }

   protected void placeWeb(Entity entity) {
      if (TensuraGameRules.canSkillGrief(this.f_19853_)) {
         Item var3 = this.getAmmo().m_41720_();
         if (var3 instanceof WebCartridgeItem) {
            WebCartridgeItem webCartridgeItem = (WebCartridgeItem)var3;
            int yPos = Mth.m_14107_(entity.m_20186_()) - 1;
            int xPos = Mth.m_14107_(entity.m_20185_());
            int zPos = Mth.m_14107_(entity.m_20189_());
            boolean destroyBlock = false;
            boolean placeWeb = false;
            boolean webbedStones = false;

            for(int j = -1; j <= 1; ++j) {
               for(int k = -1; k <= 1; ++k) {
                  for(int i = -1; i <= 2; ++i) {
                     int newYPos = yPos + i;
                     int newXPos = xPos;
                     int newZPos = zPos;
                     if (i == 1 || i == 0) {
                        newXPos = xPos + j;
                        newZPos = zPos + k;
                     }

                     BlockPos blockpos = new BlockPos(newXPos, newYPos, newZPos);
                     BlockState blockstate = this.f_19853_.m_8055_(blockpos);
                     if (k != j && k != -j || j == 0) {
                        SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(this.m_37282_(), (ManasSkillInstance)null, blockpos);
                        if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
                           BlockState webbedStone;
                           if (blockstate.m_60795_() || blockstate.m_204336_(TensuraTags.Blocks.WEB_REPLACABLE)) {
                              webbedStone = webCartridgeItem.getWebBlock().m_49966_();
                              destroyBlock = this.f_19853_.m_46953_(blockpos, true, this) || destroyBlock;
                              placeWeb = this.f_19853_.m_46597_(blockpos, webbedStone) || placeWeb;
                              this.f_19853_.m_186460_(blockpos, webbedStone.m_60734_(), webCartridgeItem.getDissolvingDuration());
                           }

                           if (this.getAmmo().m_150930_((Item)TensuraToolItems.WEB_CARTRIDGE.get()) && blockstate.m_204336_(TensuraTags.Blocks.WEBBED_AVAILABLE)) {
                              webbedStone = ((Block)TensuraBlocks.WEBBED_COBBLESTONE.get()).m_49966_();
                              if (blockstate.m_60713_(Blocks.f_50222_)) {
                                 webbedStone = ((Block)TensuraBlocks.WEBBED_STONE_BRICKS.get()).m_49966_();
                              }

                              webbedStones = this.f_19853_.m_46597_(blockpos, webbedStone) || webbedStones;
                           }

                           MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(this.m_37282_(), (ManasSkillInstance)null, blockpos));
                        }
                     }
                  }
               }
            }

            if (destroyBlock) {
               this.f_19853_.m_5594_((Player)null, this.m_20183_(), SoundEvents.f_12555_, SoundSource.PLAYERS, 0.2F, 1.0F);
               if (this.m_37282_() != null) {
                  this.f_19853_.m_142346_(this.m_37282_(), GameEvent.f_157794_, this.m_20183_());
               }
            }

            if (placeWeb || webbedStones) {
               this.f_19853_.m_5594_((Player)null, this.m_20183_(), SoundEvents.f_12555_, SoundSource.PLAYERS, 0.2F, 1.0F);
               if (this.m_37282_() != null) {
                  this.f_19853_.m_142346_(this.m_37282_(), GameEvent.f_157792_, this.m_20183_());
               }
            }

         }
      }
   }

   static {
      SLINGER = SynchedEntityData.m_135353_(WebBulletProjectile.class, EntityDataSerializers.f_135035_);
      SOURCE_ITEM = SynchedEntityData.m_135353_(WebBulletProjectile.class, EntityDataSerializers.f_135033_);
      AMMO = SynchedEntityData.m_135353_(WebBulletProjectile.class, EntityDataSerializers.f_135033_);
   }
}
