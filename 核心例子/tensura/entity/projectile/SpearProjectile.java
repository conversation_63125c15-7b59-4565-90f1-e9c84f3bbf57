package com.github.manasmods.tensura.entity.projectile;

import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.enchantment.EngravingEnchantment;
import com.github.manasmods.tensura.item.templates.custom.SimpleSpearItem;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Objects;
import javax.annotation.Nullable;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.entity.projectile.AbstractArrow.Pickup;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.Vec3;

public class SpearProjectile extends AbstractArrow {
   protected static final EntityDataAccessor<Integer> LOYALTY_LEVEL;
   private static final EntityDataAccessor<ItemStack> SOURCE_ITEM;
   private boolean finishPiercing;
   private int piercingEntity = 0;
   public int clientSideReturnKunaiTickCount;

   public SpearProjectile(EntityType<? extends SpearProjectile> type, Level level) {
      super(type, level);
   }

   public SpearProjectile(Level worldIn, LivingEntity shooter, ItemStack pStack, boolean right) {
      super((EntityType)TensuraEntityTypes.SPEAR.get(), shooter, worldIn);
      this.setSourceItem(pStack.m_41777_());
      float rot = shooter.f_20885_ + (float)(right ? 60 : -60);
      this.m_6034_(shooter.m_20185_() - (double)shooter.m_20205_() * 0.5D * (double)Mth.m_14031_(rot * 0.017453292F), shooter.m_20188_() - 0.20000000298023224D, shooter.m_20189_() + (double)shooter.m_20205_() * 0.5D * (double)Mth.m_14089_(rot * 0.017453292F));
   }

   public SpearProjectile(Level pLevel, double pX, double pY, double pZ) {
      super((EntityType)TensuraEntityTypes.SPEAR.get(), pX, pY, pZ, pLevel);
   }

   public ItemStack m_7941_() {
      return this.getSourceItem();
   }

   @Nullable
   protected EntityHitResult m_6351_(Vec3 pStartVec, Vec3 pEndVec) {
      return this.finishPiercing ? null : super.m_6351_(pStartVec, pEndVec);
   }

   private boolean isAcceptableReturnOwner() {
      Entity entity = this.m_37282_();
      if (entity == null) {
         return false;
      } else if (!entity.m_6084_()) {
         return false;
      } else {
         return !(entity instanceof ServerPlayer) || !entity.m_5833_();
      }
   }

   public void m_8119_() {
      if (this.f_36704_ > 4) {
         this.finishPiercing = true;
      }

      Entity owner = this.m_37282_();
      int i = this.getLoyaltyLevel();
      if (i > 0 && (this.finishPiercing || this.m_36797_()) && owner != null && this.m_6084_()) {
         if (!this.isAcceptableReturnOwner()) {
            if (!this.f_19853_.f_46443_ && this.f_36705_ == Pickup.ALLOWED) {
               this.m_5552_(this.m_7941_(), 0.1F);
            }

            this.m_146870_();
         } else {
            this.m_36790_(true);
            Vec3 vec3 = owner.m_146892_().m_82546_(this.m_20182_());
            this.m_20343_(this.m_20185_(), this.m_20186_() + vec3.f_82480_ * 0.015D * (double)i, this.m_20189_());
            if (this.f_19853_.f_46443_) {
               this.f_19791_ = this.m_20186_();
            }

            double d0 = 0.05D * (double)i;
            this.m_20256_(this.m_20184_().m_82490_(0.95D).m_82549_(vec3.m_82541_().m_82490_(d0)));
            if (this.clientSideReturnKunaiTickCount == 0) {
               this.m_5496_(SoundEvents.f_12516_, 10.0F, 1.0F);
            }

            ++this.clientSideReturnKunaiTickCount;
         }
      }

      super.m_8119_();
   }

   protected void m_5790_(EntityHitResult pResult) {
      Entity entity = pResult.m_82443_();
      Item var4 = this.getSourceItem().m_41720_();
      if (var4 instanceof SimpleSpearItem) {
         SimpleSpearItem spear = (SimpleSpearItem)var4;
         float projectileDamage = (float)(this.m_36789_() + (double)spear.m_43314_().m_6631_() + 2.0D);
         if (entity instanceof LivingEntity) {
            LivingEntity livingentity = (LivingEntity)entity;
            projectileDamage += EnchantmentHelper.m_44833_(this.getSourceItem(), livingentity.m_6336_());
         }

         Entity ownerEntity = this.m_37282_();
         DamageSource damagesource = TensuraDamageSources.spear(this, (Entity)Objects.requireNonNullElse(ownerEntity, this));
         if (entity.m_6469_(damagesource, projectileDamage)) {
            if (entity.m_6095_() == EntityType.f_20566_) {
               return;
            }

            if (entity instanceof LivingEntity) {
               LivingEntity livingEntity = (LivingEntity)entity;
               if (ownerEntity instanceof LivingEntity) {
                  LivingEntity owner = (LivingEntity)ownerEntity;
                  EnchantmentHelper.m_44823_(livingEntity, ownerEntity);
                  EnchantmentHelper.m_44896_(owner, livingEntity);
                  EngravingEnchantment.doAdditionalAttack(this.getSourceItem(), owner, entity, projectileDamage);
                  pResult.m_82443_().m_20254_(3 * this.getSourceItem().getEnchantmentLevel(Enchantments.f_44981_));
               }

               this.m_7761_(livingEntity);
            }
         }

         ++this.piercingEntity;
         if (this.piercingEntity >= 1 + this.getSourceItem().getEnchantmentLevel(Enchantments.f_44961_)) {
            this.finishPiercing = true;
            this.m_20256_(this.m_20184_().m_82542_(-0.01D, -0.1D, -0.01D));
            float f1 = 1.0F;
            this.m_5496_(SoundEvents.f_12514_, f1, 1.0F);
         }

      }
   }

   protected boolean m_142470_(Player pPlayer) {
      return super.m_142470_(pPlayer) || this.m_36797_() && this.m_150171_(pPlayer) && pPlayer.m_150109_().m_36054_(this.m_7941_());
   }

   protected SoundEvent m_7239_() {
      return SoundEvents.f_12515_;
   }

   public void m_6123_(Player pEntity) {
      if (this.m_150171_(pEntity) || this.m_37282_() == null) {
         super.m_6123_(pEntity);
      }

   }

   public ItemStack getSourceItem() {
      return (ItemStack)this.f_19804_.m_135370_(SOURCE_ITEM);
   }

   public void setSourceItem(ItemStack pStack) {
      this.f_19804_.m_135381_(SOURCE_ITEM, pStack);
      this.setLoyaltyLevel(EnchantmentHelper.m_44928_(pStack));
   }

   public int getLoyaltyLevel() {
      return (Integer)this.f_19804_.m_135370_(LOYALTY_LEVEL);
   }

   public void setLoyaltyLevel(int level) {
      this.f_19804_.m_135381_(LOYALTY_LEVEL, level);
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(LOYALTY_LEVEL, 0);
      this.f_19804_.m_135372_(SOURCE_ITEM, ((Item)TensuraToolItems.DIAMOND_SPEAR.get()).m_7968_());
   }

   public void m_7378_(CompoundTag pCompound) {
      super.m_7378_(pCompound);
      if (pCompound.m_128425_("Source Item", 10)) {
         this.setSourceItem(ItemStack.m_41712_(pCompound.m_128469_("Source Item")));
      }

      this.setLoyaltyLevel(pCompound.m_128451_("Loyalty"));
      this.finishPiercing = pCompound.m_128471_("DealtDamage");
   }

   public void m_7380_(CompoundTag pCompound) {
      super.m_7380_(pCompound);
      pCompound.m_128379_("DealtDamage", this.finishPiercing);
      pCompound.m_128365_("Source Item", this.getSourceItem().m_41739_(new CompoundTag()));
      pCompound.m_128405_("Loyalty", this.getLoyaltyLevel());
   }

   public void m_6901_() {
      int i = this.getLoyaltyLevel();
      if ((this.f_36705_ != Pickup.ALLOWED || i <= 0) && ++this.f_36697_ >= (Integer)TensuraConfig.INSTANCE.entitiesConfig.spearDespawn.get()) {
         this.m_146870_();
      }

   }

   protected float m_6882_() {
      return 0.79F + (float)this.getSourceItem().getEnchantmentLevel(Enchantments.f_44956_) * 0.04F;
   }

   static {
      LOYALTY_LEVEL = SynchedEntityData.m_135353_(SpearProjectile.class, EntityDataSerializers.f_135028_);
      SOURCE_ITEM = SynchedEntityData.m_135353_(SpearProjectile.class, EntityDataSerializers.f_135033_);
   }
}
