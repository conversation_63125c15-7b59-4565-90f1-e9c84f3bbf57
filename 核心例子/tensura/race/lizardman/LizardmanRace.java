package com.github.manasmods.tensura.race.lizardman;

import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.Tags.Biomes;
import org.jetbrains.annotations.Nullable;

public class LizardmanRace extends Race {
   public LizardmanRace() {
      super(Race.Difficulty.INTERMEDIATE);
   }

   public LizardmanRace(Race.Difficulty difficulty) {
      super(difficulty);
   }

   public double getBaseHealth() {
      return 24.0D;
   }

   public float getPlayerSize() {
      return 2.0F;
   }

   public double getBaseAttackDamage() {
      return 1.5D;
   }

   public double getBaseAttackSpeed() {
      return 4.0D;
   }

   public double getKnockbackResistance() {
      return 0.2D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer();
   }

   public double getMovementSpeed() {
      return 0.1D;
   }

   public double getSprintSpeed() {
      return 0.13D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(600.0D, 800.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(100.0D, 200.0D);
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      List<TensuraSkill> list = new ArrayList();
      list.add((TensuraSkill)IntrinsicSkills.SCALE_ARMOR.get());
      return list;
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.DRAGONEWT.get();
   }

   @Nullable
   public Race getAwakeningEvolution(Player player) {
      return (Race)TensuraRaces.TRUE_DRAGONEWT.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return (Race)TensuraRaces.DRAGONEWT.get();
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.DRAGONEWT.get());
      return list;
   }

   public void raceTick(Player player) {
      if (player.f_19853_.m_46469_().m_46207_(TensuraGameRules.HARDCORE_RACE)) {
         if (!player.m_7500_()) {
            if (!player.m_5833_()) {
               if (player.f_19853_.m_204166_(player.m_20097_()).m_203656_(Biomes.IS_COLD_OVERWORLD)) {
                  player.m_7292_(new MobEffectInstance(MobEffects.f_19599_, 40, 0, false, false, false));
                  player.m_7292_(new MobEffectInstance(MobEffects.f_19613_, 40, 1, false, false, false));
                  player.m_7292_(new MobEffectInstance(MobEffects.f_19597_, 40, 1, false, false, false));
               }
            }
         }
      }
   }
}
