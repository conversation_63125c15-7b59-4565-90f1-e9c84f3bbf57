package com.github.manasmods.tensura.race.slime;

import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.api.race.AdvancedHitbox;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public class SlimeRace extends Race implements AdvancedHitbox {
   public static final int jumpChargeTicks = 60;

   public SlimeRace() {
      super(Race.Difficulty.EXTREME);
   }

   public SlimeRace(Race.Difficulty difficulty) {
      super(difficulty);
   }

   public double getBaseHealth() {
      return 10.0D;
   }

   public float getPlayerSize() {
      return 0.5F;
   }

   public double getBaseAttackDamage() {
      return 0.3D;
   }

   public double getBaseAttackSpeed() {
      return 4.0D;
   }

   public double getKnockbackResistance() {
      return 0.0D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer(1.0D);
   }

   public double getMovementSpeed() {
      return 0.07D;
   }

   public double getSprintSpeed() {
      return 0.091D;
   }

   public double getAdditionalSpiritualHealth() {
      return 20.0D;
   }

   public double getSpiritualHealthMultiplier() {
      return 2.5D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(200.0D, 500.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(200.0D, 500.0D);
   }

   public float getHitboxWidthModifier() {
      return 4.0F;
   }

   public float getHitboxHeightModifier() {
      return 1.0F;
   }

   public boolean isMajin() {
      return true;
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      List<TensuraSkill> list = new ArrayList();
      list.add((TensuraSkill)IntrinsicSkills.ABSORB_DISSOLVE.get());
      list.add((TensuraSkill)CommonSkills.SELF_REGENERATION.get());
      return list;
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.DEMON_SLIME.get();
   }

   @Nullable
   public Race getAwakeningEvolution(Player player) {
      return (Race)TensuraRaces.DEMON_SLIME.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return null;
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.METAL_SLIME.get());
      list.add((Race)TensuraRaces.DEMON_SLIME.get());
      return list;
   }

   public void raceTick(Player player) {
      if (player.f_19853_.m_46469_().m_46207_(TensuraGameRules.HARDCORE_RACE)) {
         if (!player.m_21023_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get())) {
            player.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.TRUE_BLINDNESS.get(), 40, 0, false, false, false));
            player.m_7292_(new MobEffectInstance(MobEffects.f_19610_, 40, 0, false, false, false));
         }
      }
   }
}
