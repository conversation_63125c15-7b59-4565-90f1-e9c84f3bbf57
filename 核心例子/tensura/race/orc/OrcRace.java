package com.github.manasmods.tensura.race.orc;

import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public class OrcRace extends Race {
   public OrcRace() {
      super(Race.Difficulty.INTERMEDIATE);
   }

   public OrcRace(Race.Difficulty difficulty) {
      super(difficulty);
   }

   public double getBaseHealth() {
      return 28.0D;
   }

   public float getPlayerSize() {
      return 3.0F;
   }

   public double getBaseAttackDamage() {
      return 1.0D;
   }

   public double getBaseAttackSpeed() {
      return 3.9D;
   }

   public double getKnockbackResistance() {
      return 0.5D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer();
   }

   public double getMovementSpeed() {
      return 0.08D;
   }

   public double getSprintSpeed() {
      return 0.104D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(400.0D, 600.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(50.0D, 100.0D);
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.HIGH_ORC.get();
   }

   @Nullable
   public Race getAwakeningEvolution(Player player) {
      return (Race)TensuraRaces.SPIRIT_BOAR.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return (Race)TensuraRaces.HIGH_ORC.get();
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.HIGH_ORC.get());
      return list;
   }
}
