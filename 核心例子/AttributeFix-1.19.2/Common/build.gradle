plugins {
    id 'org.spongepowered.gradle.vanilla' version '0.2.1-SNAPSHOT'
}

apply from: '../gradle/property_helper.gradle'

archivesBaseName = "${mod_name}-Common-${minecraft_version}"

minecraft {
    version(minecraft_version)
}

dependencies {
     compileOnly group:'org.spongepowered', name:'mixin', version:'0.8.5'
}

processResources {

    def buildProps = project.properties.clone()

    filesMatching(['pack.mcmeta']) {

        expand buildProps
    }
}

// -- MAVEN PUBLISHING --
project.publishing {
    
    publications {

        mavenJava(MavenPublication) {
        
            artifactId project.archivesBaseName

            // Base mod archive.
            artifact jar
            
            // Adds the sources as an artifact.
            artifact project.sourcesJar {
                classifier 'sources'
            }

            // Adds the javadocs as an artifact.
            artifact project.javadocJar {
                classifier 'javadoc'
            }
        }
    }

    repositories {
    
        maven {
        
            // Sets maven credentials if they are provided. This is generally
            // only used for external/remote uploads.
            if (project.hasProperty('mavenUsername') && project.hasProperty('mavenPassword')) {
            
                credentials {
                
                    username findProperty('mavenUsername')
                    password findProperty('mavenPassword')
                }
            }

            url getDefaultString('mavenURL', 'undefined', true)
        }
    }
}