/*
 This module can inject build properties from a JSON file. Each property in the
 JSON file will be mapped to a build property using the key of that property. 
 Property keys ending with _comment will be skipped.
 
 If a secretFile property exists and points to a valid JSON file that file will
 be automatically loaded. You can manually load a file using the loadProperties
 method.
*/
import groovy.json.JsonSlurper

// Auto detects a secret file and injects it.
if (project.rootProject.hasProperty('secretFile')) {

    project.logger.lifecycle('Automatically loading properties from the secretFile')
    final def secretsFile = project.rootProject.file(project.rootProject.getProperty('secretFile'))
    
    if (secretsFile.exists() && secretsFile.name.endsWith('.json')) {
    
        loadProperties(secretsFile)
    }
}

// Loads properties using a specified json file.
def loadProperties(propertyFile) {

    if (propertyFile.exists()) {
    
        propertyFile.withReader {
        
            Map propMap = new JsonSlurper().parse it
            
            for (entry in propMap) {
            
                // Filter entries that use _comment in the key.
                if (!entry.key.endsWith('_comment')) {
                
                    project.ext.set(entry.key, entry.value)
                }
            }
            
            project.logger.lifecycle('Successfully loaded ' + propMap.size() + ' properties')
            propMap.clear()
        }
    }
    
    else {
    
        project.logger.warn('The property file ' + propertyFile.getName() + ' could not be loaded. It does not exist.')
    }
}

// Allows other scripts to use these methods.
ext {

    loadProperties = this.&loadProperties
}
